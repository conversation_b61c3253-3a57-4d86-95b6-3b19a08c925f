server {
    listen 80;
    server_name localhost;

    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    access_log /dev/stdout;
    error_log /dev/stderr;

    location ~ ^/automation/remove_(users|user/idm)$ {
        proxy_pass http://bot_remove:8010;
    }

    location / {
        proxy_pass http://bot_consult:8010;
    }
}

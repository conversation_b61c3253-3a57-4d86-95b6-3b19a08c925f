<!-- omit in toc -->
# Desarrollo
Este artículo describe la estructura y arquitectura del Bot, asi como la explicación del funcionamiento de cada componente de la API del Bot.

A continuación se presenta la guía para conocer de qué manera se puede contribuir en el desarrollo de este proyecto.

<!-- omit in toc -->
## Tabla de contenidos
- [Estructura del proyecto](#estructura-del-proyecto)
    - [Archivos y directorios](#archivos-y-directorios)
    - [Arquitectura técnica](#arquitectura-técnica)
- [Guía de modificaciones del Bot](#guía-de-modificaciones-del-bot)
    - [Estructura del Bot](#estructura-del-bot)
    - [Flujo de ejecución del Bot](#flujo-de-ejecución-del-bot)
        - [Explicación del Bot](#explicación-del-bot)
        - [Diagrama del funcionamiento del Bot](#diagrama-del-funcionamiento-del-bot)
    - [Script principal](#script-principal)
    - [Inyección de dependencias](#inyección-de-dependencias)
        - [Explicación de la inyección de dependencias](#explicación-de-la-inyección-de-dependencias)
        - [Módulo de inyecciones](#módulo-de-inyecciones)
        - [Hacer que un elemento pueda ser inyectable](#hacer-que-un-elemento-pueda-ser-inyectable)
    - [Configuración](#configuración)
    - [Middlewares](#middlewares)
    - [DTOs](#dtos)
        - [Definición](#definición)
        - [Uso](#uso)
    - [Controladores](#controladores)
    - [Validadores](#validadores)
    - [Modelos](#modelos)
    - [Servicios](#servicios)
    - [Módulos](#módulos)
        - [Definición](#definición-1)
        - [Método de retiro de usuarios](#método-de-retiro-de-usuarios)
        - [Tipo de retorno](#tipo-de-retorno)
        - [Uso](#uso-1)
    - [Traducción con I18N](#traducción-con-i18n)
        - [Uso](#uso-2)
        - [Agregar nuevo lenguaje](#agregar-nuevo-lenguaje)
        - [Mensajes con parámetros](#mensajes-con-parámetros)
    - [Librerías](#librerías)
        - [Definición](#definición-2)
        - [Descripción de las librerías](#descripción-de-las-librerías)
    - [Librería `http`](#librería-http)
    - [Librería `pdf`](#librería-pdf)
        - [Ejemplo de uso](#ejemplo-de-uso)
        - [Métodos](#métodos)
        - [PDF de ejemplo](#pdf-de-ejemplo)
    - [Librería `validator`](#librería-validator)
        - [Ejemplo de uso](#ejemplo-de-uso-1)
        - [Descripción de los validadores](#descripción-de-los-validadores)
        - [Descripción de los operadores](#descripción-de-los-operadores)

## Estructura del proyecto

### Archivos y directorios
El proyecto está estructurado de la siguiente manera

| Fichero/Directorio | Descripción |
| ---- | ---- |
| __bot__ | API Rest hecha en el framework **Starlette** de Python que ejecuta las funciones principales del Bot. |
| __cert__ | Contiene los archivos **cert.pem** y **key.pem** correspondientes al certificado SSL. |
| __commands__ | Contiene herramientas de línea de comandos como el [**env_encrypter.py**](/commands/env_encrypter.py) y el binario ubicado en la carpeta **/commands/bin**. |
| __db__ | En caso de que el proyecto se ejecute en modo desarrollo, se creará esta carpeta como volumen del docker para almacenar los datos. |
| __docs__ | Contiene los archivos de la documentación del proyecto. |
| __frontend__ | Interfaz gráfica para ejecutar la automatización, administrar la base de datos, ver reportes, entre otros. |
| __.env__ | Archivo de configuración. |
| __.env.example__ | Archivo de configuración de ejemplo (plantilla y documentación para la creación del archivo .env). |
| __.gitignore__ | Archivo para excluir ficheros y directorios del repositorio. |
| __docker-compose.yml__ | Servicios de docker de producción. |
| __docker-compose.dev.yml__ | Servicios de docker de desarrollo. |
| __README.md__ | Archivo principal de la documentación. |

### Arquitectura técnica
![Arquitectura](/docs/screenshots/development_architecture.png)

## Guía de modificaciones del Bot

Esta guía explica como funciona cada parte del código del Bot y de que manera se conectan para formar un servidor ASGI de automatización.

En general, todas las clases y métodos del código tienen su respectiva documentación mediante el uso de **docstring**. Si desea conocer para qué sirve y cómo funciona un módulo, servicio, librería, controlador, función, etc., solo tiene que dirigirse al archivo y leer los docstring o la documentación de cada clase y/o método.

Si está usando Visual Studio Code, puede colocar el cursor sobre la variable, objeto, clase o método que desee inspeccionar y le saldrá una ventana con la preview del docstring:

![Docstring](/docs/screenshots/development_docstring.png)

### Estructura del Bot
| Fichero/Directorio | Descripción |
| ---- | ---- |
| __controllers__ | Controladores (endpoints). |
| __dtos__ | DTOs o Data Transfer Objects, son las clases que representan la estructura del cuerpo de las peticiones y respuestas en los endpoints. |
| __i18n__ | Módulo de traducción. |
| __lib__ | Librerías. |
| __middlewares__ | Middlewares personalizados. |
| __models__ | Modelos de la base de datos. |
| __modules__ | Contiene los módulos de la funcionalidad del Bot como los módulos de las automatizaciones. |
| __services__ | Servicios, intermediarios entre los endpoints y los modelos o módulos. |
| __validators__ | Validadores para revisar que los datos de las peticiones sean correctos. |
| __api.http__ | Cliente REST para probar los endpoints del Bot desde Visual Studio Code. |
| __Dockerfile__ | Imagen de Docker para producción. |
| __Dockerfile.dev__ | Imagen de Docker para desarrollo. |
| __main.py__ | Script principal. |
| __mypy.ini__ | Configuración del linter de python "mypy". |
| __requirements.txt__ | Paquetes de Python requeridos. |

### Flujo de ejecución del Bot

#### Explicación del Bot
Las peticiones entrantes llegan a los **controladores** (endpoints) y estos envían los datos a los **validadores** (estos son opcionales) para revisar si los datos que vienen en la petición son correctos.

Si los datos son correctos (en caso de que se esté implementando un validador), los **controladores** envían los datos a los **servicios**.

Los **servicios** son los intermediarios de los **controladores** y los **módulos** o **modelos**. Preparan los datos que necesitan los **módulos** o realizan las consultas a la base de datos mediante los **modelos**.

Tanto los **módulos** como los **modelos** retornan una respuesta a los **servicios**, los cuales a su vez, retornan la respuesta a los **controladores**, y estos últimos retornan la respuesta al cliente que hizo la petición.

#### Diagrama del funcionamiento del Bot
Lo anterior se explica con el siguiente diagrama:

![Diagrama del flujo de ejecución del Bot](/docs/screenshots/development_bot_execution_flow.png)

### Script principal
Es el archivo **main.py** el cual contiene el código necesario para iniciar una instancia de Starlette (un servidor ASGI).

Entre las modificaciones importantes a realizar en este archivo se encuentra la importación de los modelos para ser inicializados por la librería Beanie y el motor de la base de datos (MongoDB):

```python
# bot/main.py

from models import CustomModel1, CustomModel2, ... # <----- Importar modelos
...

async def on_startup():
    ...
    await init_beanie(
        database=client[database],
        document_models=[
            CustomModel1, #  <----- Inicializar modelos
            CustomModel2,
        ])
```

Otra posible modificación puede ser el cambio de puerto en caso de que se requiera:

```python
# bot/main.py

if __name__ == '__main__':
    uvicorn.run(
        app='main:app',
        host='0.0.0.0',
        port=8010, # <----------- Puerto de la API
        log_level='info',
        reload=False if Config.production else True
    )
```

### Inyección de dependencias

#### Explicación de la inyección de dependencias
La API implementa inyección de dependencias mediante los métodos "**singleton**", "**scoped**" y "**transient**".

| Método | Descripción |
| ---- | ---- |
| Singleton | Se crea una única instancia para todas las peticiones |
| Scoped | Se crea una instancia por cada petición |
| Transient | Se crea una instancia nueva siempre |

Los módulos, servicios y validadores deben ser inyectados por temas de optimización y buenas prácticas. Inyectar o no las librerías depende de la necesidad.

#### Módulo de inyecciones
Esta inyección se hace en el archivo [**injectables.py**](/bot/lib/injectables.py) en el cual se importa el elemento a inyectar (módulo, servicio, validador, etc.) y se inyecta usando los métodos anteriormente mencionados:

```python
# bot/lib/injectables.py

from services import CustomService # <-------

class Injectables:

    @staticmethod
    def execute():
        Injectables.config()
        ...
        Injectables.services()
        ...
        injectable.end()

    @staticmethod
    def services():
        injectable.add_scoped(CustomService) # <-------
```

#### Hacer que un elemento pueda ser inyectable
Todos los elementos que vayan a ser inyectados deben heredar de la clase "**BaseInjectable**", por ejemplo:
```python
from lib.base_injectable import BaseInjectable

class MiInyectable(BaseInjectable)
    pass
```

La forma en la que se suelen inyectar los elementos es la siguiente:

| Elemento | Método de inyección |
| ---- | ---- |
| Módulos de configuración | Singleton |
| Conexión a bases de datos | Singleton |
| Módulos de automatización | Scoped |
| Módulos de conexiones | Singleton o Scoped |
| Módulos de traducción (I18N) | Transient |
| Servicios | Scoped |
| Validadores | Scoped |
| Librerías | Transient |

### Configuración
En el módulo [**config**](/bot/lib/config.py) se leen las variables de entorno
del archivo **.env**. También, se instancia un objeto de la clase **Health**,
el cual es utilizado para verificar el estado de la API.

La lectura de las variables de entorno se realiza así:
```python
# bot/lib/config.py

from starlette.config import Config

_config = Config()

PROD = _config('PROD', cast=bool, default=False)
```

El primer parámetro es el nombre de la variable de entorno, el segundo es el
tipo de dato (debe ser una clase o una función que retorne el tipo de dato deseado)
y el tercero es el valor por defecto.

Utilice la clase **Secret** como el tipo de dato para variables sensibles como
contraseñas, tokens, etc., así:

```python
# bot/lib/config.py

from starlette.config import Config
from starlette.datastructures import Secret

_config = Config()

DATABASE_PASSWORD = _config('DATABASE_PASSWORD', cast=Secret, default='')
```

Si no especifica un valor por defecto, se entiende que la variable de entorno es
obligatoria, es decir, si no se especifica un valor por defecto y la variable de
entorno no está definida, se lanzará una excepción.

### Middlewares
Para agregar un middleware siga los siguientes pasos:

1. Diríjase a la carpeta "**middlewares**" y cree un archivo con una clase basada en _BaseMiddleware_:

```python
# bot/middlewares/custom_middleware.py

from lib.base_middleware import BaseMiddleware


class CustomMiddleware(BaseMiddleware):

    async def before_dispatch(self) -> None:
        ...

    async def after_dispatch(self) -> None:
        ...
```

Para conocer como funciona un middleware, consulte la [documentación de Starlette acerca de los middlewares](https://www.starlette.io/middleware/) y/o revise la documentación de la clase [BaseMiddleware](/bot/lib/base_middleware.py).

2. Agregue el middleware al **\_\_init\_\_.py** de la carpeta middlewares:
```python
# bot/middlewares/__init__.py

...
from .custom_middleware import CustomMiddleware # <-------
```

3. Importe el middleware en el script principal (**main.py**) y agréguelo a la lista de middlewares:

```python
# bot/main.py

from middlewares import ..., CustomMiddleware # <-------

...

middleware = [
    Middleware(
        TrustedHostMiddleware,
        allowed_hosts=['*'],
    ),
    ...
    Middleware(LanguageMiddleware),
    Middleware(CustomMiddleware) # <-------
]
```

### DTOs

#### Definición
Los Data Transfer Objects, son las clases que representan la estructura del cuerpo de las peticiones y respuestas en los endpoints, es decir, representan un JSON como un objeto y viceversa.

Se crean en la carpeta "**dtos**" y hay tres tipos, genéricos, de petición y de respuesta.

1. DTO genérico:
```python
# bot/dtos/custom_dto.py

from lib.base_dto import BaseDto # <---- Clase base de los DTOs

class CustomDto(BaseDto):
    fullName: str
    lastName: str
    ...
```

Suelen usarse para almacenar datos de un objeto con mayor facilidad y seguridad. Aunque también sirven para almacenar campos de tipo objeto en un [modelo](#modelos).

2. DTO de petición:
```python
# bot/dtos/custom_dto.py

from lib.base_dto import BaseRequestDto # <---- DTO de petición

class CustomRequestDto(BaseRequestDto):
    fullName: str
    lastName: str
    ...
```

El cuerpo de la petición debería ser el siguiente:
```json
{
    "fullName": "María",
    "lastName": "Hernandez"
}
```

3. DTO de respuesta:
```python
# bot/dtos/custom_dto.py

from lib.base_dto import BaseResponseDto # <---- DTO de respuesta

class CustomResponseDto(BaseResponseDto):
    id: PydanticObjectId
    fullName: str
    lastName: str
    ...
    updatedAt: datetime.datetime
    createdAt: datetime.datetime
```

El cuerpo de la respuesta debería ser el siguiente:
```json
{
    "id": "Bf987892347234",
    "fullName": "María",
    "lastName": "Hernandez",
    ...
    "updatedAt": "2024-05-30 14:20:40.324243",
    "createdAt": "2024-05-30 14:20:40.324243"
}
```

#### Uso

Los DTOs poseen 3 funciones importantes:

1. **to_dict**: Convierte un DTO a diccionario.
   ```python
    class MyDTO(BaseDto):
        name: str
        surname: str
        age: int

    dto = MyDTO(name='Foo', surname='Bar', age=22)
    > dto.to_dict()
   ```

    El resultado sería el siguiente:
    ```python
    {'name': 'Foo', 'surname': 'Bar', 'age': 22}
    ```

2. **from_orm**: Convierte una instancia de un modelo a DTO.
    ```python

    # Modelo
    class MyModel(BaseModel):
        name: str
        surname: str
        age: int

    # DTO
    class MyDTO(BaseDto):
        name: str
        surname: str
        age: int

    model = await MyModel.find_one(MyModel.name == 'Foo')
    dto = MyDTO.from_orm(model)
    > dto.name
    ```

    Resultado:
    ```python
    Foo
    ```

3. **from_orm_many**: Convierte una lista de instancias de un modelo a una lista de DTOs.
    ```python

    # Modelo
    class MyModel(BaseModel):
        name: str
        surname: str
        age: int

    # DTO
    class MyDTO(BaseDto):
        name: str
        surname: str
        age: int

    models = await MyModel.find().to_list()
    dtos = MyDTO.from_orm_many(models)

    for dto in dtos:
        print(dto.name)
    ```

    Resultado:
    ```python
    Foo
    Bar
    Baz
    ...
    ```

NOTA: La explicación de como se usan los DTOs de petición y de respuesta está en el siguiente apartado que habla sobre los [controladores](#controladores) la cual se encuentra a continuación.

### Controladores
Son los endpoints de la API. Para crear un controlador, siga los siguientes pasos:

1. Si el endpoint espera recibir datos, cree un DTO como se explica [aquí](#dtos).

2. Cree un controlador en la carpeta "**controllers**" como el siguiente:

```python
# bot/controllers/custom_controller.py

from lib.auth import auth
from lib.base_exception import BaseException
from lib.base_controller import BaseController, get, post
...

from dtos.custom_dto import CustomRequestDto, CustomResponseDto # <--- DTOs

class CustomController(BaseController): # <--- Heredar de BaseController

    @auth # <------- El cliente debe estar autenticado
    @post('/create') # <------- La petición es POST a la ruta /create
    async def create(self,
        data: CustomRequestDto, # <----- DTO de petición
        validator: CustomValidador # <---- Inyecta el validador
        # también podría inyectar un servicio u otro elemento
    ) -> DtoResponse[CustomResponseDto] | ErrorResponse:
        error = ErrorValidator()
        try:
            if not await validator.validate(data): # Validar datos

                # Retornar error si los datos no son validos
                return ErrorResponse(validator.error)

            # Ejecutar servicio
            created_data = await validator.service.create(data):

            # Retornar respuesta como DTO
            return DtoResponse[CustomResponseDto](created_data)
        except BaseException as e:
            error.add('common', str(e))
            return ErrorResponse(error, e.code)

    @auth # <------- El cliente debe estar autenticado
    @get('/list') # <------- La petición es GET a la ruta /list
    async def list(self) -> PaginationResponse[CustomResponseDto] | ErrorResponse:
        ...
```

Como puede observar en el ejemplo anterior, el controlador hereda de la clase "**BaseController**" y cada método (endpoint) implementa un decorador que define el método y la ruta del endpoint (POST, GET, DELETE, PUT). Estos se importan con:
```python
from lib.base_controller import BaseController, get, post, delete, put
```

El decorador "**auth**" se usa cuando se requiere que en la petición haya un header Authorization con un token válido, es decir, que el cliente se haya autenticado.
Este se importa con:
```python
from lib.auth import auth
```

Para mayor información sobre la autenticación consulte la [documentación de Starlette sobre el middleware de autenticación](https://www.starlette.io/authentication/).

3. Cuando el controlador esté creado, importe el controlador en el **\_\_init\_\_.py** de la carpeta controllers:
```python
# bot/controllers/__init__.py

...
from .custom_controller import CustomController # <-----
```

### Validadores
Validan los DTOs de los controladores. Se inyectan como se mencionó en la sección de [inyección de dependencias](#inyección-de-dependencias) y se ubican en la carpeta "**validators**".

Su estructura es la siguiente:
```python
# bot/validators/custom_validator.py

class CustomValidator(BaseInjectable, BaseValidator):

    service: CustomService # <----- Servicio (opcional, también podría ser un módulo)

    def __init__(self,

        # Puede inyectar el servicio aquí o en el controlador, es su elección
        service: CustomService,

        # SIEMPRE INYECTE EL I18N, es requerido por la librería de validación
        i18n: I18N
    ) -> None:
        self.service = service
        self.i18n = i18n

    def validator(self,
        custom_dto: CustomRequestDto # <------- DTO
    ) -> DataValidator:
        validator = DataValidator()

        # Agregar validación de "requerido" para el campo "fullName" del dto
        validator.add('fullName', Validator[str](custom_dto.fullName).required())
        ...

        return validator

    async def validate(self,
        custom_dto: CustomRequestDto # <------- DTO
    ) -> bool:

        # No cambie estas líneas de código a no ser que lo necesite
        validator = self.validator(custom_dto)
        if await validator.validate():
            return True
        self.error = validator.get_error(self.i18n)
        return False
```

### Modelos
Representan las colecciones de la base de datos (en este caso, MongoDB). Proporcionan métodos para realizar consultas y operaciones en una colección de la base de datos como insertar, actualizar, listar, eliminar, entre otras.

Se importan en el script principal (**main.py**) como se explicó [en esta sección](#script-principal) y se ubican en la carpeta "**models**".

La estructura base de un modelo es la siguiente:
```python
# bot/models/custom_model.py

from lib.base_model import BaseModel
from lib.base_dto import BaseDto

class User(BaseDto): # <-- Para campos de tipo objeto se hereda la clase BaseDto
    username: str
    document: str

class CustomModel(BaseModel): # <-- Los modelos heredan la clase BaseModel
    text: str
    user: User # <-- Campos de tipo objeto
    state: Literal['done', 'error']
    ...

    # (OPCIONAL) Clase para implementar proyecciones de Mongo
    class ProjectionConfig:

        # Agregar campo "username" extraído del campo "user"
        username = 'user.username'

        # Agregar campo "document" extraído del campo "user"
        document = 'user.document'

    # (OPCIONAL) Clase para implementar traducción al exportar datos
    class TranslationConfig:

        # Traducir nombres de las columnas (sólo para Excel)
        columns = {
            'text': '<mensaje de i18n>',
            'username': '<mensaje de i18n>',
            'document': '<mensaje de i18n>',
            'createdAt': '<mensaje de i18n>',
            'updatedAt': '<mensaje de i18n>'
        }

        # Traducir valores de un campo
        data = {
            'status': {
                'done': '<mensaje de i18n>',
                'error': '<mensaje de i18n>'
            },
        }
```

NOTA: Las clases ProjectionConfig y TranslationConfig son totalmente opcionales.

Al implementar la clase ProjectionConfig, debe usar la librería [Query](/bot/lib/query.py) para hacer uso de esta proyección. Un ejemplo del uso es este:

```python
# Consultar documentos de la colección del modelo "CustomModel"
# cuyo campo isDeleted sea "False".
query = self.__query.create(CustomModel, CustomModel.isDeleted == False)

# Listar documentos
documents = await query.list()
...
```

NOTA: Si está usando el linter **Ruff**, para ocultar la advertencia de comparaciones con valores booleanos (`True` y `False`) con el operador `==`, agregue el siguiente comentario al final de la línea de código donde se encuentra la comparación:

```python
query = self.__query.create(..., CustomModel.isDeleted == False)  # noqa: E712
```

Si desea conocer el funcionamiento de las proyecciones de Mongo [haga click aquí para ir a la documentación de Mongo acerca de las proyecciones](https://www.mongodb.com/docs/manual/reference/operator/projection/positional/).

Si implementa la clase TranslationConfig, debe agregar los mensajes I18N a utilizar para las traducciones como se menciona [en la sección de traducción con I18N](#traducción-con-i18n).

> ## <span style="color:red">¡Importante!</span>
> Por seguridad y buenas prácticas, los controladores (endpoints) no deben interactuar con los modelos, es decir, no deben realizar ningún tipo de operación con ellos.<br>
> Los controladores solo deben recibir peticiones y enviar respuestas, por lo tanto, siempre que necesite retornar una instancia o una lista de instancias de un modelo, utilice las funciones [**from_orm**](#uso) y [**from_orm_many**](#uso) para retornar dichas instancias como un DTO o lista de DTOs como se explicó en la sección sobre [DTOs](#dtos).
> <br><br>

### Servicios

Reciben los datos de los endpoints y se comunican con los modelos para hacer consultas en la base de datos o con los módulos para realizar automatizaciones o algún otro tipo de operación (por ejemplo, exportar datos). Se inyectan como se mencionó [aquí](#inyección-de-dependencias) y se ubican en la carpeta "**services**".

Su estructura es la siguiente:

```python
# bot/services/custom_service.py

from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService

class CustomService(
        BaseInjectable, BaseService): # Los servicios heredan de estas dos clases

    __filter: Filter
    __query: Query
    ...

    def __init__(self,

        # Estas librerías están inyectadas como dependencias
        _filter: Filter,
        query: Query
    ) -> None:
        super().__init__()
        self.__filter = _filter
        self.__query = query

    # Ejemplo de una función para crear un documento en una colección
    async def create(self, custom_dto: CustomRequestDto) -> CustomResponseDto:
        new: Modelo = Modelo(**custom_dto.to_dict())
        return CustomResponseDto.from_orm(await new.create())
    ...
```

### Módulos

#### Definición
Son los RPA o módulos de automatización y la principal funcionalidad del Bot, es decir, los que realizan la labor que haría un humano manualmente. Se inyectan como se mencionó en la sección de [inyección de dependencias](#inyección-de-dependencias) y se ubican en la carpeta "**modules**".

Los módulos deben declarar una función **main** o función principal de ejecución como se muestra en el siguiente ejemplo:
```python
# bot/modules/custom_module.py

from lib.base_injectable import BaseInjectable
from lib.base_module import BaseModule

class CustomModule(
    BaseInjectable, BaseModule): # Los módulos heredan de estas dos clases
    i18n: I18N

    def __init__(self,
        # Casi todos los módulos y librerías
        # inyectan la configuración del servidor
        i18n: I18N
    ) -> None:
        super().__init__()
        self.i18n = i18n

    # Función principal
    def main(self, ...):
        ...
```

Si desea declarar la función principal con un nombre personalizado, debe decorar la función con el decorador **main** así:
```python
# bot/modules/custom_module.py

from lib.base_injectable import BaseInjectable
from lib.base_module import BaseModule # <--- Importar decorador

class CustomModule(BaseInjectable, BaseModule):

    @main # <--- Establecer función como principal
    def run_automation(self, ...):
        ...
```

Es importante tomar en cuenta que:
- Si no declara una función **main**, se lanzará una excepción **MainFunctionNotFoundError**.
- Si declara más de una función **main**, se lanzará una excepción **MultipleMainFunctionsError**.

#### Método de retiro de usuarios

Para la funcionalidad de retiro de usuarios (Bot Retiros), los módulos deben declarar una función `remove_user` así:
```python
# bot/modules/custom_module.py

from lib.base_injectable import BaseInjectable
from lib.base_module import BaseModule

class CustomModule(BaseInjectable, BaseModule):

    def remove_user(self, ...): # Método para retirar usuarios
        ...
```

#### Tipo de retorno

Tanto el método principal `main` como el método de retiro de usuarios `remove_user` deben retornar un objeto
de las clases `ApplicationUserDataResponseDto` y `RemovedUserDataResponseDto` respectivamente. Cada módulo define
una clase personalizada que herede de cada una de las clases mencionadas anteriormente en el archivo que contiene
los [DTOs de automatización](/bot/dtos/automation_dto.py), por ejemplo:
```python
# bot/dtos/automation_dto.py

class CustomModuleResponseDto(ApplicationUserDataResponseDto):

    # Ejemplo de datos que extraerá el método principal del módulo (main)
    active: bool
    locked: bool
    ...


class CustomModuleRemovedUserResponseDto(RemovedUserDataResponseDto):

    # Ejemplo de datos que extraerá el módulo al retirar un usuario (remove_user)
    active: bool
    locked: bool
    message: str
    warning: bool = False
    ...
```
```python
# bot/modules/custom_module.py

from dtos.automation_dto import CustomModuleResponseDto, CustomModuleRemovedUserResponseDto

class CustomModule(BaseInjectable, BaseModule):

    def main(self, ...):
        ...

        return CustomModuleResponseDto(active=True, locked=False, ...)

    def remove_user(self, ...):
        ...

        return CustomModuleRemovedUserResponseDto(
            active=False,
            locked=True,
            message='Usuario retirado correctamente.',
            ...)
```

#### Uso
Entre los servicios, se encuentra el [**automation_service**](/bot/services/automation_service.py) el cual se encarga de ejecutar la consulta del usuario, por lo tanto, cada vez que se agregue un nuevo módulo debe importarlo en este servicio de la siguiente forma:

1. Importe el módulo en el archivo **\_\_init\_\_.py** de la carpeta **modules**:
```python
# bot/modules/custom_module.py

...
from .custom_module import CustomModule # <--- Nuevo módulo
```

2. Inyecte el módulo y agréguelo a la lista de módulos de consulta `CONSULT_USER_AUTOMATION_MODULES`:
```python
# bot/services/automation_service.py

class AutomationService(BaseInjectable, BaseService):

    def __init__(self,
                ...,
                custom_module: CustomModule, # <---- Dependencia del nuevo módulo
                i18n: I18N) -> None:

        self.CONSULT_USER_AUTOMATION_MODULES = [
            ...,
            custom_module, # <--- Inyección del nuevo módulo
        ]
```

También puede usar una función en lugar de un módulo utilizando una instancia de la clase `Module`.
El atributo `name` puede ser una [clave I18N](#traducción-con-i18n) en caso de que se deba traducir el nombre de la función:
```python
# bot/services/automation_service.py

from ... import my_function # <--- Importar función

@dataclass
class Module:
    name: str
    func: Callable[..., ApplicationUserDataResponseDto]

class AutomationService(BaseInjectable, BaseService):

    def __init__(self, ...) -> None:

        self.CONSULT_USER_AUTOMATION_MODULES = [
            ...,
            Module(name='my_function', func=my_function), # <--- Agregar como un módulo
        ]
```

Si el módulo tiene el método [`remove_user`](#método-de-retiro-de-usuarios), para utilizarlo en la ejecución del proceso de retiro,
agréguelo a la lista de módulos de retiro:
```python
# bot/services/automation_service.py

class AutomationService(BaseInjectable, BaseService):

    def __init__(self,
                ...,
                custom_module: CustomModule, # <---- Dependencia del nuevo módulo
                i18n: I18N) -> None:

        self.REMOVE_USER_AUTOMATION_MODULES = [
            ...,
            custom_module, # <--- Inyección del nuevo módulo
        ]
```

3. (OPCIONAL) Agregue la traducción para el nombre del módulo o de la función (en caso de que haya usado una instancia de la clase `Module`) como se explica [aquí](#traducción-con-i18n). Esta traducción será utilizada por el módulo [**PDF**](/bot/lib/results_exporter.py) al exportar los resultados de una consulta o retiro:

```python
# bot/i18n/es_CO/__init__.py

es_CO = {

    #region applications
    ...,
    'CustomModule': 'Custom Module', # <--- Nombre del nuevo módulo
    #endregion
}
```

NOTA: Si la respuesta del módulo contiene campos cuyo nombre no se encuentra en el [diccionario de traducción](#traducción-con-i18n) puede agregarlos para que estos sean traducidos durante el exporte de los resultados de la consulta a PDF, por ejemplo:
```python
# bot/modules/custom_module.py

from dtos.automation_dto import CustomModuleResponseDto

class CustomModule(BaseInjectable, BaseModule):
    def main(self, ...):
        ...

        # Agregar nombres de campos `active` y `locked` al I18N
        return CustomModuleResponseDto(active=True, locked=False)
```
```python
# bot/i18n/es_CO/__init__.py

es_CO = {

    #region common
    ...,
    'active': 'Activo', # <--- Traducción de nombres de campos `active` y `locked`
    'locked': 'Bloqueado',
    #endregion
}
```

### Traducción con I18N

I18N es un módulo de traducción. Se inyecta como dependencia usando el método "Transient" explicado en la sección de [inyección de dependencias](#inyección-de-dependencias).

Cuando se recibe una petición con el header "Accept-Language", los controladores capturan el valor del header y lo asignan al campo "language" del módulo I18N. Esta captura se realiza en el método de configuraciones previas de la librería [BaseController](/bot/lib/base_controller.py) como se muestra a continuación:

```python
# bot/lib/base_controller.py

class BaseController:

    def prev(self) -> None:
        cast(I18N, self.injectable.get(I18N)).language = self.request.headers.get('Accept-Language')
```

De esa forma **I18N** reconoce en qué lenguaje mostrar los mensajes. En caso de no recibir este header, se usará el lenguaje por defecto (es-CO).

NOTA: Su uso no es obligatorio, excepto en los validadores.

#### Uso

Su modo de uso es el siguiente:

1. Agregue el mensaje a cada diccionario de I18N:

```python
# bot/i18n/es_CO/__init__.py

# Diccionario del lenguaje español de Colombia
es_CO = {

    ...,
    '<mensaje de I18N>': 'Texto a mostrar',
}
```

2. Inyectar I18N:

```python
class CustomModule(BaseInjectable):

    def __init__(self,
        ...,
        i18n: I18N # <--- Inyección del I18N
    ):
        super().__init__()
        self.i18n = i18n # <--- Asignarlo como atributo de la clase
```

3. Ejecutar I18N pasándole el mensaje a mostrar:

```python
class CustomModule(BaseInjectable):

    def main(self, ...):

        response = requests.get('www.example.com')
        if not response.ok:
            raise AutomationError(
                self.i18n('<mensaje de I18N>') # Clave del mensaje de I18N
            )
```

#### Agregar nuevo lenguaje

Para agregar un nuevo lenguaje, siga los siguientes pasos:

1. Cree una carpeta nombrada con el código del lenguaje en la carpeta del módulo I18N (bot/i18n). Dentro de la carpeta cree un archivo llamado "**\_\_init\_\_.py**"":

NOTA: Al crear la carpeta, no utilice guiones medios ni espacios, sólo letras y guiones bajos.

![Lenguaje I18N](/docs/screenshots/development_i18n_language.png)

2. Cree un diccionario de Python con el código del lenguaje dentro del **\_\_init\_\_.py** e ingrese los mensajes, así:

```python
# bot/i18n/es_US/__init__.py

en_US = {
    '<mensaje i18n 1>': 'Texto a mostrar 1',
    '<mensaje i18n 2>': 'Texto a mostrar 2',
    ...
}
```

También puede usar regiones para clasificar y/o colapsar los mensajes (normalmente, se crea una región llamada "common" que ubica los mensajes más comunes que aplican para cualquier módulo):

```python
# bot/i18n/es_US/__init__.py

en_US = {
    #region region1
    '<mensaje i18n 1>': 'Texto a mostrar 1',
    '<mensaje i18n 2>': 'Texto a mostrar 2',
    #endregion

    #region region2
    '<mensaje i18n 3>': 'Texto a mostrar 3',
    '<mensaje i18n 4>': 'Texto a mostrar 4',
    #endregion
    ...
}
```

3. Importe el nuevo lenguaje en el [módulo I18N](bot/i18n/__init__.py) y objeto al diccionario de lenguajes.

```python
# bot/i18n/__init__.py

from .en_US import en_US # <--- Nuevo lenguaje

class I18N(BaseInjectable):

    languages = {
        ...,
        'en_US': en_US, # <--- Nuevo lenguaje
    }
```

#### Mensajes con parámetros

Es posible pasar parámetros a un mensaje mediante el parámetro "**format_values**".

Suponiendo que tenemos el siguiente mensaje:

```python
# bot/i18n/es_US/__init__.py

en_US = {
    ...,
    '<mensaje i18n>': 'Texto a mostrar con el parámetro "{valor}".',
}
```

Podemos reemplazar `{valor}` por un parámetro de la siguiente manera:

```python
class CustomModule(BaseInjectable):

    def main(self, ...):

        response = requests.get('www.example.com')
        if not response.ok:
            message = 'error'
            raise AutomationError(
                # Mensaje de I18N pasando `message` como parámetro
                # para reemplazar "{valor}"
                self.i18n('<clave I18N>', {'valor': message})
            )
```

De esta forma, el mensaje resultante sería:

```
Texto a mostrar con un parámetro "error".
```

### Librerías

#### Definición
Son módulos para realizar operaciones varias como conexiones a APIs, envío de correos, exporte de datos, conexión a plataformas, entre otras. Se pueden inyectar como se mencionó en la sección de [inyección de dependencias](#inyección-de-dependencias) pero no es obligatoria su inyección, la decisión de inyectar una librería o no depende de la necesidad del desarrollador y que problema quiera resolver. Se ubican en la carpeta "**lib**".

Ejemplo de librería de SAML:
```python
from lib.base_injectable import BaseInjectable

class SAML(BaseInjectable):

    def __init__(self,
        # Se inyectan las dependencias requeridas
        # por ejemplo, la configuración del servidor
        i18n: I18N
    ):
        super().__init__()
        self.i18n = I18N
        self.session = TLSSession()
        ...

    def authenticate(self):
        ...
```

#### Descripción de las librerías
| Librería| Descripción|
| ---- | ---- |
| [**active_directory.py**](/bot/lib/active_directory.py) | Dependencia ActiveDirectory para consultar usuarios y hacer login en el directorio activo. |
| [**ariba_session.py**](/bot/lib/ariba_session.py) | Clase **AribaSession** para realizar la autenticación SAML y manejar la sesión en Ariba. |
| [**ariba_retiros_session.py**](/bot/lib/ariba_retiros_session.py) | Clase **AribaRetirosSession** para realizar la autenticación SAML y manejar la sesión del usuario de retiros en Ariba. |
| [**auth.py**](/bot/lib/auth.py) | Decorador **auth** para restringir un endpoint a sólo clientes autenticados y métodos para crear un token de autenticación válido. |
| [**base_controller.py**](/bot/lib/base_controller.py) | Clase base para los controladores y decoradores para marcar especificar el método (GET, POST, DELETE o PUT) y ruta de cada endpoint. |
| [**base_dto.py**](/bot/lib/base_dto.py) | Clase base para los DTOs de petición y de respuesta. |
| [**base_exception.py**](/bot/lib/base_exception.py) | Clase base para excepciones. |
| [**base_export.py**](/bot/lib/base_export.py) | Clase base para módulos de exportación de datos. |
| [**base_injectable.py**](/bot/lib/base_injectable.py) | Clase base para todo lo que se vaya a inyectar como una dependencia. |
| [**base_middleware.py**](/bot/lib/base_middleware.py) | Clase base para middlewares. |
| [**base_model.py**](/bot/lib/base_model.py) | Clase base para modelos. |
| [**base_module.py**](/bot/lib/base_module.py) | Clase base para módulos de automatización. |
| [**base_response.py**](/bot/lib/base_response.py) | Clase base para respuestas. |
| [**base_serializer.py**](/bot/lib/base_serializer.py) | Clase base para serializadores que convierten uno o varios modelos en DTOs. |
| [**base_service.py**](/bot/lib/base_service.py) | Clase base para servicios. |
| [**base_validator.py**](/bot/lib/base_validator.py) | Clase base para validadores. |
| [**config.py**](/bot/lib/config.py) | Clase **Config** para almacenar y administrar la configuración del servidor. |
| [**data_frame_tools.py**](/bot/lib/data_frame_tools.py) | Clase **DataFrame** para crear y manejar marcos de datos a partir de archivos CSV o Excel. |
| [**env.py**](/bot/lib/env.py) | Clase **Env** para la lectura y almacenamiento de las variables de entorno del archivo **.env**. |
| [**exceptions.py**](/bot/lib/exceptions.py) | Excepciones personalizadas. |
| [**export.py**](/bot/lib/export.py) | Clase **ExportData** para exportar DTOs. |
| [**filter.py**](/bot/lib/filter.py) | Clase **Filter** para crear filtros de consultas de búsqueda. |
| [**injectable.py**](/bot/lib/injectable.py) | Lógica de la inyección de dependencias. |
| [**injectables.py**](/bot/lib/injectables.py) | Módulo de inyección de dependencias. |
| [**interfaces.py**](/bot/lib/interfaces.py) | Interfaces. |
| [**log.py**](/bot/lib/log.py) | Clase **Log** para crear logs. |
| [**logger.py**](/bot/lib/logger.py) | Logger del proyecto. |
| [**mail_template.py**](/bot/lib/mail_template.py) | Plantillas para el cuerpo de los correos. |
| [**mail.py**](/bot/lib/mail.py) | Clase **Mail** para enviar correos. |
| [**msal.py**](/bot/lib/msal.py) | Clase **MSAL** para realizar consultas mediante una API de Azure. |
| [**pagination.py**](/bot/lib/pagination.py) | Clase **Pagination** para crear un objeto con datos paginados. |
| [**porfin_session.py**](/bot/lib/porfin_session.py) | Clase **PorfinSession** para realizar la autenticación y manejar la sesión en Porfin. |
| [**porfin_retiros_session.py**](/bot/lib/porfin_retiros_session.py) | Clase **PorfinRetirosSession** para realizar la autenticación y manejar la sesión del usuario de retiros en Porfin. |
| [**query.py**](/bot/lib/query.py) | Clase **Query** para crear consultas de Mongo complejas que incorporen proyección y traducción de datos en Mongo. |
| [**responses.py**](/bot/lib/responses.py) | Respuestas personalizadas. |
| [**results_exporter.py**](/bot/lib/results_exporter.py) | Clase **ResultsExporter** para exportar instancias de los modelo **ConsultedUser** o **RemovedUser**, es decir, los resultados de la consulta o retiro de uno o muchos usuarios. |
| [**routes.py**](/bot/lib/routes.py) | Implementación personalizada de la clase **Route** de **Starlette** para la incluir la inyección de dependencias. |
| [**salesforce.py**](/bot/lib/salesforce.py) | Clase **Salesforce** para crear conexiones a la API de Salesforce. |
| [**salud_web_session.py**](/bot/lib/salud_web_session.py) | Clase **SaludWebSession** para realizar la autenticación y manejar la sesión en Salud Web. |
| [**salud_web_session_retiros.py**](/bot/lib/salud_web_session_retiros.py) | Clase **SaludWebRetirosSession** para realizar la autenticación y manejar la sesión del usuario de retiros en Salud Web. |
| [**saml.py**](/bot/lib/saml.py) | Clase **SAML** para autenticar mediante SAML. |
| [**tools.py**](/bot/lib/tools.py) | Herramientas (funciones varias). |
| [**translate.py**](/bot/lib/translate.py) | Clase **Translate** para la traducción de datos en Mongo. |
| [**user_session.py**](/bot/lib/user_session.py) | Backend del middleware de autenticación. |

### Librería `http`

Esta librería proporciona [**adaptadores HTTP**](/bot/lib/http/adapters.py), [**políticas de cookies**](/bot/lib/http/cookie_policies.py) y [**sesiones personalizadas**](/bot/lib/http/sessions.py). Entre las sesiones, se encuentra la clase **TLSSession** para crear una sesión que registra el adaptador HTTP personalizado **TLSAdapter** el cual admite conexiones SSL que hacen uso de una versión de OpenSSL antigua. También implementa la clase **AllowsEmptyDomainsPolicy** para procesar cookies cuyo  *domain* es un string vacío.

```python
class TLSSession(requests.Session):
    """A `requests.Session` object that
    accepts unsafe and insecure connections
    and allows empty-domain cookies.
    """

    timeout: int
    """Requests timeout."""

    def __init__(self, timeout: int = 10) -> None:
        """Creates a `requests.Session` object registering
        the custom `TLSAdapter` HTTP adapter.

        It also implements the `AllowsEmptyDomainsPolicy`
        policy to allow empty-domain cookies.

        Parameters
        ----------
        timeout : int | None, optional
            Requests timeout in seconds, by default None.
        """

        super().__init__()
        self.verify = False
        self.timeout = timeout
        self.mount('https://', TLSAdapter())
        self.cookies.set_policy(AllowsEmptyDomainsPolicy())

    def request(self, method: str | bytes, url: str | bytes, **kwargs) -> requests.Response:
        kwargs.setdefault('timeout', self.timeout)
        return super().request(method, url, **kwargs)
```

### Librería `pdf`

Esta librería proporciona una [clase](/bot/lib/pdf/pdf.py) para construir archivos PDF utilizando la biblioteca [`reportlab`](https://docs.reportlab.com/demos/hello_world/hello_world/). La librería [**results_exporter.py**](/bot/lib/results_exporter.py) utiliza esta clase para generar el informe correspondiente a la exportación de usuarios consultados y retirados.

#### Ejemplo de uso

La clase [`PDF`](/bot/lib/pdf/pdf.py) contiene métodos para agregar diferentes elementos al PDF y renderizarlos en un archivo. Por ejemplo, el siguiente código genera un PDF con el texto **¡Hola Mundo!**:
```python
from lib.pdf import PDF
from lib.pdf.interfaces import *

# Crear objeto de PDF
pdf = PDF()

# Agregar párrafo con el texto "¡Hola Mundo!"
pdf.add_paragraph('¡Hola Mundo!')

# Renderizar elementos del PDF y obtener el contenido en bytes
content = pdf.build(title='Mi primer PDF')

# Guardar contenido a un archivo .pdf
with open('my_pdf.pdf', 'wb') as f:
    f.write(content)
```

El PDF resultante luciría así:

![Hola Mundo en PDF](/docs/screenshots/development_pdf_hello_world.png)

La clase `PDF` hereda de **BaseInjectable**, por lo tanto, también puede ser inyectada como una dependencia tal y como se explica [aquí](#inyección-de-dependencias):
```python
from lib.pdf import PDF
from lib.pdf.interfaces import *

class CustomModule(BaseInjectable, BaseModule):

    def __init__(self,
        pdf: PDF # <--- Inyección del módulo PDF
    ) -> None:
        super().__init__()
        self.pdf = pdf

    # Método para crear el PDF
    def build_pdf(self):

        # Agregar párrafo con el texto "¡Hola Mundo!"
        self.pdf.add_paragraph('¡Hola Mundo!')

        # Renderizar elementos del PDF y obtener el contenido en bytes
        content = self.pdf.build(title='Mi primer PDF')

        # Guardar contenido a un archivo .pdf
        with open('my_pdf.pdf', 'wb') as f:
            f.write(content)
```

#### Métodos

A continuación se presenta la descripción de cada método que contiene el módulo para generar el PDF y algunos métodos de utilidad:

| Método | Descripción |
| ---- | ---- |
| **add_front_page** | Agrega una portada con un formato ya preestablecido. Para ver una ejemplo de portada [haga click aquí](/docs/screenshots/development_pdf_front_page.png). |
| **add_line** | Agrega una línea o separador |
| **add_page_break** | Agrega un salto de página. |
| **add_paragraph** | Agrega un párrafo. |
| **add_spacer** | Agrega un espaciado. |
| **add_subtitle** | Agrega un subtítulo. |
| **add_table** | Agrega una tabla. |
| **add_table_from_dict** | Agrega una tabla a partir de un diccionario. |
| **add_table_from_list** | Agrega una tabla a partir de una lista. |
| **add_table_of_contents** | Agrega una tabla de contenidos. Para ver un ejemplo de tabla de contenidos [haga click aquí](/docs/screenshots/development_pdf_toc.png). |
| **add_title** | Agrega un título. |
| **add_title1** | Agrega un título de primer nivel. |
| **add_title2** | Agrega un título de segundo nivel. |
| **add_title3** | Agrega un título de tercer nivel. |
| **build** | Renderiza todos los elementos y retorna el contenido del PDF en bytes. |
| **get_color_from_hex** | Obtiene una instancia de la clase [`Color`](https://docs.reportlab.com/reportlab/userguide/ch2_graphics/#colors) de **reportlab** a partir de un color hexadecimal. |
| **get_color_from_rgb** | Obtiene una instancia de la clase [`Color`](https://docs.reportlab.com/reportlab/userguide/ch2_graphics/#colors) de **reportlab** a partir de un color RGB. |

#### PDF de ejemplo

Si está viendo esta documentación desde Visual Studio Code, puede instalar la extensión [vscode-pdf](https://marketplace.visualstudio.com/items?itemName=tomoki1207.pdf) y [hacer click aquí](/docs/files/example.pdf) para abrir el PDF de ejemplo que contiene todos los elementos mencionados anteriormente (portada, tabla de contenido, separadores, saltos de página, párrafos, espaciados, títulos y subtítulos).

También puede abrir manualmente el archivo de la ruta **/docs/files/example.pdf** y verlo desde algún visor de PDF o un navegador web.

### Librería `validator`

Esta librería proporciona métodos de validación para campos de DTOs.

#### Ejemplo de uso

A continuación se presenta un ejemplo de uso en un validador de un modelo de roles:

```python
# bot/validators/role_validator.py

from i18n import I18N
from lib.base_injectable import BaseInjectable
from lib.base_validator import BaseValidator
from lib.validator.data_validator import DataValidator
from lib.validator.validator import Validator

class RoleValidator(BaseInjectable, BaseValidator):

    def __init__(self, i18n: I18N) -> None:
        super().__init__()
        self.i18n = i18n

    # Valida que el nombre del rol que se va a crear no exista
    async def validate_name(self, name: str) -> bool:
        role = await self.service.filter_name(name)
        return True if role is None else False

    # Crear validador de datos
    def validator(self, role: RoleRequestDto) -> DataValidator:
        validator = DataValidator()

        # Agregar una validación para un campo
        validator.add(
            # Nombre de la validación
            # Se recomienda usar el mismo nombre del campo a validar
            'name',

            # Tipo de campo "str", el campo es "name"
            Validator[str](role.name)
                .required( # El campo es requerido
                    'role_is_required') # Mensaje I18N personalizado (OPCIONAL)
                .custom( # Validación personalizada
                    self.validate_name, # Función de la validación personalizada
                    # Mensaje I18N personalizado (OPCIONAL)
                    message='role_already_exists',
                    # Parámetros del mensaje I18N personalizado (OPCIONAL)
                    format_values={'name': role.name}))

        return validator

    # Ejecutar el validador
    async def validate(self, role: RoleCreateDto) -> bool:
        validator = self.validator(role)
        if await validator.validate():
            return True

        # Obtener mensajes de error en caso de que hayan campos no válidos
        self.error = validator.get_error(self.i18n)
        return False
```

Como se puede observar en el ejemplo anterior, los validadores tienen un mensaje predefinido que se puede sobrescribir mediante el parámetro "**message**". En el caso del validador "**custom**", este contiene un parámetro "**format_values**" que permite pasar parámetros al mensaje personalizado tal y como se explica en la sección de [mensajes con parámetros de I18N](#mensajes-con-parámetros).

#### Descripción de los validadores

Los validadores definen qué tipo de validación se hará sobre el elemento.

Cada validador tiene su respectiva documentación. Para saber como se usa un validador diríjase a su respectivo archivo y revise el **docstring** tal y como se menciona al [inicio de esta guía](#guía-de-modificaciones-del-bot).

A continuación se presenta la descripción de cada validador:

| Validador | Descripción |
| ---- | ---- |
| [**cell_phone_validator.py**](/bot/lib/validator/validators/cell_phone_validator.py) | Validador de números de teléfono móvil. |
| [**custom_validator.py**](/bot/lib/validator/validators/custom_validator.py) | Validador personalizado. |
| [**email_validator.py**](/bot/lib/validator/validators/email_validator.py) | Validador de emails. |
| [**greater_equals_than_validator.py**](/bot/lib/validator/validators/greater_equals_than_validator.py) | Mayor o igual que. |
| [**greater_than_validator.py**](/bot/lib/validator/validators/greater_than_validator.py) | Mayor que. |
| [**id_card_validator.py**](/bot/lib/validator/validators/id_card_validator.py) | Validador de números de documento. |
| [**in_validator.py**](/bot/lib/validator/validators/in_validator.py) | Validar si el elemento está en una lista. |
| [**lower_equals_than_validator.py**](/bot/lib/validator/validators/lower_equals_than_validator.py) | Menor o igual que. |
| [**lower_than_validator.py**](/bot/lib/validator/validators/lower_than_validator.py) | Menor que. |
| [**max_length_validator.py**](/bot/lib/validator/validators/max_length_validator.py) | Longitud máxima. |
| [**min_length_validator.py**](/bot/lib/validator/validators/min_length_validator.py) | Longitud mínima. |
| [**object_compare_validator.py**](/bot/lib/validator/validators/object_compare_validator.py) | Comparar similitud entre dos diccionarios. |
| [**password_validator.py**](/bot/lib/validator/validators/password_validator.py) | Validador de contraseña  |
| [**phone_validator.py**](/bot/lib/validator/validators/phone_validator.py) | Validador de número de teléfono fijo  |
| [**range_length_validator.py**](/bot/lib/validator/validators/range_length_validator.py) | Validar que la longitud del elemento esté en un rango. |
| [**range_validator.py**](/bot/lib/validator/validators/range_validator.py) | Validar que el número esté en un rango. |
| [**regular_expression_validator.py**](/bot/lib/validator/validators/regular_expression_validator.py) | Validar que el elemento existe usando regex. |
| [**required_validator.py**](/bot/lib/validator/validators/required_validator.py) | Requerido. |
| [**url_validator.py**](/bot/lib/validator/validators/url_validator.py) | Validador de URL. |

#### Descripción de los operadores

Los operadores permiten controlar el flujo de ejecución de las validaciones.

Cada operador tiene su respectiva documentación. Para saber como se usa un operador diríjase a su respectivo archivo y revise el **docstring** tal y como se menciona al [inicio de esta guía](#guía-de-modificaciones-del-bot).

A continuación se presenta la descripción de cada operador:

| Operador | Descripción |
| ---- | ---- |
| [**if_not_operator.py**](/bot/lib/validator/operators/if_not_operator.py) | Continua con la validación siguiente si la condición no se cumple. |
| [**if_operator.py**](/bot/lib/validator/operators/if_operator.py) | Continua con la validación siguiente si la condición se cumple. |
| [**stop_operator.py**](/bot/lib/validator/operators/stop_operator.py) | Continua con la validación siguiente si hasta el momento todas las validaciones anteriores han sido correctas. |

# Containers settings
PROD=true # Flag for production mode
SERVER_NAME="botconsultausuarios.suramericana.com.co" # Server IP or domain name
BOT_PORT=8010 # Bot container port
FRONTEND_PORT=8090 # Frontend container port
LAB_ENV=false # Flag for lab environment

# Database settings
MONGO_INITDB_DATABASE="sura" # Database name
MONGO_INITDB_ROOT_USERNAME= # Admin user
MONGO_INITDB_ROOT_PASSWORD= # Password of the admin user
DATABASE_USER= # User of the database connection (<PERSON><PERSON> will use this to connect to the database)
DATABASE_PASSWORD= # Password of the database connection (<PERSON><PERSON> will use this to connect to the database)
DATABASE_VOLUME_PATH="./db" # Path to the database volume

# Authentication settings
SERVICE="BotConsultaUsuarios" # SEUS 4 service provider name
SERVICE_URL="https://seus.sura.com/conf/configuration/sso-api" # SEUS 4 service provider configuration URL
SP_ENTITY_ID="BotConsultaUsuarios" # SEUS 4 service provider name
SSO_SECRET="yY9dSJ2EuF3t[9SEXFGf9[FQmsRnQEI@gysMIXMCw0_gl9Ik7PUT]y_nHE>hHvUx" # Secret to decrypt SEUS 4 variables
PUBLIC_RESOURCES="health" # Public resources
AUTH_CACHE_ENABLE=1 # Enable SEUS 4 authorization caching
AUTH_CACHE_TIME_TO_LIVE=60 # Life-time of SEUS 4 authorization caché
AUTH_ENABLE=false # Enable SEUS 4 authorization
SERVICE_CONF_CACHE_TIME=7 # Life-time of SEUS 4 service provider configuration in days
USE_AUTHENTICATION=true # Flag for authentication
USE_AUTHORIZATION=true # Flag for authorization

# Data settings
ROWS_PER_PAGE=25 # Limit of rows to fetch when executing "find" or "find_many" model functions
EXPORT_LIMIT=1000 # Default limit value for exporting rows, set to 0 to disable

# PDF settings
PDF_MAIN_LOGO_IMG="static/sura_logo.png" # Main logo image path
PDF_SECONDARY_LOGO_IMG="static/arus_logo.png" # Secondary logo image path

# Mailer settings
MAILER_ENABLED=false # Flag for mailer
MAILER_URL="https://mosaico.arus.com.co:3000/mailer/withAttachments" # Url of the service to send mails
MAILER_TYPE="divulgacion_mosaico" # Type of mails, DO NOT CHANGE THIS VALUE
MAILER_CONTRACT="5d682412e0a6e100062cc5cc" # Contract of mails, DO NOT CHANGE THIS VALUE
MAILER_SEND_FROM="<EMAIL>" # Admins mails sender

# Automation settings
ENABLE_BOT_RETIROS=false # Flag for Bot Retiros
APPS_CONNECTION_USER= # Connection user for most of applications
APPS_CONNECTION_PASSWORD= # Connection password for most of applications
RETIROS_APPS_CONNECTION_USER= # Connection user to remove users on applications
RETIROS_APPS_CONNECTION_PASSWORD= # Connection password to remove users on applications
CONSULTED_USERS_TIMEOUT_MINUTES=7200 # Life time of the consulted users data. After this time, data will be updated at the next automation
USE_RAW_AUTOMATION_RESPONSE=false # Use the hardcoded automation response in the frontend in case there is no connection to SURA VPN
CONSULT_USERS_MAX=20 # Maximum number of users that can be consulted
CONSOLIDATED_TIME="19:00" # Time to consolidate removed users
CONSOLIDATED_INTERVAL=30 # Interval in seconds to check the time to consolidate removed users

# Active directory settings (view https://ldap3.readthedocs.io/en/latest/server.html?highlight=get_info#server-object for more details)
LDAP_DOMAIN= # Domain name of the Active Directory server
LDAP_USER= # User of the Active Directory connection
LDAP_PASSWORD= # Password of the Active Directory connection
LDAP_BASE_DN= # Base DN where to performs queries
LDAP_SERVER="ldaps://<host>:<port>" # Connection server, the port is usually 636 for secure connections or 389 for insecure connections
LDAP_USE_SSL=1 # 1 if the connection is on a secure port, 0 if not

# IDM API settings
IDM_API_USER= # User to connect to the IDM API
IDM_API_CRED= # Password to connect to the IDM API
IDM_API_URL= # Base URL of the IDM API

# Salesforce settings
SALESFORCE_DOMAIN="login" # "login" for prod, "test" for testing environment.
SALESFORCE_USER= # User of the Salesforce user
SALESFORCE_PASSWORD= # Password of the Salesforce user
SALESFORCE_SECURITY_TOKEN= # Security token of the Salesforce user
SALESFORCE_RETIROS_USER= # Bot Retiros user of the Salesforce user
SALESFORCE_RETIROS_PASSWORD= # Bot Retiros password of the Salesforce user
SALESFORCE_RETIROS_SECURITY_TOKEN= # Bot Retiros security token of the Salesforce user
SALESFORCE_CLIENT_ID= # Client ID of the Salesforce application
SALESFORCE_CLIENT_SECRET= # Secret of the Salesforce application

# Health Cloud settings
HEALTH_CLOUD_DOMAIN="login" # "login" for prod, "test" for testing environment.
HEALTH_CLOUD_USER= # User of the Health Cloud user
HEALTH_CLOUD_PASSWORD= # Password of the Health Cloud user
HEALTH_CLOUD_SECURITY_TOKEN= # Security token of the Health Cloud user
HEALTH_CLOUD_RETIROS_USER= # Bot Retiros user of the Health Cloud user
HEALTH_CLOUD_RETIROS_PASSWORD= # Bot Retiros password of the Health Cloud user
HEALTH_CLOUD_RETIROS_SECURITY_TOKEN= # Bot Retiros security token of the Health Cloud user
HEALTH_CLOUD_CLIENT_ID= # Client ID of the Health Cloud application
HEALTH_CLOUD_CLIENT_SECRET= # Secret of the Health Cloud application

# SEUS settings
SEUS_USER= # User of the SEUS connection
SEUS_PASSWORD= # Password of the SEUS connection

# Confluence settings
CONFLUENCE_ORG_ID= # Organization ID of the Confluence connection
CONFLUENCE_API_TOKEN= # API token of the Confluence connection

# Porfin settings
PORFIN_DOMAIN= # Domain of Porfin environment
PORFIN_USER= # User of the Porfin connection
PORFIN_PASSWORD= # Password of the Porfin connection
PORFIN_RETIROS_USER= # Bor Retiros user of the Porfin connection
PORFIN_RETIROS_PASSWORD= # Bor Retiros password of the Porfin connection

# Viafirma settings
VIAFIRMA_USER= # User of the Viafirma connection
VIAFIRMA_PASSWORD= # Password of the Viafirma connection
VIAFIRMA_RETIROS_USER= # Bot Retiros user of the Viafirma connection
VIAFIRMA_RETIROS_PASSWORD= # Bot Retiros password of the Viafirma connection

# Eventos Adversos settings
EVENTOS_ADVERSOS_USER= # User of the Eventos Adversos connection
EVENTOS_ADVERSOS_PASSWORD= # Password of the Eventos Adversos connection
EVENTOS_ADVERSOS_RETIROS_USER= # Bot Retiros user of the Eventos Adversos connection
EVENTOS_ADVERSOS_RETIROS_PASSWORD= # Bot Retiros password of the Eventos Adversos connection

# Case Tracking settings
CASE_TRACKING_USER= # User of the Case Tracking connection
CASE_TRACKING_PASSWORD= # Password of the Case Tracking connection
CASE_TRACKING_RETIROS_USER= # Bot Retiros User of the Case Tracking connection
CASE_TRACKING_RETIROS_PASSWORD= # Bot Retiros Password of the Case Tracking connection

# OIPA settings
OIPA_USER= # User of the OIPA connection
OIPA_PASSWORD= # Password of the OIPA connection
OIPA_ENV="ID_PDN_01" # Environment of the OIPA connection (ID_PDN_01 for prod and ID_DEV_02 for lab)

# IDM settings
IDM_USER= # User of the IDM connection
IDM_PASSWORD= # Password of the IDM connection

# MSAL settings
MSAL_CLIENT_ID= # Azure application client ID
MSAL_CLIENT_SECRET= # Azure application client secret
MSAL_TENANT_ID= # Azure application tenant ID

# OHI settings
OHI_CLIENT_ID= # OHI OAuth 2.0 application client ID
OHI_CLIENT_SECRET= # OHI OAuth 2.0 application client secret

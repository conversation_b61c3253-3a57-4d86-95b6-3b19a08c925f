services:
  auth:
    build:
      context: ./auth
      dockerfile: Dockerfile
    restart: on-failure:3
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    expose:
      - 3000
    volumes:
      - ./auth/src:/app/src
      - ./cert:/etc/ssl/certs
    env_file:
      - .env
  db:
    image: mongo:8.0.13
    restart: on-failure:3
    healthcheck:
      test:
        [
          "CMD",
          "mongosh",
          "--quiet",
          "localhost/test",
          "--eval",
          "'quit(db.runCommand({ ping: 1 }).ok ? 0 : 2)'",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
    expose:
      - 27017
    volumes:
      - ${DATABASE_VOLUME_PATH:-./db}:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js
    env_file:
      - .env
    labels:
      logging: promtail
      logging_jobname: containerlogs
  bot_consult:
    build:
      context: ./bot
      dockerfile: Dockerfile
    restart: on-failure:3
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:8010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    expose:
      - 8010
    volumes:
      - ./bot:/app
      - ./cert:/etc/ssl/certs
      - ${LOGS_FOLDER_PATH:-./logs}:/usr/logs
    env_file:
      - .env
    depends_on:
      - db
    labels:
      logging: promtail
      logging_jobname: containerlogs
  bot_remove:
    build:
      context: ./bot
      dockerfile: Dockerfile
    restart: on-failure:3
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost:8010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    expose:
      - 8010
    volumes:
      - ./bot:/app
      - ./cert:/etc/ssl/certs
      - ${LOGS_FOLDER_PATH:-./logs}:/usr/logs
    env_file:
      - .env
    depends_on:
      - db
    labels:
      logging: promtail
      logging_jobname: containerlogs
  bot_proxy:
    build:
      context: ./bot_proxy
      dockerfile: Dockerfile
    restart: on-failure:3
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost/proxy/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    ports:
      - ${BOT_PORT:-8011}:443
    env_file:
      - .env
    depends_on:
      - bot_consult
      - bot_remove
    volumes:
      - ./cert:/etc/nginx/ssl
    labels:
      logging: promtail
      logging_jobname: containerlogs
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - SERVER_NAME=${SERVER_NAME:-localhost}
        - BOT_PORT=${BOT_PORT:-8011}
    restart: on-failure:3
    healthcheck:
      test: ["CMD", "curl", "-kf", "https://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
    ports:
      - ${FRONTEND_PORT:-8090}:443
    depends_on:
      - bot_proxy
    volumes:
      - ./frontend/public/icons:/usr/share/nginx/html/icons
      - ./frontend/public/images:/usr/share/nginx/html/images
      - ./cert:/etc/nginx/cert
    env_file:
      - .env
    labels:
      logging: promtail
      logging_jobname: containerlogs

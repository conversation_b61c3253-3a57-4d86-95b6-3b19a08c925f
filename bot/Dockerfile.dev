FROM python:3.12-alpine3.20

ENV PIP_ROOT_USER_ACTION=ignore \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

RUN apk add --no-cache --no-check-certificate \
    bash \
    curl \
    gcc \
    libxml2-dev \
    libxslt-dev \
    musl-dev \
    procps \
    python3-dev \
    tzdata \
    vim \
    ca-certificates \
    && update-ca-certificates \
    && cp /usr/share/zoneinfo/America/Bogota /etc/localtime \
    && echo "America/Bogota" > /etc/timezone

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir --disable-pip-version-check \
    --trusted-host pypi.org \
    --trusted-host pypi.python.org \
    --trusted-host files.pythonhosted.org \
    -r requirements.txt

COPY . .

CMD ["python", "main.py"]

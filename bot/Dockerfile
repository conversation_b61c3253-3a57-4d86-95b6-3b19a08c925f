# Build stage
FROM python:3.12-alpine3.20 AS builder

RUN apk add --no-cache --no-check-certificate \
    gcc \
    libxml2-dev \
    libxslt-dev \
    musl-dev \
    python3-dev

WORKDIR /app

COPY requirements.txt .

RUN pip install --no-cache-dir --disable-pip-version-check \
    --trusted-host pypi.org \
    --trusted-host pypi.python.org \
    --trusted-host files.pythonhosted.org \
    -r requirements.txt --target /deps

# Production stage
FROM python:3.12-alpine3.20

ENV PIP_ROOT_USER_ACTION=ignore \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

RUN apk add --no-cache --no-check-certificate \
    bash \
    curl \
    libxml2 \
    libxslt \
    procps \
    tzdata \
    vim \
    ca-certificates \
    && update-ca-certificates \
    && cp /usr/share/zoneinfo/America/Bogota /etc/localtime \
    && echo "America/Bogota" > /etc/timezone

WORKDIR /app

COPY --from=builder /deps /usr/local/lib/python3.12/site-packages

COPY . .

CMD ["python", "main.py"]

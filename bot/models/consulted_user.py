from typing import Any
from datetime import datetime
from pymongo import IndexModel, ASCENDING

from lib.base_model import BaseModel
from lib.active_directory import ADUser


class ConsultedUser(BaseModel):
    username: str
    exp: datetime
    expUTC: datetime
    ad_user: ADUser | None
    data: list[dict[str, Any]]
    time: float = 0.0
    errorCount: int = 0
    warningCount: int = 0

    class Settings:
        indexes = [
            # Expire after 3 days
            IndexModel(keys=[('expUTC', ASCENDING)], expireAfterSeconds=259200),
        ]

from typing import Any, Literal
from beanie import PydanticObjectId

from lib.base_model import BaseModel


class Log(BaseModel):
    transactionId: PydanticObjectId | None = None
    username: str
    moduleName: str
    moduleFuncArgsSpec: str
    moduleFuncArgs: list[object]
    application: str
    applicationName: str
    data: dict[str, Any] | None = None
    message: str | None = None
    detail: str | None = None
    startTime: float = 0.0
    endTime: float = 0.0
    timeDiff: float = 0.0
    userNotFound: bool = False
    ignored: bool = False
    warning: str | None = None
    status: Literal['done', 'error', 'ignored', 'user_not_found']
    action: Literal['consult', 'remove'] = 'consult'
    consultedBy: str | None = None
    removedBy: str | None = None

    class TranslationConfig:
        columns = {
            'transactionId': 'transaction',
            'username': 'username',
            'moduleName': 'module_name',
            'moduleFuncArgsSpec': 'module_func_args_spec',
            'moduleFuncArgs': 'module_func_args',
            'application': 'application',
            'applicationName': 'application_name',
            'data': 'data',
            'message': 'message',
            'detail': 'detail',
            'startTime': 'start_time',
            'endTime': 'end_time',
            'timeDiff': 'time_diff',
            'userNotFound': 'user_not_found',
            'ignored': 'ignored',
            'warning': 'warning',
            'status': 'status',
            'action': 'action',
            'consultedBy': 'consulted_by',
            'removedBy': 'removed_by',
            'createdAt': 'created_at',
        }

        data = {
            'status': {
                'done': 'done',
                'error': 'error',
                'ignored': 'ignored',
                'user_not_found': 'user_not_found',
            },
            'action': {
                'consult': 'consult',
                'remove': 'remove',
            }
        }

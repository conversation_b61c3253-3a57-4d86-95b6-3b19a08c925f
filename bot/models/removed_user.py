from typing import Any
from pymongo import IndexModel, ASCENDING

from lib.base_model import BaseModel
from lib.active_directory import ADUser


class RemovedUser(BaseModel):
    username: str
    ad_user: ADUser | None
    data: list[dict[str, Any]]
    time: float = 0.0
    catalogNumber: str = ''
    errorCount: int = 0
    warningCount: int = 0

    class Settings:
        indexes = [
            # Expire after 3 days
            IndexModel(keys=[('createdAtUTC', ASCENDING)], expireAfterSeconds=259200),
        ]

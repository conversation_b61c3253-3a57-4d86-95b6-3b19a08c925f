from typing import Literal
from pymongo import IndexModel, ASCENDING

from lib.base_model import BaseModel
from dtos.consulted_user_dto import ConsultedUserResponseDto
from dtos.removed_user_dto import RemovedUserResponseDto


class Transaction(BaseModel):
    usernames: list[str]
    action: Literal['consult', 'remove'] = 'consult'
    state: Literal['progress', 'done', 'error'] = 'progress'
    message: str
    progress: float = 0.0
    consultedUsers: list[ConsultedUserResponseDto] = []
    removedUsers: list[RemovedUserResponseDto] = []
    createdBy: str

    class Settings:
        indexes = [
            # Expire after 3 days
            IndexModel(keys=[('updatedAtUTC', ASCENDING)], expireAfterSeconds=259200),
        ]

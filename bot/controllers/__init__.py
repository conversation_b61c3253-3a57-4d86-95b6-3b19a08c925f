from .auth_controller import Auth<PERSON><PERSON>roll<PERSON>
from .automation_controller import <PERSON>mationControll<PERSON>
from .consulted_user_controller import ConsultedUserController
from .health_controller import HealthController
from .idm_log_controller import IDMLogController
from .log_controller import LogController
from .removed_user_controller import Removed<PERSON>ser<PERSON><PERSON>roller
from .transaction_controller import TransactionController

controllers = [
    AutomationController,
    ConsultedUserController,
    TransactionController,
    AuthController,
    HealthController,
    RemovedUserController,
    LogController,
    IDMLogController,
]

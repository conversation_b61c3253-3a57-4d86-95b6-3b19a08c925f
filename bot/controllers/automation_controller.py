from starlette.background import BackgroundTask

from dtos.automation_dto import RemoveUserRequestDto, RemoveUsersRequestDto
from dtos.transaction_dto import TransactionResponseDto
from lib import config
from lib.auth import AuthRole, auth
from lib.base_controller import <PERSON><PERSON><PERSON><PERSON><PERSON>, get, post
from lib.responses import DtoResponse, ErrorResponse
from lib.validator.error_validator import ErrorValidator
from services import AutomationService, TransactionService
from validators.automation_validator import AutomationValidator


class AutomationController(BaseController):
    """Automation endpoints."""

    @auth(AuthRole.CONSULT)
    @get('/automation/consult_user/{username:str}')
    async def consult_user(
        self,
        username: str,
        automation_service: AutomationService,
        transaction_service: TransactionService,
    ) -> DtoResponse[TransactionResponseDto] | ErrorResponse:
        """Consult the user on multiple applications.

        Parameters
        ----------
        username : str
            Username to consult.
        automation_service : AutomationService
            Injected dependency of the automation service.
        transaction_service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        DtoResponse[TransactionResponseDto]
            Transaction details.
        ErrorResponse
            Error response containing the list of error messages.

        """
        error = ErrorValidator()
        transaction = None
        try:
            if not username:
                error.add('common', 'El nombre de usuario es requerido.')
                return ErrorResponse(error, 400)
            if not isinstance(username, str):
                error.add('common', 'El nombre de usuario no es válido.')
                return ErrorResponse(error, 400)
            username = username.strip().upper()
            transaction = await transaction_service.create(
                usernames=[username],
                message=f'Iniciando consulta del usuario "{username}"...',
                creator=self.request.user.username,
            )
            bg_task = BackgroundTask(
                func=automation_service.consult_users,
                usernames=username,
                transaction=transaction,
            )
            return DtoResponse[TransactionResponseDto](
                TransactionResponseDto.from_orm(transaction),
                background=bg_task,
            )
        except Exception:
            if transaction:
                await transaction_service.discard_transaction(transaction)
            raise

    @auth(AuthRole.CONSULT)
    @post('/automation/consult_users')
    async def consult_users(
        self,
        validator: AutomationValidator,
        transaction_service: TransactionService,
    ) -> DtoResponse[TransactionResponseDto] | ErrorResponse:
        """Consult users on multiple applications.

        Parameters
        ----------
        validator : AutomationValidator
            Injected dependency of the automation validator.
        transaction_service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        DtoResponse[TransactionResponseDto]
            Transaction details.
        ErrorResponse
            Error response containing the list of error messages.

        """
        transaction = None
        try:
            form = await self.request.form()
            if not await validator.validate(form):
                return ErrorResponse(validator.error)
            if not await validator.validate_upload():
                return ErrorResponse(validator.error)
            if len(validator.usernames) == 1:
                initial_message = f'Iniciando consulta del usuario "{validator.usernames[0]}"...'
            else:
                initial_message = 'Iniciando consulta de los usuarios...'
            transaction = await transaction_service.create(
                usernames=validator.usernames,
                message=initial_message,
                creator=self.request.user.username,
            )
            bg_task = BackgroundTask(
                func=validator.service.consult_users,
                usernames=validator.usernames,
                transaction=transaction,
            )
            return DtoResponse[TransactionResponseDto](
                TransactionResponseDto.from_orm(transaction),
                background=bg_task,
            )
        except Exception:
            if transaction:
                await transaction_service.discard_transaction(transaction)
            raise

    @auth(AuthRole.REMOVE)
    @post('/automation/remove_user/idm')
    async def remove_user(
        self,
        data: RemoveUserRequestDto,
        service: AutomationService,
        transaction_service: TransactionService,
    ) -> DtoResponse[TransactionResponseDto] | ErrorResponse:
        """Remove a user on multiple applications and send
        the result to IDM API.

        Parameters
        ----------
        data : RemoveUsersRequestDto
            List of users to be removed and
            the target applications.
        service : AutomationService
            Injected dependency of the automation service.
        transaction_service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        DtoResponse[TransactionResponseDto]
            Transaction details.
        ErrorResponse
            Error response containing the list of error messages.

        """
        if not config.ENABLE_BOT_RETIROS:
            error = ErrorValidator()
            error.add('common', 'Retiro de usuarios no disponible.')
            return ErrorResponse(error, 403)

        transaction = None
        try:
            initial_message = (
                f'Iniciando retiro del usuario "{data.username}"...'
            )
            transaction = await transaction_service.create(
                usernames=[data.username],
                message=initial_message,
                creator='IDM',
                action='remove',
            )
            bg_task = BackgroundTask(
                func=service.remove_user_and_send_to_idm,
                data=data,
                transaction=transaction,
            )
            return DtoResponse[TransactionResponseDto](
                TransactionResponseDto.from_orm(transaction),
                background=bg_task,
            )
        except Exception:
            if transaction:
                await transaction_service.discard_transaction(transaction)
            raise

    @auth(AuthRole.REMOVE)
    @post('/automation/remove_users')
    async def remove_users(
        self,
        data: RemoveUsersRequestDto,
        service: AutomationService,
        transaction_service: TransactionService,
    ) -> DtoResponse[TransactionResponseDto] | ErrorResponse:
        """Remove users on multiple applications.

        Parameters
        ----------
        data : RemoveUsersRequestDto
            List of users to be removed and
            the target applications.
        service : AutomationService
            Injected dependency of the automation service.
        transaction_service : TransactionService
            Injected dependency of the transactions service.

        Returns
        -------
        DtoResponse[TransactionResponseDto]
            Transaction details.
        ErrorResponse
            Error response containing the list of error messages.

        """
        if not config.ENABLE_BOT_RETIROS:
            error = ErrorValidator()
            error.add('common', 'Retiro de usuarios no disponible.')
            return ErrorResponse(error, 403)

        transaction = None
        try:
            usernames = list(data.users.keys())
            if len(usernames) == 1:
                initial_message = (
                    f'Iniciando retiro del usuario "{usernames[0]}"...'
                )
            else:
                initial_message = 'Iniciando retiro de los usuarios...'
            transaction = await transaction_service.create(
                usernames=usernames,
                message=initial_message,
                creator=self.request.user.username,
                action='remove',
            )
            bg_task = BackgroundTask(
                func=service.remove_users,
                data=data,
                transaction=transaction,
            )
            return DtoResponse[TransactionResponseDto](
                TransactionResponseDto.from_orm(transaction),
                background=bg_task,
            )
        except Exception:
            if transaction:
                await transaction_service.discard_transaction(transaction)
            raise

from dtos.health_dto import HealthResponseDto
from lib import config
from lib.base_controller import BaseController, get
from lib.responses import DtoResponse, ErrorResponse


class HealthController(BaseController):
    """Server health endpoints."""

    @get('/health')
    async def health(self) -> DtoResponse[HealthResponseDto] | ErrorResponse:
        """Server health endpoint.

        Returns
        -------
        DtoResponse[HealthResponseDto]
            Server health details.
        ErrorResponse
            Error response containing the list of error messages.

        """
        health_data = HealthResponseDto(
            status=config.health.status,
            message=config.health.error_message or 'OK',
            authenticated=self.request.user.is_authenticated,
            enableBotRetiros=config.ENABLE_BOT_RETIROS,
        )
        return DtoResponse[HealthResponseDto](health_data)

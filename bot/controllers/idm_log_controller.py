import json

from dtos.idm_log_dto import IDMLogResponseDto
from lib import config
from lib.auth import Auth<PERSON><PERSON>, auth
from lib.base_controller import BaseController, get
from lib.responses import (
    DtoResponse,
    ErrorResponse,
    PaginationResponse,
)
from lib.validator.error_validator import <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>
from services import IDMLogService


class IDMLogController(BaseController):
    """IDM API logs management endpoints."""

    @auth(AuthRole.CONSULT)
    @get('/idm-logs')
    async def get_all(
        self, service: IDMLogService
    ) -> PaginationResponse[IDMLogResponseDto] | ErrorResponse:
        """Return a paginated list of logs.

        Parameters
        ----------
        service : IDMResponseLogService
            Injected dependency of the logs service.

        Returns
        -------
        PaginationResponse[IDMLogResponseDto]
            Paginated list of logs.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get('limit', config.ROWS_PER_PAGE)
        pagination = await service.list_all(int(page), int(limit), filter_data)
        return PaginationResponse[IDMLogResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/idm-logs/by_transaction/{id:str}')
    async def get_logs_by_transaction(
        self, id: str, service: IDMLogService
    ) -> PaginationResponse[IDMLogResponseDto] | ErrorResponse:
        """Return a paginated list of logs of a transaction.

        Parameters
        ----------
        service : IDMResponseLogService
            Injected dependency of the logs service.

        Returns
        -------
        PaginationResponse[IDMLogResponseDto]
            Paginated list of logs.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get('limit', config.ROWS_PER_PAGE)
        pagination = await service.get_logs_by_transaction_id(
            id, int(page), int(limit), filter_data
        )
        return PaginationResponse[IDMLogResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/idm-logs/{id:str}')
    async def retrieve(
        self, id: str, service: IDMLogService
    ) -> DtoResponse[IDMLogResponseDto] | ErrorResponse:
        """Finds a log by id.

        Parameters
        ----------
        id : str
            Log id.
        service : IDMResponseLogService
            Injected dependency of the logs service.

        Returns
        -------
        DtoResponse[IDMLogResponseDto]
            Log details.
        ErrorResponse
            Generic error.

        """
        log_data = await service.retrieve(id)
        if not log_data:
            error = ErrorValidator()
            error.add('common', 'Reporte no encontrado.')
            return ErrorResponse(error, 404)
        return DtoResponse[IDMLogResponseDto](log_data)

import json
import typing

from dtos.log_dto import LogResponseDto
from lib import config
from lib.auth import AuthR<PERSON>, auth
from lib.base_controller import BaseController, ExportCallback, get
from lib.responses import (
    DtoResponse,
    ErrorResponse,
    MediaResponse,
    PaginationResponse,
)
from lib.validator.error_validator import ErrorValidator
from services import LogService


class LogController(BaseController):
    """Logs management endpoints."""

    @auth(AuthRole.CONSULT)
    @get('/logs')
    async def get_all(
        self, service: LogService
    ) -> PaginationResponse[LogResponseDto] | ErrorResponse:
        """Return a paginated list of logs.

        Parameters
        ----------
        service : LogService
            Injected dependency of the logs service.

        Returns
        -------
        PaginationResponse[LogResponseDto]
            Paginated list of logs.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get(
            'limit', config.ROWS_PER_PAGE
        )
        pagination = await service.list(int(page), int(limit), filter_data)
        return PaginationResponse[LogResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/logs/{id:str}')
    async def retrieve(
        self, id: str, service: LogService
    ) -> DtoResponse[LogResponseDto] | ErrorResponse:
        """Finds a log by id.

        Parameters
        ----------
        id : str
            Log id.
        service : LogService
            Injected dependency of the logs service.

        Returns
        -------
        DtoResponse[LogResponseDto]
            Log details.
        ErrorResponse
            Generic error.

        """
        log_data = await service.retrieve(id)
        if not log_data:
            error = ErrorValidator()
            error.add('common', 'Reporte no encontrado.')
            return ErrorResponse(error, 404)
        return DtoResponse[LogResponseDto](log_data)

    @auth(AuthRole.CONSULT)
    @get('/logs/export/{extension:str}/{filename:str}')
    async def export(
        self, extension: str, filename: str, service: LogService
    ) -> MediaResponse | ErrorResponse:
        """Exports the logs to JSON or XLSX.

        Parameters
        ----------
        extension : str
            Output file's extension.
        filename : str
            Filename.
        service : UserService
            Injected dependency of the users service.

        Returns
        -------
        MediaResponse
            Data of the media file.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        return await self._export(
            typing.cast(ExportCallback, service.export),
            extension,
            filename,
            *self._get_export_parameters(config.EXPORT_LIMIT),
            filter_data,
        )

import json

from dtos.log_dto import LogResponseDto
from lib import config
from lib.auth import AuthRole, auth
from lib.base_controller import BaseController, get
from lib.responses import (
    DtoResponse,
    ErrorResponse,
    PaginationResponse,
)
from lib.validator.error_validator import ErrorValidator
from services import LogService


class LogController(BaseController):
    """Logs management endpoints."""

    @auth(AuthRole.CONSULT)
    @get('/logs')
    async def get_all(
        self, service: LogService
    ) -> PaginationResponse[LogResponseDto] | ErrorResponse:
        """Return a paginated list of logs.

        Parameters
        ----------
        service : LogService
            Injected dependency of the logs service.

        Returns
        -------
        PaginationResponse[LogResponseDto]
            Paginated list of logs.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get('limit', config.ROWS_PER_PAGE)
        pagination = await service.list_all(int(page), int(limit), filter_data)
        return PaginationResponse[LogResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/logs/by_transaction/{id:str}')
    async def get_logs_by_transaction(
        self, id: str, service: LogService
    ) -> PaginationResponse[LogResponseDto] | ErrorResponse:
        """Return a paginated list of logs of a transaction.

        Parameters
        ----------
        service : LogService
            Injected dependency of the logs service.

        Returns
        -------
        PaginationResponse[LogResponseDto]
            Paginated list of logs.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get('limit', config.ROWS_PER_PAGE)
        pagination = await service.get_logs_by_transaction_id(
            id, int(page), int(limit), filter_data
        )
        return PaginationResponse[LogResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/logs/{id:str}')
    async def retrieve(
        self, id: str, service: LogService
    ) -> DtoResponse[LogResponseDto] | ErrorResponse:
        """Finds a log by id.

        Parameters
        ----------
        id : str
            Log id.
        service : LogService
            Injected dependency of the logs service.

        Returns
        -------
        DtoResponse[LogResponseDto]
            Log details.
        ErrorResponse
            Generic error.

        """
        log_data = await service.retrieve(id)
        if not log_data:
            error = ErrorValidator()
            error.add('common', 'Reporte no encontrado.')
            return ErrorResponse(error, 404)
        return DtoResponse[LogResponseDto](log_data)

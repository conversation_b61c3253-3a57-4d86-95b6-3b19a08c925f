import json

from dtos.consulted_user_dto import ConsultedUserResponseDto
from lib import config
from lib.auth import AuthR<PERSON>, auth
from lib.base_controller import BaseController, get
from lib.responses import (
    DtoResponse,
    ErrorResponse,
    MediaResponse,
    PaginationResponse,
)
from lib.validator.error_validator import ErrorValidator
from services.consulted_user_service import ConsultedUserService


class ConsultedUserController(BaseController):
    """Consulted users endpoints."""

    @auth(AuthRole.CONSULT)
    @get('/consulted_users')
    async def list(
        self, service: ConsultedUserService
    ) -> PaginationResponse[ConsultedUserResponseDto] | ErrorResponse:
        """Return a paginated list of consulted users.

        Parameters
        ----------
        service : ConsultedUserService
            Injected dependency of the consulted users service.

        Returns
        -------
        PaginationResponse[ConsultedUserResponseDto]
            Paginated list of consulted users.
        ErrorResponse
            Error response containing the list of error messages.

        """
        filter_data = self.request.query_params.get('q')
        filter_data = json.loads(filter_data) if filter_data else None
        page = self.request.query_params.get('page', 1)
        limit = self.request.query_params.get(
            'limit', config.ROWS_PER_PAGE
        )
        pagination = await service.list(int(page), int(limit), filter_data)
        return PaginationResponse[ConsultedUserResponseDto](pagination)

    @auth(AuthRole.CONSULT)
    @get('/consulted_users/{id:str}')
    async def retrieve(
        self, id: str, service: ConsultedUserService
    ) -> DtoResponse[ConsultedUserResponseDto] | ErrorResponse:
        """Find a consulted user by id.

        Parameters
        ----------
        id : str
            Consulted user id.
        service : ConsultedUserService
            Injected dependency of the consulted users service.

        Returns
        -------
        DtoResponse[ConsultedUserResponseDto]
            Consulted user details.
        ErrorResponse
            Generic error.

        """
        consulted_user = await service.retrieve(id)
        if not consulted_user:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido consultado.')
            return ErrorResponse(error, 404)
        return DtoResponse[ConsultedUserResponseDto](consulted_user)

    @auth(AuthRole.CONSULT)
    @get('/consulted_users/username/{username:str}')
    async def retrieve_by_username(
        self, username: str, service: ConsultedUserService
    ) -> DtoResponse[ConsultedUserResponseDto] | ErrorResponse:
        """Find a consulted user by username.

        Parameters
        ----------
        username : str
            Consulted user username.
        service : ConsultedUserService
            Injected dependency of the consulted users service.

        Returns
        -------
        DtoResponse[ConsultedUserResponseDto]
            Consulted user details.
        ErrorResponse
            Generic error.

        """
        consulted_user = await service.retrieve_by_username(username)
        if not consulted_user:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido consultado.')
            return ErrorResponse(error, 404)
        return DtoResponse[ConsultedUserResponseDto](consulted_user)

    @auth(AuthRole.CONSULT)
    @get('/consulted_users/{id:str}/export')
    async def export(
        self, id: str, service: ConsultedUserService
    ) -> MediaResponse | ErrorResponse:
        """Find a consulted user by id and exports its data.

        Parameters
        ----------
        id : str
            Consulted user id.
        service : ConsultedUserService
            Injected dependency of the consulted users service.

        Returns
        -------
        MediaResponse
            Streams a file content.
        ErrorResponse
            Generic error.

        """
        file = await service.export(id)
        if not file:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido consultado.')
            return ErrorResponse(error, 404)
        return MediaResponse(
            file.stream,
            headers=file.headers,
            media_type=file.media_type,
        )

    @auth(AuthRole.CONSULT)
    @get('/consulted_users/username/{username:str}/export')
    async def export_by_username(
        self, username: str, service: ConsultedUserService
    ) -> MediaResponse | ErrorResponse:
        """Find a consulted user by username and exports its data.

        Parameters
        ----------
        username : str
            Consulted user username.
        service : ConsultedUserService
            Injected dependency of the consulted users service.

        Returns
        -------
        MediaResponse
            Streams a file content.
        ErrorResponse
            Generic error.

        """
        file = await service.export_by_username(username)
        if not file:
            error = ErrorValidator()
            error.add('common', 'El usuario no ha sido consultado.')
            return ErrorResponse(error, 404)
        return MediaResponse(
            file.stream,
            headers=file.headers,
            media_type=file.media_type,
        )

from urllib.parse import urlparse

from starlette.responses import HTMLResponse, JSONResponse, RedirectResponse

from lib import config
from lib.base_controller import BaseController, get
from lib.responses import ErrorResponse
from services.auth_service import AuthService


class AuthController(BaseController):
    """Authentication endpoints."""

    @get('/login')
    async def login(
        self, service: AuthService
    ) -> HTMLResponse | RedirectResponse | ErrorResponse:
        """Login endpoint.

        Parameters
        ----------
        service : AuthService
            Injected dependency of the service.

        Returns
        -------
        HTMLResponse
            Response containing the SEUS login form.
        RedirectResponse
            Response containing the session token.
        ErrorResponse
            Error response containing the list of error messages.

        """
        referer = self.request.headers.get('referer')
        is_internal_req = (
            config.SERVER_NAME == urlparse(referer).hostname
            if referer
            else False
        )
        return await service.login(self.request.user, is_internal_req)

    @get('/logout')
    async def get_logout_url(
        self, service: AuthService
    ) -> JSONResponse | ErrorResponse:
        """Get the logout URL.

        Parameters
        ----------
        service : AuthService
            Injected dependency of the service.

        Returns
        -------
        JSONResponse
            Logout url.
        ErrorResponse
            Error response containing the list of error messages.

        """
        logout_url = await service.get_logout_url()
        return JSONResponse({'url': logout_url})

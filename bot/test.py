import logging
import unittest
from datetime import datetime
from functools import wraps
from typing import cast

from dotenv import load_dotenv
load_dotenv('../.env')

# ruff: noqa: E402
from dtos.automation_dto import IDMEmployeeResponseDto, IPSARole
from lib.active_directory import ActiveDirectory
from lib.ariba_session import AribaSession
from lib.exceptions import AutomationError
from lib.porfin_retiros_session import PorfinRetirosSession
from lib.porfin_session import PorfinSession
from lib.salesforce import Salesforce
from lib.salud_web_retiros_session import SaludWebRetirosSession
from lib.salud_web_session import SaludWebSession
from lib.saml import SAML
from lib.tools import decorate_all_methods
from modules import (
    AgendaWebModule,
    AribaModule,
    BandejaEscalamientoModule,
    BeyondHealthModule,
    BillingCenterModule,
    CAServiceDeskModule,
    CaseTrackingModule,
    ClaimCenterModule,
    ConfluenceModule,
    ContactManagerModule,
    ConveniosModule,
    EventosAdversosModule,
    HealthCloudModule,
    IDMModule,
    IntegradorModule,
    IPSAModule,
    OfficeModule,
    OHIModule,
    OIPAModule,
    PolicyCenterModule,
    PorfinModule,
    SalesforceModule,
    SaludWebModule,
    SEEEModule,
    SEUSModule,
    SOATModule,
    STARModule,
    TablaTercerosModule,
    ViafirmaModule,
)

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.DEBUG)
saml = SAML()
salesforce = Salesforce()
ariba_session = AribaSession(saml)
salud_web_session = SaludWebSession(saml)
salud_web_retiros_session = SaludWebRetirosSession(saml)
active_directory = ActiveDirectory()
porfin_connection = PorfinSession()
porfin_retiros_connection = PorfinRetirosSession()


def handle_automation_error(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        try:
            return func(self, *args, **kwargs)
        except AutomationError as e:
            logger.error(e.get_full_message())
            self.fail(str(e))
    return wrapper


@decorate_all_methods(handle_automation_error)
class ConsultUserTest(unittest.TestCase):
    USERNAME = 'FERNECOR'
    """Testing user's username."""

    DOCUMENT = '*********'
    """Testing user's document."""

    FIRST_NAME = 'Ferney Andres'
    """User's first name to be consulted."""

    LAST_NAME = 'Echeverri Ortiz'
    """User's last name to be consulted."""

    FULLNAME = 'Ferney Andres Echeverri Ortiz'
    """Testing user's fullname."""

    EMAIL = '<EMAIL>'
    """Testing user's email."""

    OIPA_USERNAME = 'ANACRESE'
    """Testing OIPA user's username."""

    CONFLUENCE_FULLNAME = 'Ferney Andres Echeverri'
    """User's fullname to be consulted on Confluence."""

    SALESFORCE_USERNAME = 'YELEGACE'
    """Testing Salesforce user's username."""

    IDM_USERNAME = 'YELEGACE'
    """Testing IDM user's username."""

    @unittest.skip('')
    def test_active_directory(self):
        ad_user = active_directory.search_user(self.USERNAME)
        logger.debug(ad_user)
        self.assertIsNotNone(ad_user)
        user_principal_name = '<EMAIL>'
        if ad_user:
            self.assertEqual(ad_user.user_principal_name, user_principal_name)

    @unittest.skip('')
    def test_agenda_web(self):
        module = AgendaWebModule(saml)
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        ROLE = '408 - APROVISIONAMIENTO DE USUARIOS'
        self.assertEqual(ROLE, response.role)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_ariba(self):
        module = AribaModule(ariba_session)
        response = module.get_user_data(self.USERNAME)
        module._logout()
        logger.debug(response)
        GROUP_NAME = 'Aprovisionamiento de usuarios'
        self.assertIn(GROUP_NAME, response.groups)

    @unittest.skip('')
    def test_bandeja_escalamiento(self):
        module = BandejaEscalamientoModule(saml)
        response = module.get_user_role(self.USERNAME)
        logger.debug(response)
        ROLE = 'RT_APEX_A_APROVISIONAR_USUARIOS'
        self.assertEqual(ROLE, response.role)

    @unittest.skip('')
    def test_beyond_health(self):
        module = BeyondHealthModule(saml)
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        one_of_the_user_roles = 'RT_BH_F_CREACION USUARIOS'
        self.assertIn(one_of_the_user_roles, response.roles)

    @unittest.skip('')
    def test_billing_center(self):
        module = BillingCenterModule(saml)
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_ca_service_desk(self):
        module = CAServiceDeskModule()
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        self.assertTrue(response.active)
        one_of_user_roles = 'INV-Analista Nivel 3'
        self.assertIn(one_of_user_roles, response.roles)

    @unittest.skip('')
    def test_case_tracking(self):
        module = CaseTrackingModule()
        response = module.get_user_data(self.FULLNAME)
        logger.debug(response)
        CATEGORY_NAME = 'RT_CASET_F_Gestión de Accesos'
        self.assertEqual(response.category, CATEGORY_NAME)

    @unittest.skip('')
    def test_claim_center(self):
        module = ClaimCenterModule(saml)
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_confluence(self):
        module = ConfluenceModule()
        response = module.get_user_data(self.CONFLUENCE_FULLNAME)
        logger.debug(response)
        one_of_user_groups = 'confluence-users'
        self.assertIn(one_of_user_groups, response.groups)

    @unittest.skip('')
    def test_contact_manager(self):
        module = ContactManagerModule(saml)
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_convenios(self):
        module = ConveniosModule(saml)
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        ROLE = '454 - RT_CVEPS_P_IMPLANTACIONES'
        self.assertEqual(ROLE, response.role)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_eventos_adversos(self):
        module = EventosAdversosModule()
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        one_of_user_groups = 'Administrator'
        self.assertIn(one_of_user_groups, response.groups)

    @unittest.skip('')
    def test_health_cloud(self):
        module = HealthCloudModule(salesforce, )
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        user_license = 'Salesforce'
        self.assertEqual(response.license, user_license)

    @unittest.skip('')
    def test_idm(self):
        module = IDMModule()
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        boss_document = '********'
        self.assertEqual(boss_document, response.boss_document)
        response = module.get_user_data(self.IDM_USERNAME)
        logger.debug(response)
        payroll_code = '52660'
        self.assertEqual(
            payroll_code, cast(IDMEmployeeResponseDto, response).payroll_code
        )

    @unittest.skip('')
    def test_integrador(self):
        module = IntegradorModule(saml)
        response = module.get_user_role(self.DOCUMENT)
        logger.debug(response)
        user_role = '8 - Soporte'
        self.assertEqual(user_role, response.role)

    @unittest.skip('')
    def test_ipsa(self):
        module = IPSAModule(saml)
        response = module.get_user_roles(self.DOCUMENT)
        logger.debug(response)
        role = IPSARole(
            branch='(100) PUNTO DE VISTA CARACAS',
            position='(15) Analista de Soporte',
            role='(17) CREACION DE USUARIOS',
            status='(1) ACTIVO',
        )
        self.assertIn(role, response.roles)

    @unittest.skip('')
    def test_office_365(self):
        module = OfficeModule()
        response = module.get_user_data(self.FIRST_NAME, self.LAST_NAME)
        logger.debug(response)
        LICENSE_NAME = 'STANDARDPACK'
        self.assertIn(LICENSE_NAME, response.licenses)

    @unittest.skip('')
    def test_ohi(self):
        module = OHIModule()
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        role = 'RT_OHI_A_GIGA'
        self.assertIn(role, response.roles)

    @unittest.skip('')
    def test_oipa(self):
        module = OIPAModule()
        response = module.get_user_data(self.OIPA_USERNAME)
        logger.debug(response)
        user_company = 'SURAMERICANA'
        self.assertEqual(user_company, response.company)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_policy_center(self):
        module = PolicyCenterModule(saml)
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_porfin(self):
        module = PorfinModule(porfin_connection, porfin_retiros_connection)
        response = module.get_user_data(self.USERNAME)
        module._logout()
        logger.debug(response)
        one_of_user_roles = 'Aplica solo para Gestion de Accesos - Mesa Ayuda'
        self.assertIn(one_of_user_roles, response.roles)

    @unittest.skip('')
    def test_salesforce(self):
        module = SalesforceModule(salesforce, )
        response = module.get_user_data(self.SALESFORCE_USERNAME)
        logger.debug(response)
        user_license = 'Salesforce'
        self.assertEqual(response.license, user_license)

    @unittest.skip('')
    def test_salud_web(self):
        module = SaludWebModule(salud_web_session, salud_web_retiros_session)
        response = module.get_user_data(self.USERNAME)
        module._logout()
        logger.debug(response)
        ROLE = '567 - CONSULTAS + ADMINISTRACION DE PRIVILEGIOS'
        self.assertEqual(ROLE, response.role)
        self.assertTrue(response.active)

    @unittest.skip('')
    def test_seee(self):
        module = SEEEModule(saml)
        response = module.get_user_data(self.DOCUMENT)
        logger.debug(response)
        one_of_user_offices = '004'
        self.assertIn(one_of_user_offices, response.offices)

    @unittest.skip('')
    def test_seus(self):
        module = SEUSModule()
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        repository = 'AD Empleados'
        self.assertEqual(repository, response.repository)

    @unittest.skip('')
    def test_soat(self):
        module = SOATModule(saml)
        response = module.get_user_data(self.DOCUMENT)
        logger.debug(response)
        one_of_user_channels = 'GERENCIA NACIONAL SOAT'
        self.assertTrue(response.active)
        self.assertIn(one_of_user_channels, response.channels)

    @unittest.skip('')
    def test_star(self):
        module = STARModule(saml)
        response = module.get_user_data(self.FULLNAME)
        logger.debug(response)
        user_role = 'PREAJUSTADOR'
        self.assertEqual(response.role, user_role)

    @unittest.skip('')
    def test_tabla_terceros(self):
        module = TablaTercerosModule(saml)
        response = module.get_user_data(self.DOCUMENT)
        logger.debug(response)
        user_discharge_date = '2021-05-15 00:00:00.0'
        self.assertEqual(user_discharge_date, response.discharge_date)

    @unittest.skip('')
    def test_viafirma(self):
        module = ViafirmaModule()
        response = module.get_user_data(self.USERNAME)
        logger.debug(response)
        self.assertTrue(response.active)
        ROLE = 'Administrador global'
        self.assertEqual(response.role, ROLE)


@decorate_all_methods(handle_automation_error)
class RemoveUserTest(unittest.TestCase):
    USERNAME = 'FERNECOR'
    """Testing user's username."""

    DOCUMENT = '*********'
    """Testing user's document."""

    FIRST_NAME = 'Ferney Andres'
    """User's first name to be consulted."""

    LAST_NAME = 'Echeverri Ortiz'
    """User's last name to be consulted."""

    FULLNAME = 'Ferney Andres Echeverri Ortiz'
    """Testing user's fullname."""

    EMAIL = '<EMAIL>'
    """Testing user's email."""

    SALESFORCE_USERNAME = 'YELEGACE'
    """Testing Salesforce user's username."""

    IPSA_REMOVE_USER_DOCUMENT = '1144067975'
    """Username to test user roles deletion on IPSA."""

    CASE_TRACKING_FULLNAME = 'Test User'
    """Fullname to test user deletion on Case Tracking."""

    OIPA_USERNAME = 'GLADROAG'
    """Testing OIPA user's username."""

    VIAFIRMA_USERNAME = 'testuser'
    """Testing Viafirma user's username."""

    @unittest.skip('')
    def test_agenda_web(self):
        module = AgendaWebModule(saml)
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.active)
        REMOVED_USER_ROLE = '0 - GUEST'
        self.assertEqual(REMOVED_USER_ROLE, response.role)

    @unittest.skip('')
    def test_beyond_health(self):
        module = BeyondHealthModule(saml)
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.warning)
        self.assertGreater(len(response.removed_roles), 0)

    @unittest.skip('')
    def test_case_tracking(self):
        module = CaseTrackingModule()
        response = module.remove_user(self.CASE_TRACKING_FULLNAME)
        logger.debug(response)
        self.assertFalse(response.warning)

    @unittest.skip('')
    def test_contact_manager(self):
        module = ContactManagerModule(saml)
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.active)
        self.assertTrue(response.locked)
        self.assertGreater(len(response.removed_roles), 0)

    @unittest.skip('')
    def test_convenios(self):
        module = ConveniosModule(saml)
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.active)
        REMOVED_USER_ROLE = '0 - GUEST'
        self.assertEqual(REMOVED_USER_ROLE, response.role)

    @unittest.skip('')
    def test_eventos_adversos(self):
        module = EventosAdversosModule()
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.enabled)

    @unittest.skip('')
    def test_health_cloud(self):
        module = HealthCloudModule(salesforce, )
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.warning)

    @unittest.skip('')
    def test_integrador(self):
        module = IntegradorModule(saml)
        response = module.remove_user(self.DOCUMENT)
        logger.debug(response)
        self.assertFalse(response.warning)

    @unittest.skip('')
    def test_ipsa(self):
        module = IPSAModule(saml)
        response = module.remove_user(self.IPSA_REMOVE_USER_DOCUMENT)
        logger.debug(response)
        self.assertFalse(response.warning)
        self.assertGreater(len(response.removed_roles), 0)

    @unittest.skip('')
    def test_office_365(self):
        module = OfficeModule()
        response = module.remove_user(self.FIRST_NAME, self.LAST_NAME)
        logger.debug(response)
        LICENSE_NAME = 'STANDARDPACK'
        self.assertIn(LICENSE_NAME, response.removed_licenses)

    @unittest.skip('')
    def test_ohi(self):
        module = OHIModule()
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.active)

    @unittest.skip('')
    def test_oipa(self):
        module = OIPAModule()
        response = module.remove_user(self.OIPA_USERNAME)
        logger.debug(response)
        self.assertFalse(response.active)

    @unittest.skip('')
    def test_porfin(self):
        module = PorfinModule(porfin_connection, porfin_retiros_connection)
        response = module.remove_user(self.USERNAME)
        module._logout(use_retiros_user=True)
        logger.debug(response)
        self.assertFalse(response.active)

    @unittest.skip('')
    def test_salesforce(self):
        module = SalesforceModule(salesforce, )
        response = module.remove_user(self.SALESFORCE_USERNAME)
        logger.debug(response)
        self.assertFalse(response.active)

    @unittest.skip('')
    def test_salud_web(self):
        module = SaludWebModule(salud_web_session, salud_web_retiros_session)
        response = module.remove_user(self.USERNAME)
        module._logout(use_retiros_user=True)
        logger.debug(response)
        self.assertFalse(response.active)
        REMOVED_USER_ROLE = '0 - GUEST'
        self.assertEqual(REMOVED_USER_ROLE, response.role)

    @unittest.skip('')
    def test_seus(self):
        module = SEUSModule()
        response = module.remove_user(self.USERNAME)
        logger.debug(response)
        self.assertFalse(response.warning)

    @unittest.skip('')
    def test_tabla_terceros(self):
        module = TablaTercerosModule(saml)
        response = module.remove_user(self.DOCUMENT)
        logger.debug(response)
        LEAVING_DATE = datetime.today().strftime('%Y-%m-%d')
        self.assertFalse(response.warning)
        self.assertEqual(LEAVING_DATE, response.leaving_date)

    @unittest.skip('')
    def test_viafirma(self):
        module = ViafirmaModule()
        response = module.remove_user(self.VIAFIRMA_USERNAME)
        logger.debug(response)
        self.assertFalse(response.warning)


if __name__ == '__main__':
    unittest.main()

from datetime import datetime

from pydantic import SerializeAsAny, field_validator

from lib.base_dto import BaseDto, BaseRequestDto, BaseResponseDto


class IPSARole(BaseDto):
    branch: str
    position: str
    role: str
    status: str


class PorfinApplication(BaseDto):
    product: str
    code: str
    description: str


class RemoveUserDataRequestDto(BaseRequestDto):
    applications: list[str] = []
    catalogNumber: str = 'N/A'


class RemoveUsersRequestDto(BaseRequestDto):
    users: dict[str, RemoveUserDataRequestDto]


class RemoveUserRequestDto(RemoveUserDataRequestDto):
    username: str

    @field_validator('username')
    @classmethod
    def username_uppercase(cls, v: str) -> str:
        return v.upper()


class ApplicationDataResponseDto(BaseResponseDto):
    pass


class ConsultedUserDataResponseDto(ApplicationDataResponseDto):
    """Application response DTO class
    must inherit from this class.
    """

    @property
    def has_privileges(self) -> bool:
        """Checks if user has privileges."""

        return True


class RemovedUserDataResponseDto(ApplicationDataResponseDto):
    """Removed user response DTO class
    must inherit from this class.
    """

    message: str
    warning: bool = False


class AgendaWebResponseDto(ConsultedUserDataResponseDto):
    active: bool
    role: str

    @property
    def has_privileges(self) -> bool:
        REMOVED_USER_ROLE = '0 - GUEST'
        return self.active or self.role != REMOVED_USER_ROLE


class AgendaWebRemovedUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    role: str


class AribaResponseDto(ConsultedUserDataResponseDto):
    active: bool
    locked: bool
    groups: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or not self.locked or len(self.groups) > 0


class BandejaEscalamientoResponseDto(ConsultedUserDataResponseDto):
    role: str


class BeyondHealthResponseDto(ConsultedUserDataResponseDto):
    active: bool
    locked: bool
    roles: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or not self.locked or len(self.roles) > 0


class BeyondHealthRemovedUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    locked: bool
    removed_roles: list[str]


class BillingCenterResponseDto(ConsultedUserDataResponseDto):
    active: bool
    locked: bool
    roles: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or not self.locked or len(self.roles) > 0


class CAServiceDeskResponseDto(ConsultedUserDataResponseDto):
    active: bool
    roles: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or len(self.roles) > 0


class CaseTrackingResponseDto(ConsultedUserDataResponseDto):
    category: str
    permission_codes: list[str]


class CaseTrackingRemovedUserResponseDto(RemovedUserDataResponseDto):
    pass


class ClaimCenterResponseDto(ConsultedUserDataResponseDto):
    active: bool
    locked: bool
    roles: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or not self.locked or len(self.roles) > 0


class ConfluenceResponseDto(ConsultedUserDataResponseDto):
    active: bool
    groups: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or len(self.groups) > 0


class ContactManagerResponseDto(ConsultedUserDataResponseDto):
    active: bool
    locked: bool
    roles: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or not self.locked or len(self.roles) > 0


class ContactManagerRemovedUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    locked: bool
    removed_roles: list[str]


class ConveniosResponseDto(ConsultedUserDataResponseDto):
    active: bool
    role: str

    @property
    def has_privileges(self) -> bool:
        REMOVED_USER_ROLE = '0 - GUEST'
        return self.active or self.role != REMOVED_USER_ROLE


class ConveniosRemovedUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    role: str


class EventosAdversosResponseDto(ConsultedUserDataResponseDto):
    enabled: bool
    groups: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.enabled or len(self.groups) > 0


class EventosAdversosRemovedUserResponseDto(RemovedUserDataResponseDto):
    enabled: bool


class IDMResponseDto(ConsultedUserDataResponseDto):
    user_type: str
    activation_date: str
    expiration_date: str
    boss_document: str


class IDMEmployeeResponseDto(ConsultedUserDataResponseDto):
    user_type: str
    activation_date: str
    expiration_date: str
    boss_document: str
    employee_status: str
    payroll_code: str
    absenteeism_status: str
    roles: list[str]


class IntegradorResponseDto(ConsultedUserDataResponseDto):
    role: str


class IntegradorRemovedUserResponseDto(RemovedUserDataResponseDto):
    pass


class IPSAResponseDto(ConsultedUserDataResponseDto):
    roles: list[IPSARole]

    @property
    def has_privileges(self) -> bool:
        return len(self.roles) > 0


class IPSARemovedUserResponseDto(RemovedUserDataResponseDto):
    removed_roles: list[IPSARole]
    unremoved_roles: list[IPSARole]


class OfficeResponseDto(ConsultedUserDataResponseDto):
    licenses: list[str]

    @property
    def has_privileges(self) -> bool:
        return len(self.licenses) > 0


class OfficeRemovedUserResponseDto(RemovedUserDataResponseDto):
    removed_licenses: list[str]


class OHIResponseDto(ConsultedUserDataResponseDto):
    active: bool
    roles: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or len(self.roles) > 0


class OHIRemovedUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    removed_roles: list[str]


class OIPAResponseDto(ConsultedUserDataResponseDto):
    active: bool
    company: str
    groups: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or len(self.groups) > 0


class OIPARemovedUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    removed_groups: list[str]


class PolicyCenterResponseDto(ConsultedUserDataResponseDto):
    active: bool
    locked: bool
    roles: list[str]
    authorities: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or not self.locked or len(self.roles) > 0 or len(self.authorities) > 0


class PorfinResponseDto(ConsultedUserDataResponseDto):
    active: bool
    roles: list[str]
    applications: list[PorfinApplication]

    @property
    def has_privileges(self) -> bool:
        return self.active


class PorfinRemoveUserResponseDto(RemovedUserDataResponseDto):
    active: bool


class SalesforceResponseDto(ConsultedUserDataResponseDto):
    position: str
    role: str
    profile: str
    license: str
    active: bool
    frozen: bool

    @property
    def has_privileges(self) -> bool:
        return self.active and not self.frozen


class SalesforceRemoveUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    frozen: bool


class SaludWebResponseDto(ConsultedUserDataResponseDto):
    active: bool
    role: str

    @property
    def has_privileges(self) -> bool:
        REMOVED_USER_ROLE = '0 - GUEST'
        return self.active or self.role != REMOVED_USER_ROLE


class SaludWebRemovedUserResponseDto(RemovedUserDataResponseDto):
    active: bool
    role: str


class SEEEResponseDto(ConsultedUserDataResponseDto):
    branches: list[str]
    offices: list[str]

    @property
    def has_privileges(self) -> bool:
        return len(self.branches) > 0 or len(self.offices) > 0


class SEUSResponseDto(ConsultedUserDataResponseDto):
    active: bool
    repository: str
    profiles: list[str]
    roles: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or len(self.profiles) > 0 or len(self.roles) > 0


class SEUSRemovedUserResponseDto(RemovedUserDataResponseDto):
    pass


class SOATResponseDto(ConsultedUserDataResponseDto):
    active: bool
    channels: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or len(self.channels) > 0


class STARResponseDto(ConsultedUserDataResponseDto):
    cas: str
    role: str
    discharge_date: str
    leaving_date: str

    @property
    def has_privileges(self) -> bool:
        if self.leaving_date and self.leaving_date != 'N/A':
            try:
                parsed_datetime = datetime.strptime(self.leaving_date, "%Y/%m/%d")
                has_leaving_date = parsed_datetime > datetime.now()
            except Exception:
                has_leaving_date = True
        else:
            has_leaving_date = False
        return self.cas != 'N/A' or self.role != 'N/A' or has_leaving_date


class TablaTercerosResponseDto(ConsultedUserDataResponseDto):
    discharge_date: str
    leaving_date: str

    @property
    def has_privileges(self) -> bool:
        try:
            if self.leaving_date:
                parsed_datetime = datetime.strptime(self.leaving_date, '%Y-%m-%d %H:%M:%S.%f')
                return parsed_datetime > datetime.now()
            return True
        except Exception:
            return True


class TablaTercerosRemovedUserResponseDto(RemovedUserDataResponseDto):
    leaving_date: str


class ViafirmaResponseDto(ConsultedUserDataResponseDto):
    active: bool
    role: str

    @property
    def has_privileges(self) -> bool:
        return self.active


class ViafirmaRemovedUserResponseDto(RemovedUserDataResponseDto):
    pass


class ApplicationResultResponseDto(BaseResponseDto):
    application: str
    applicationName: str
    data: SerializeAsAny[ApplicationDataResponseDto] | None = None
    error: str | None = None
    errorDetail: str | None = None
    startTime: float = 0.0
    endTime: float = 0.0
    timeDiff: float = 0.0
    userNotFound: bool = False
    ignored: bool = False
    hasRemove: bool = False
    hasPrivileges: bool = False
    warning: str | None = None

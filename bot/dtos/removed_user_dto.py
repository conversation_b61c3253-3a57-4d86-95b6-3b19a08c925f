from typing import Any
from datetime import datetime

from beanie import PydanticObjectId
from pydantic import field_validator

from lib.active_directory import ADUser
from lib.base_dto import BaseResponseDto


class RemovedUserResponseDto(BaseResponseDto):
    id: PydanticObjectId
    username: str
    ad_user: ADUser | None = None
    data: list[dict[str, Any]]
    time: float = 0.0
    catalogNumber: str = ''
    errorCount: int = 0
    warningCount: int = 0
    updatedAt: datetime
    createdAt: datetime

    @field_validator('id')
    @classmethod
    def serialize_id(cls, value: PydanticObjectId):
        return str(value)

import datetime
from typing import Any

from beanie import PydanticObjectId

from lib.base_dto import BaseRequestDto, BaseResponseDto


class IDMLogRequestDto(BaseRequestDto):
    transactionId: PydanticObjectId
    username: str
    content: str
    is_success: bool
    status_code: int
    request_data: dict[str, Any] | None = None


class IDMLogResponseDto(BaseResponseDto):
    id: PydanticObjectId
    transactionId: PydanticObjectId
    username: str
    content: str
    is_success: bool
    status_code: int
    request_data: dict[str, Any] | None = None
    createdAt: datetime.datetime

from typing import Any
from datetime import datetime

from beanie import PydanticObjectId
from pydantic import field_validator

from lib.active_directory import ADUser
from lib.base_dto import BaseResponseDto


class ConsultedUserResponseDto(BaseResponseDto):
    id: PydanticObjectId
    username: str
    exp: datetime
    ad_user: ADUser | None = None
    data: list[dict[str, Any]]
    time: float = 0.0
    errorCount: int = 0
    warningCount: int = 0
    updatedAt: datetime
    createdAt: datetime

    @field_validator('id')
    @classmethod
    def serialize_id(cls, value: PydanticObjectId):
        return str(value)

import typing
import datetime

from beanie import PydanticObjectId

from lib.base_dto import BaseResponseDto
from dtos.consulted_user_dto import ConsultedUserResponseDto
from dtos.removed_user_dto import RemovedUserResponseDto


class TransactionResponseDto(BaseResponseDto):
    id: PydanticObjectId
    usernames: list[str]
    action: typing.Literal['consult', 'remove'] = 'consult'
    state: typing.Literal['progress', 'done', 'error']
    message: str
    progress: float = 0.0
    consultedUsers: list[ConsultedUserResponseDto] = []
    removedUsers: list[RemovedUserResponseDto] = []
    updatedAt: datetime.datetime
    createdAt: datetime.datetime
    createdBy: str


class TransactionProgressResponseDto(BaseResponseDto):
    action: typing.Literal['consult', 'remove'] = 'consult'
    state: typing.Literal['progress', 'done', 'error']
    message: str
    progress: float = 0.0

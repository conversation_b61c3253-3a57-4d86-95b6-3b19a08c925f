from .agenda_web_module import AgendaWebModule
from .ariba_module import AribaModule
from .bandeja_escalamiento_module import BandejaEscalamientoModule
from .beyond_health_module import BeyondHealthModule
from .billing_center_module import BillingCenterModule
from .ca_service_desk_module import CAServiceDeskModule
from .case_tracking_module import CaseTrackingModule
from .claim_center_module import ClaimCenterModule
from .confluence_module import ConfluenceModule
from .contact_manager_module import ContactManagerModule
from .convenios_module import ConveniosModule
from .eventos_adversos_module import EventosAdversosModule
from .health_cloud_module import HealthCloudModule
from .idm_module import IDMModule
from .integrador_module import IntegradorModule
from .ipsa_module import IPSAModule
from .office_module import OfficeModule
from .ohi_module import OHIModule
from .oipa_module import OIPAModule
from .policy_center_module import PolicyCenterModule
from .porfin_module import PorfinModule
from .salesforce_module import SalesforceModule
from .salud_web_module import SaludWebModule
from .seee_module import SEEEModule
from .seus_module import SEUSModule
from .soat_module import SOATModule
from .star_module import STARModule
from .tabla_terceros_module import TablaTercerosModule
from .viafirma_module import ViafirmaModule

modules = [
    AgendaWebModule,
    AribaModule,
    BandejaEscalamientoModule,
    BeyondHealthModule,
    BillingCenterModule,
    CAServiceDeskModule,
    CaseTrackingModule,
    ClaimCenterModule,
    ConfluenceModule,
    ContactManagerModule,
    ConveniosModule,
    EventosAdversosModule,
    HealthCloudModule,
    IDMModule,
    IntegradorModule,
    IPSAModule,
    OfficeModule,
    OHIModule,
    OIPAModule,
    PolicyCenterModule,
    PorfinModule,
    SalesforceModule,
    SaludWebModule,
    SEEEModule,
    SEUSModule,
    SOATModule,
    STARModule,
    TablaTercerosModule,
    ViafirmaModule,
]

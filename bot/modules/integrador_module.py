import re

from dtos.automation_dto import (
    IntegradorRemovedUserResponseDto,
    IntegradorResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.saml import SAML
from lib.tools import (
    async_retry,
    extract_digits,
    get_form_data,
    get_html_soup,
    get_xml_soup,
    xml_to_dict,
)

APP_URL = 'https://epsapps.suramericana.com/integrador'
BASE_PLATFORM_URL = 'https://epsapps.suramericana.com/integrador/%s'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded'}
GET_ENTRY_FORM_PATH = 'inicio.do'
SEND_ENTRY_FORM_PATH = 'validarUsuario.do'
CONSULT_USER_FORM_PATH = 'ajax.administracionItgUsuariosAjax.do'
DELETE_USER_FORM_PATH = 'ajax.administracionItgUsuariosAjax.do'


class IntegradorModule(BaseModule):
    """Provide functions to consult and remove
    a user on Integrador.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Perform the SAML authentication.

        Parameters
        ----------
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        self.session = self.saml.authenticate(
            app_url=APP_URL,
            username=username,
            password=password,
        )

    def _extract_ips(self, entry_form_raw_content: str) -> str:
        """Extract the ips form field value.

        Parameters
        ----------
        entry_form_raw_content : str
            Entry form raw content.

        Returns
        -------
        str
            The ips form field value.

        Raises
        ------
        AutomationError
            If IPS was not found.
        AutomationError
            If IPS could not be extracted.

        """
        matches = re.search(r'lasIps\[1\] = (\d+);', entry_form_raw_content)
        if not matches:
            raise AutomationError(
                'No se encontró el IPS.',
                detail=entry_form_raw_content,
            )
        try:
            return matches.group(1)
        except Exception as e:
            raise AutomationError(
                'No se pudo extraer el IPS.',
                detail=f'CONTENT: {entry_form_raw_content}; ERROR: {str(e)}',
            ) from e

    def _extract_platform(
        self,
        entry_form_raw_content: str,
        ips: str,
    ) -> str | None:
        """Extract the platform form field value.

        Parameters
        ----------
        entry_form_raw_content : str
            Entry form raw content.
        ips : str
            The ips form field value.

        Returns
        -------
        str | None
            The platform form field value.

        """
        matches = re.search(
            rf'IPS_{ips}\[2\]\[0\]\[0\] = \"(\d+)\";',
            entry_form_raw_content,
        )
        if not matches:
            return None
        try:
            return matches.group(1)
        except IndexError:
            return None

    def _get_entry_form(self) -> FormData:
        """Get the form to select the IPS and
        validate the user.

        Raises
        ------
        AutomationError
            If user could not be validated.

        """
        response = self.session.get(BASE_PLATFORM_URL % GET_ENTRY_FORM_PATH)
        if not response.ok:
            raise AutomationError(
                'Ha ocurrido un error al obtener el formulario de ingreso.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            error_message='No se encontró el formulario de ingreso',
        )
        form_data.action = BASE_PLATFORM_URL % SEND_ENTRY_FORM_PATH
        ips = self._extract_ips(response.text)
        platform = self._extract_platform(response.text, ips)
        form_data.data = {'ips': ips, 'plataforma': platform}
        return form_data

    def _send_entry_form(self, entry_form: FormData) -> None:
        """Send the entry form. It is the last login step.

        Parameters
        ----------
        entry_form : FormData
            Entry form.

        Raises
        ------
        AutomationError
            If form could not be sent.

        """
        response = self.session.request(
            method=entry_form.method,
            url=entry_form.action,
            data=entry_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'Ha ocurrido un error al enviar el formulario de ingreso.',
                detail=response.text,
            )

    def _extract_user_role(self, user_data_response: str) -> str:
        """Extract the role from the XML user info.

        Parameters
        ----------
        user_data_response : str
            XML user info.

        Returns
        -------
        str
            Role of the user.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If an error was encountered.

        """
        try:
            user_data = xml_to_dict(user_data_response)
            error = user_data['root']['usuarioBuscado'].get('mensajeUsuario')
            r_code = user_data['root']['usuarioBuscado']['codigoRol'].strip()
            r_name = user_data['root']['usuarioBuscado']['nombreRol'].strip()
        except Exception as e:
            raise NotFoundError() from e

        if error:
            raise AutomationError(
                str(error).strip(), detail=user_data_response
            )
        return f'{r_code} - {r_name}'

    def _send_consult_user_form(self, document: str) -> str:
        """Consult a user by its document.

        Parameters
        ----------
        document : str
            Document of the user to be consulted.

        Returns
        -------
        str
            User HTML info.

        Raises
        -------
        AutomationError
            If user could not be consulted.

        """
        data = {
            'tipoIdUsuarioBuscar': 'CC',
            'numeroIdUsuarioBuscar': document,
            'codigoCrud': 'Buscar',
        }
        response = self.session.post(
            BASE_PLATFORM_URL % CONSULT_USER_FORM_PATH,
            data=data,
            headers=HEADERS,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _delete_user(self, document: str) -> str:
        """Delete a user by its document.

        Parameters
        ----------
        document : str
            Document of the user to be deleted.

        Returns
        -------
        str
            Deleted user HTML response.

        Raises
        ------
        AutomationError
            If user could not be deleted.

        """
        data = {
            'tipoIdUsuarioEliminar': 'CC',
            'numeroIdUsuarioEliminar': document,
            'codigoCrud': 'Eliminar',
        }
        response = self.session.post(
            BASE_PLATFORM_URL % DELETE_USER_FORM_PATH,
            data=data,
            headers=HEADERS,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo eliminar el usuario.',
                detail=response.text,
            )
        return response.text

    def _get_deletion_response(
        self,
        user_deletion_response: str,
    ) -> IntegradorRemovedUserResponseDto:
        """Get the user deletion message.

        Parameters
        ----------
        user_deletion_response : str
            Deletion response content.

        Returns
        -------
        IntegradorRemovedUserResponseDto
            Deletion message.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        soup = get_xml_soup(user_deletion_response)
        if soup.root and soup.root.mensajeEliminar:
            if 'no existe' in soup.root.mensajeEliminar.text.lower():
                raise NotFoundError()
            message = soup.root.mensajeEliminar.text.replace(
                'existosamente', 'exitosamente.'
            )
            warning = False
        else:
            message = (
                'Por favor verifique que el usuario hay'
                ' sido retirado en esta aplicación.'
            )
            warning = True
        return IntegradorRemovedUserResponseDto(
            message=message, warning=warning
        )

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, document: str) -> IntegradorResponseDto:
        """Consult the role of a user.

        Parameters
        ----------
        document : str
            Document of the user to be consulted.

        Returns
        -------
        IntegradorResponseDto
            Role of the user.

        """
        try:
            document = extract_digits(document)
            self._authenticate()
            entry_form = self._get_entry_form()
            self._send_entry_form(entry_form)
            user_info = self._send_consult_user_form(document)
            return IntegradorResponseDto(
                role=self._extract_user_role(user_info)
            )
        finally:
            self.close_session()

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, document: str) -> IntegradorRemovedUserResponseDto:
        """Remove a user.

        Parameters
        ----------
        document : str
            Document of the user to be removed.

        Returns
        -------
        IntegradorRemovedUserResponseDto
            Deletion response.

        """
        try:
            document = extract_digits(document)
            self._authenticate(
                username=str(config.RETIROS_APPS_CONNECTION_USER),
                password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
            )
            entry_form = self._get_entry_form()
            self._send_entry_form(entry_form)
            deletion_response = self._delete_user(document)
            return self._get_deletion_response(deletion_response)
        finally:
            self.close_session()

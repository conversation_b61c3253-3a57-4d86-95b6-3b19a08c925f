from typing import cast

from bs4 import BeautifulSoup, Tag

from dtos.automation_dto import (
    ConveniosRemovedUserResponseDto,
    ConveniosResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.saml import SAML
from lib.tools import retry, get_form_data, get_html_soup

APP_URL = 'https://saludweb.suramericana.com/convenios/index.jsp'
BASE_PLATFORM_URL = 'https://saludweb.suramericana.com/convenios/pri/%s'
CONSULT_USER_FORM_PATH = 'admfd00001-buscar.do'
UPDATE_USER_FORM_PATH = 'admin00002-grabar.do'


class ConveniosModule(BaseModule):
    """Provide functions to consult and remove
    a user on Convenios.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Perform the SAML authentication.

        Parameters
        ----------
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        self.session = self.saml.authenticate(
            app_url=APP_URL,
            username=username,
            password=password,
        )

    def _get_main_page(self) -> None:
        """Get the main page and menu. It is the last login step.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        response = self.session.get(APP_URL)
        if not response.ok:
            raise AutomationError(
                'Ha ocurrido un error al obtener el menú principal.',
                detail=response.text,
            )

    def _extract_user_state(self, soup: BeautifulSoup) -> bool:
        """Extract the state from the HTML user info.

        Parameters
        ----------
        soup : BeautifulSoup
            HTML user data soup.

        Returns
        -------
        bool
            Whether user is active.

        """
        state_input = soup.find('select', {'name': 'estado'})
        if not state_input:
            return False
        selected_state = cast(Tag, state_input).find(
            'option', {'selected': True}
        )
        return (
            cast(Tag, selected_state).get('value', 'I') == 'A'
            if selected_state
            else False
        )

    def _extract_user_role(self, soup: BeautifulSoup) -> str:
        """Extract the role from the HTML user info.

        Parameters
        ----------
        soup : BeautifulSoup
            HTML user data soup.

        Returns
        -------
        str
            Role of the user.

        """
        role_code_input = soup.find('input', {'name': 'codigoRol'})
        role_code = (
            cast(Tag, role_code_input).get('value')
            if role_code_input
            else None
        )
        role_name_input = soup.find('input', {'name': 'nombreRol'})
        role_name = (
            cast(Tag, role_name_input).get('value')
            if role_name_input
            else None
        )
        return f'{role_code} - {role_name}'

    def _get_user_info_soup(self, user_html_info: str) -> BeautifulSoup:
        """Returns a BeautifulSoup instance of the HTML user info.

        Parameters
        ----------
        user_html_info : str
            HTML user info.

        Returns
        -------
        BeautifulSoup
            HTML user data soup.

        Raises
        -------
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(user_html_info)
        name_input = soup.find('input', {'name': 'nombre'})
        if not name_input or not cast(Tag, name_input).get('value'):
            raise NotFoundError()
        return soup

    def _create_user_data_dto(self, user_info: str) -> ConveniosResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        ConveniosResponseDto
            Requested user data.

        """
        soup = self._get_user_info_soup(user_info)
        role = self._extract_user_role(soup)
        active = self._extract_user_state(soup)
        return ConveniosResponseDto(active=active, role=role)

    def _send_consult_user_form(self, username: str) -> str:
        """Consult a user by a username.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        str
            User HTML info.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        data = {'modo': 'B', 'loggin': username.upper()}
        response = self.session.post(
            BASE_PLATFORM_URL % CONSULT_USER_FORM_PATH,
            data=data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _disable_user(self, user_info: str) -> str:
        """Disable a user.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        str
            Disabled user HTML info.

        Raises
        ------
        AutomationError
            If user could not be disabled.

        """
        soup = get_html_soup(user_info)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de actualización del usuario.',
        )
        form_data.action = BASE_PLATFORM_URL % UPDATE_USER_FORM_PATH
        form_data.data.update(
            {
                'accionesCambiadas': 'S',
                'estado': 'I',
                'codigoRol': '0',
                'nombreRol': 'GUEST',
            }
        )
        response = self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo retirar el usuario.',
                detail=response.text,
            )
        return response.text

    def _create_inactivated_user_data_dto(
        self,
        disabled_user_info: str,
    ) -> ConveniosRemovedUserResponseDto:
        """Create a response DTO from a disabled user data.

        Parameters
        ----------
        disabled_user_info : str
            HTML info of the disabled user.

        Returns
        -------
        ConveniosRemovedUserResponseDto
            Disabled user response.

        """
        user_data = self._create_user_data_dto(disabled_user_info)
        return ConveniosRemovedUserResponseDto(
            message='El usuario no pudo ser desactivado.'
            if user_data.active
            else 'Usuario retirado correctamente.',
            warning=user_data.active,
            active=user_data.active,
            role=user_data.role,
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> ConveniosResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        ConveniosResponseDto
            Data of the user.

        """
        try:
            self._authenticate()
            self._get_main_page()
            user_info = self._send_consult_user_form(username)
            return self._create_user_data_dto(user_info)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> ConveniosRemovedUserResponseDto:
        """Disable a user and removes its role.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        ConveniosRemovedUserResponseDto
            Disabled user data.

        """
        try:
            self._authenticate(
                username=str(config.RETIROS_APPS_CONNECTION_USER),
                password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
            )
            self._get_main_page()
            user_info = self._send_consult_user_form(username)
            disabled_user_info = self._disable_user(user_info)
            return self._create_inactivated_user_data_dto(disabled_user_info)
        except Exception as e:
            self.close_session()
            raise e

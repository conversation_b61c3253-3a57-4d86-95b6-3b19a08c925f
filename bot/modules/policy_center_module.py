import json
from typing import Any

from dtos.automation_dto import PolicyCenterResponseDto
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.saml import SAML
from lib.tools import async_retry, filter_dict_list_by_field

APP_URL = 'https://coreseguros.suramericana.com/pc/PolicyCenter.do'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'}
ACTIVE_FIELD_ID = (
    'UserDetailPage:UserDetailScreen:UserDetailDV:'
    'UserDetailCommons:UserDetailInputSet:Active'
)
LOCKED_FIELD_ID = (
    'UserDetailPage:UserDetailScreen:UserDetailDV:'
    'UserDetailCommons:UserDetailInputSet:AccountLocked'
)


class PolicyCenterModule(BaseModule):
    """Provide a function to consult the data of
    a user on Policy Center.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(self) -> None:
        """Perform the SAML authentication."""
        self.session = self.saml.authenticate(APP_URL)

    def _get_main_page(self) -> None:
        """Get the main page and menu. It is the last login step.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        response = self.session.get(APP_URL)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú de la página principal.',
                detail=response.text,
            )

    def _get_user_query_form(self) -> None:
        """Get the user query form.

        Raises
        ------
        AutomationError
            If user query form could not be obtained.

        """
        payload = {
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': (
                'TabBar:AdminTab:Admin_UsersAndSecurity'
                ':UsersAndSecurity_AdminUserSearchPage_act'
            ),
            'objFocusId': ':tabs-menu-trigger',
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de consulta de usuarios.',
                detail=response.text,
            )

    def _send_consult_user_form(self, username: str) -> None:
        """Consult the user by its username.

        Parameters
        ----------
        username : str
            Username of the user to consult.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        payload = {
            'AdminUserSearchPage:UserSearchScreen:UserSearchDV:Username': username,
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': (
                'AdminUserSearchPage:UserSearchScreen:UserSearchDV'
                ':SearchAndResetInputSet:SearchLinksInputSet:Search_act'
            ),
            'objFocusId': (
                'AdminUserSearchPage:UserSearchScreen:UserSearchDV'
                ':SearchAndResetInputSet:SearchLinksInputSet:Search'
            ),
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                f'No se pudo consultar al usuario "{username}".',
                detail=response.text,
            )

    def _fetch_user_data(self, username: str) -> Any:
        """Get the data of the user.

        Parameters
        ----------
        username : str
            Username of the consulted user.

        Returns
        -------
        Any
            User data.

        Raises
        ------
        AutomationError
            If user data could not be obtained.

        """
        payload = {
            'AdminUserSearchPage:UserSearchScreen:UserSearchDV:Username': username,
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': (
                'AdminUserSearchPage:UserSearchScreen'
                ':UserSearchResultsLV:0:DisplayName_act'
            ),
            'objFocusId': (
                'AdminUserSearchPage:UserSearchScreen'
                ':UserSearchResultsLV:0:DisplayName'
            ),
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                f'No se pudo obtener los datos del usuario "{username}".',
                detail=response.text,
            )
        return response.json()

    def _extract_user_roles(self, response: Any) -> list[str]:
        """Extract the found user roles.

        Parameters
        ----------
        response : Any
            Found user functions.

        Returns
        -------
        list[str]
            User roles.

        """
        try:
            key = 'items'
            data = response[2][key][0][key][1][key][2][key][0]['data']
            user_functions = data.get('root', [])
            return [role['c1']['text'] for role in user_functions]
        except (KeyError, IndexError, TypeError) as e:
            raise AutomationError(
                'No se pudo extraer las funciones del usuario.',
                detail=json.dumps(response, ensure_ascii=False),
            ) from e

    @async_retry(times=1)
    def _get_user_functions(self, username: str) -> list[str]:
        """Get the found user functions.

        Parameters
        ----------
        username : str
            Username of the consulted user.

        Returns
        -------
        list[str]
            User functions.

        Raises
        ------
        AutomationError
            If user functions could not be obtained.

        """
        payload = {
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': (
                'UserDetailPage:UserDetailScreen:UserDetail_RolesCardTab_act'
            ),
            'objFocusId': (
                'UserDetailPage:UserDetailScreen:UserDetail_RolesCardTab'
            ),
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                f'No se pudo obtener las funciones del usuario "{username}".',
                detail=response.text,
            )
        return self._extract_user_roles(response.json())

    def _extract_user_authorities(
        self,
        response: Any,
    ) -> list[str]:
        """Extract the found user authorities.

        Parameters
        ----------
        response : Any
            Found user authorities.

        Returns
        -------
        list[str]
            User authorities.

        """
        try:
            key = 'items'
            data = response[2][key][0][key][1][key][5][key][0][key][0]['data']
            user_authorities = data.get('root', [])
            return [authority['c1']['text'] for authority in user_authorities]
        except (KeyError, IndexError, TypeError) as e:
            raise AutomationError(
                'No se pudo extraer los aseguradores de '
                'autoridad del usuario.',
                detail=json.dumps(response, ensure_ascii=False),
            ) from e

    @async_retry(times=1)
    def _get_user_authorities(self, username: str) -> list[str]:
        """Gets the found user authorities.

        Parameters
        ----------
        username : str
            Username of the consulted user.

        Returns
        -------
        list[str]
            User authorities.

        Raises
        ------
        AutomationError
            If user authorities could not be obtained.

        """
        payload = {
            'csrfToken': self.session.cookies.get('csrfToken'),
            'eventSource': (
                'UserDetailPage:UserDetailScreen'
                ':UserDetail_AuthorityCardTab_act'
            ),
            'objFocusId': (
                'UserDetailPage:UserDetailScreen'
                ':UserDetail_AuthorityCardTab'
            ),
        }
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                message=(
                    f'No se pudo obtener la autoridad del asegurador'
                    f' del usuario "{username}".'
                ),
                detail=response.text,
            )
        return self._extract_user_authorities(response.json())

    def _extract_text_field(
        self,
        user_data_fields: list[dict[str, Any]],
        field_value: Any,
        field_name: str = 'id',
    ) -> str | None:
        """Extract the value of a text field.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.
        field_value : Any
            Value of the field to extract.
        field_name : str, optional
            Name of the field to extract, by default 'id'.

        Returns
        -------
        str | None
            Value of the field if found.

        """
        field = filter_dict_list_by_field(
            user_data_fields, field_name, field_value
        )
        return field.get('editValue')

    def _extract_active_field(
        self, user_data_fields: list[dict[str, Any]]
    ) -> bool:
        """Extract the active state of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        bool
            Whether user is active.

        Raises
        ------
        AutomationError
            If field not found.

        """
        active = self._extract_text_field(user_data_fields, ACTIVE_FIELD_ID)
        if active is None:
            raise AutomationError(
                'No se encontró el campo "Activo" del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            )

        return active == 'true'

    def _extract_locked_field(
        self, user_data_fields: list[dict[str, Any]]
    ) -> bool:
        """Extract the locked state of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        bool
            Whether user is locked.

        Raises
        ------
        AutomationError
            If field not found.

        """
        locked = self._extract_text_field(user_data_fields, LOCKED_FIELD_ID)
        if locked is None:
            raise AutomationError(
                'No se encontró el campo "Bloqueado" del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            )

        return locked == 'true'

    def _extract_user_data_fields(
        self, user_data: Any
    ) -> list[dict[str, Any]]:
        """Extract the data fields from the user data.

        Parameters
        ----------
        user_data : Any
            User data.

        Returns
        -------
        list[dict[str, Any]]
            User data fields.

        Raises
        -------
        AutomationError
            If user data fields could not be extracted.
        NotFoundError
            If user was not found.

        """
        try:
            # Fourth "items" key contains the user data if found
            key = 'items'
            fields = user_data[2][key][0][key][1][key][0].get(key)
            if not fields:
                raise NotFoundError()
            return fields[0][key][0][key]
        except (KeyError, IndexError, TypeError) as e:
            raise AutomationError(
                'No se pudo extraer los datos del usuario.',
                detail=json.dumps(user_data, ensure_ascii=False),
            ) from e

    def _create_user_data_dto(
        self,
        user_data_fields: list[dict[str, Any]],
        roles: list[str],
        authorities: list[str],
    ) -> PolicyCenterResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_data_fields : Any
            User data fields.

        Returns
        -------
        PolicyCenterResponseDto
            Requested user data.

        """
        return PolicyCenterResponseDto(
            active=self._extract_active_field(user_data_fields),
            locked=self._extract_locked_field(user_data_fields),
            roles=roles,
            authorities=authorities,
        )

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> PolicyCenterResponseDto:
        """Consult the user by its username and get its info.

        Parameters
        ----------
        username : str
            Username to consult.

        Returns
        -------
        PolicyCenterResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            self._get_main_page()
            self._get_user_query_form()
            self._send_consult_user_form(username=username)
            user_data = self._fetch_user_data(username=username)
            user_data_fields = self._extract_user_data_fields(user_data)
            roles = self._get_user_functions(username)
            authorities = self._get_user_authorities(username)
            return self._create_user_data_dto(
                user_data_fields, roles, authorities
            )
        finally:
            self.close_session()

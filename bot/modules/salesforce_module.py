from typing import Any, OrderedDict

from dtos.automation_dto import (
    SalesforceRemoveUserResponseDto,
    SalesforceResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.salesforce import Salesforce
from lib.tools import async_retry, get_nested

UPDATED_STATUS_CODE = '204'
QUERY = (
    'SELECT Id, FederationIdentifier, Name, Username, IsActive, UserRole.name,'
    ' Profile.name, Profile.UserLicense.name, Title, CompanyName'
    ' FROM User WHERE FederationIdentifier = {}'
    " AND Profile.name NOT IN ('Integraciones','Devops')"
)


class SalesforceModule(BaseModule):
    """Provide functions to consult and remove
    a user on Salesforce.
    """

    def __init__(self, sf: Salesforce) -> None:
        self.salesforce = sf
        self.module = 'Salesforce'

    @property
    def _is_salesforce(self) -> bool:
        return self.module == 'Salesforce'

    def _connect(self, use_retiros_user: bool = False) -> None:
        """Connect to Salesforce API.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        """
        if use_retiros_user:
            username = config.SALESFORCE_RETIROS_USER
            password = config.SALESFORCE_RETIROS_PASSWORD
            security_token = config.SALESFORCE_RETIROS_SECURITY_TOKEN
        else:
            username = config.SALESFORCE_USER
            password = config.SALESFORCE_PASSWORD
            security_token = config.SALESFORCE_SECURITY_TOKEN
        self.salesforce.connect(
            domain=config.SALESFORCE_DOMAIN,
            username=str(username),
            password=str(password),
            security_token=str(security_token),
            client_id=str(config.SALESFORCE_CLIENT_ID),
            client_secret=str(config.SALESFORCE_CLIENT_SECRET),
        )

    def _find_user_by_username(self, username: str) -> OrderedDict[str, Any]:
        """Find user by its username.

        Parameters
        ----------
        username : str
            User's username.

        Returns
        -------
        dict[str, Any]
            User's data.

        Raises
        -------
        NotFoundError
            If user was not found.
        AutomationError
            If user could not be consulted.

        """
        try:
            username = username.strip().lower()
            records = self.salesforce.run_query(QUERY, username)
        except Exception as e:
            raise AutomationError(
                f'No se pudo consultar el usuario en {self.module}.',
                detail=f'QUERY: {QUERY}; ERROR: {str(e)}',
            ) from e
        if not records:
            raise NotFoundError()
        return records[0]

    def _get_disable_user_fields(self) -> dict[str, Any]:
        """Set the fields to be updated in order to disable a user.

        Parameters
        ----------
        user : OrderedDict[str, Any]
            User data.

        """
        fields: dict[str, Any] = {'IsActive': False}
        fields.setdefault('Title', 'N/A')
        fields.setdefault('CompanyName', 'N/A')
        fields.setdefault('FederationIdentifier', 'N/A')
        return fields

    def _freeze_user(
        self, user: dict[str, Any]
    ) -> SalesforceRemoveUserResponseDto:
        """Freeze a user.

        Parameters
        ----------
        user : dict[str, Any]
            User data.

        Returns
        -------
        SalesforceRemoveUserResponseDto
            Freeze user response.

        """
        result = self.salesforce.freeze_user(user['Id'])
        if str(result) != UPDATED_STATUS_CODE:
            message = (
                f'El usuario no pudo ser desactivado en {self.module} debido'
                f' a que tiene jerarquías. Se intentó realizar la congelación'
                f' del usuario pero no fue exitosa. Por favor ingrese al CRM y'
                f' congele al usuario manualmente.'
            )
        else:
            message = (
                f'El usuario no pudo ser desactivado en {self.module} debido'
                f' a que tiene jerarquías. Por lo tanto, el usuario ha sido'
                f' congelado.'
            )
        return SalesforceRemoveUserResponseDto(
            active=user.get('IsActive', False),
            frozen=True,
            warning=True,
            message=message,
        )

    def _disable_user(self, username: str) -> SalesforceRemoveUserResponseDto:
        """Disable a user.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        SalesforceRemoveUserResponseDto
            Disabled user response.

        Raises
        ------
        AutomationError
            If user could not be disabled.

        """
        user = self._find_user_by_username(username)
        try:
            result = self.salesforce.update_user(
                user_id=user['Id'],
                fields=self._get_disable_user_fields(),
            )
        except Exception as e:
            err_msg = str(e)
            if 'dependency_exists' in err_msg.lower() and self._is_salesforce:
                return self._freeze_user(user)
            raise AutomationError(
                f'No se pudo retirar el usuario en {self.module}: {err_msg}.',
                detail=f'USER: {str(user)}; ERROR: {err_msg}',
            ) from e

        response_status_code = str(result)
        if response_status_code != UPDATED_STATUS_CODE:
            raise AutomationError(
                f'La API no pudo retirar el usuario en {self.module}.',
                detail=f'USER: {str(user)}; RESULT: {response_status_code}',
            )

        user_login = self.salesforce.get_user_login_by_user_id(user['Id'])
        return SalesforceRemoveUserResponseDto(
            active=False,
            frozen=user_login.get('IsFrozen', False),
            message='Usuario retirado correctamente.',
        )

    def _create_user_data_dto(
        self,
        user: OrderedDict[str, Any],
        user_login: OrderedDict[str, Any],
    ) -> SalesforceResponseDto:
        """Create a response DTO from a user data.

        Parameters
        ----------
        user : OrderedDict[str, Any]
            User data.
        user_login : OrderedDict[str, Any]
            User login data.

        Returns
        -------
        SalesforceResponseDto
            Requested user data.

        """
        profile = get_nested(user, 'Profile')
        return SalesforceResponseDto(
            position=user.get('Title', ''),
            active=user.get('IsActive', False),
            frozen=user_login.get('IsFrozen', False),
            role=get_nested(user, 'UserRole').get('Name', ''),
            profile=profile.get('Name', ''),
            license=get_nested(profile, 'UserLicense').get('Name', ''),
        )

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> SalesforceResponseDto:
        """Find a user by its username and return its data.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        SalesforceResponseDto
            User data.

        """
        try:
            self._connect()
            user = self._find_user_by_username(username)
            user_login = self.salesforce.get_user_login_by_user_id(user['Id'])
            return self._create_user_data_dto(user, user_login)
        finally:
            self.salesforce.close()

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> SalesforceRemoveUserResponseDto:
        """Disable a user.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        SalesforceRemoveUserResponseDto
            Disabled user response.

        """
        try:
            self._connect(use_retiros_user=True)
            return self._disable_user(username)
        finally:
            self.salesforce.close()

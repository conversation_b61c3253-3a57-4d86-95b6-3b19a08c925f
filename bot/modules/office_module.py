from typing import Any

from msal import ConfidentialClientApplication
from requests import Session

from dtos.automation_dto import OfficeRemovedUserResponseDto, OfficeResponseDto
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.tools import async_retry

AUTHORITY = 'https://login.microsoftonline.com/{}'
GRAPH_URL = 'https://graph.microsoft.com/v1.0/%s'
AUTH_SCOPES = ['https://graph.microsoft.com/.default']
HEADERS = {'Content-Type': 'application/json'}


class OfficeModule(BaseModule):
    """Provide functions to consult and remove the licenses
    of a user on Office 365.
    """

    def _get_client(self) -> ConfidentialClientApplication:
        """Get a client for the Azure AD app.

        Returns
        -------
        ConfidentialClientApplication
            Azure AD app Client.

        """
        return ConfidentialClientApplication(
            str(config.MSAL_CLIENT_ID),
            authority=AUTHORITY.format(str(config.MSAL_TENANT_ID)),
            client_credential=str(config.MSAL_CLIENT_SECRET),
        )

    def _get_access_token(self, client: ConfidentialClientApplication) -> str:
        """Gets the access token.

        Parameters
        ----------
        client : ConfidentialClientApplication
            Azure AD app Client.

        Returns
        -------
        str
            Access token.

        Raises
        ------
        AutomationError
            If access token could not be obtained.

        """
        token = client.acquire_token_for_client(scopes=AUTH_SCOPES)
        if not token or 'access_token' not in token:
            raise AutomationError('No se pudo obtener el token de acceso')
        return token['access_token']

    def _set_bearer_token(self, token: str) -> None:
        """Set the access token as a Bearer token.

        Parameters
        ----------
        token : str
            Access token.

        """
        self.session.headers['authorization'] = token

    def _authenticate(self) -> Session:
        """Return an authenticated session."""
        self.create_session()
        app = self._get_client()
        token = self._get_access_token(app)
        self._set_bearer_token(token)
        return self.session

    def _get_user_id_by_name(self, first_name: str, last_name: str) -> str:
        """Find a user by its givenName and surname
        attributes and return its id.

        Parameters
        ----------
        first_name : str
            First name.
        last_name : str
            Last name.

        Returns
        -------
        str
            User ID.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If user ID could not be obtained.
        AutomationError
            If multiple users found.

        """
        filter_users_path = (
            f"users?$filter=givenName eq '{first_name}'"
            f" and surname eq '{last_name}'"
        )
        response = self.session.get(GRAPH_URL % filter_users_path)
        if not response.ok:
            if response.json()['error']['code'] == 'Request_ResourceNotFound':
                raise NotFoundError()
            raise AutomationError(
                'No se pudo obtener el ID del usuario.',
                detail=response.text,
            )
        users = response.json().get('value', [])
        if not users:
            raise NotFoundError()
        if len(users) > 1:
            raise AutomationError(
                message=(
                    f'Se encontraron múltiples usuarios'
                    f' con nombre "{first_name} {last_name}".'
                ),
                detail=response.text,
            )
        return users[0]['id']

    def _get_user_licenses(self, user_id: str) -> list[dict[str, Any]]:
        """Find a user by its ID and return its licenses.

        Parameters
        ----------
        user_id : str
            User id.

        Returns
        -------
        list[dict[str, Any]]
            List of user's licenses.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If user licenses could not be obtained.

        """
        get_user_licenses_path = f'users/{user_id}/licenseDetails'
        response = self.session.get(GRAPH_URL % get_user_licenses_path)
        if not response.ok:
            if response.json()['error']['code'] == 'Request_ResourceNotFound':
                raise NotFoundError()
            raise AutomationError(
                'No se pudo obtener las licencias del usuario.',
                detail=response.text,
            )
        return response.json().get('value', [])

    def _remove_user_licenses(
        self,
        user_id: str,
        user_licenses: list[dict[str, Any]],
    ) -> None:
        """Remove the selected licenses from a user.

        Parameters
        ----------
        user_id : str
            User id.

        Returns
        -------
        list[str]
            List of user's licenses.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If user licenses could not be removed.

        """
        remove_licenses_path = f'users/{user_id}/assignLicense'
        data = {
            'addLicenses': [],
            'removeLicenses': [
                user_license['skuId'] for user_license in user_licenses
            ],
        }
        response = self.session.post(
            GRAPH_URL % remove_licenses_path,
            json=data,
            headers=HEADERS,
        )
        if not response.ok:
            if response.json()['error']['code'] == 'Request_ResourceNotFound':
                raise NotFoundError()
            raise AutomationError(
                'No se pudo retirar las licencias del usuario',
                detail=response.text,
            )

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(
        self, first_name: str, last_name: str
    ) -> OfficeResponseDto:
        """Find a user by its fullname and get its licenses.

        Parameters
        ----------
        first_name : str
            First name.
        last_name : str
            Last name.

        Returns
        -------
        OfficeResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            user_id = self._get_user_id_by_name(first_name, last_name)
            user_licenses = self._get_user_licenses(user_id)
            license_names = [
                user_license['skuPartNumber'] for user_license in user_licenses
            ]
            return OfficeResponseDto(licenses=license_names)
        finally:
            self.close_session()

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(
        self, first_name: str, last_name: str
    ) -> OfficeRemovedUserResponseDto:
        """Remove the licenses of a user.

        Parameters
        ----------
        first_name : str
            First name.
        last_name : str
            Last name.

        Returns
        -------
        OfficeRemovedUserResponseDto
            Removed licenses.

        """
        try:
            self._authenticate()
            user_id = self._get_user_id_by_name(first_name, last_name)
            user_licenses = self._get_user_licenses(user_id)
            self._remove_user_licenses(user_id, user_licenses)
            license_names = [
                user_license['skuPartNumber'] for user_license in user_licenses
            ]
            return OfficeRemovedUserResponseDto(
                removed_licenses=license_names,
                message='Usuario retirado correctamente.',
            )
        finally:
            self.close_session()

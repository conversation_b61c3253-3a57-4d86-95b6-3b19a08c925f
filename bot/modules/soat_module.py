import json
from dataclasses import dataclass
from typing import cast

from bs4 import Tag

from dtos.automation_dto import SOATResponseDto
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.saml import SAML
from lib.tools import retry, extract_digits, get_form_data, get_html_soup

APP_URL = 'https://soat.suranet.com/soat'
BASE_PLATFORM_URL = 'https://soat.suranet.com%s'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'}
HOME_PATH = '/soat/faces/Home2/Home.jsp'
CONSULT_USERS_PATH = (
    '/soat/faces/Administracion/Usuarios/AdministracionUsuarios.jsp'
)


@dataclass
class SOATUserData:
    """State of the user and ID of user channels element."""

    state: bool
    channels_id: str


class SOATModule(BaseModule):
    """Provide a function to consult the state and channels
    of a user on SOAT.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(self) -> None:
        """Perform the SAML authentication."""
        self.session = self.saml.authenticate(APP_URL)

    def _get_main_page(self) -> None:
        """Get the main page and menu. It is the last login step.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        response = self.session.get(APP_URL)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú de la página principal.',
                detail=response.text,
            )

    def _get_home_page(self) -> None:
        """Get the home page of the administration section.

        Raises
        ------
        AutomationError
            If home page could not be fetched.

        """
        response = self.session.get(BASE_PLATFORM_URL % HOME_PATH)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener la página principal de administración.',
                detail=response.text,
            )

    def _get_users_form(self) -> FormData:
        """Get the form to consult a user.

        Returns
        -------
        FormData
            Form to consult a user.

        Raises
        ------
        AutomationError
            If form could not be fetched.

        """
        response = self.session.get(BASE_PLATFORM_URL % CONSULT_USERS_PATH)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de consulta de usuarios.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        return get_form_data(
            soup,
            'No se encontró el formulario de consulta de usuarios.',
        )

    def _build_payload(
        self,
        document: str,
        consult_user_form: FormData,
    ) -> dict[str, str]:
        """Build the payload to consult a user.

        Parameters
        ----------
        consult_user_form : FormData
            Form to consult the user.
        document : str
            User document.

        Returns
        -------
        dict[str, str]
            Payload to consult a user.

        """
        consult_user_form.data['campo_numeroIdentificacion'] = document
        consult_user_form.data.pop('campo_nombreCanal')
        consult_user_form.data.pop('campo_codigoCanal')
        consult_user_form.data.pop('check_consultarSubcanales')
        consult_user_form.data.pop('campo_nmTotalRegistros')
        consult_user_form.data.pop('campo_mensajesUsuario')
        consult_user_form.data.pop('campo_mensajesTecnicos')
        consult_user_form.data.pop('isPostback')
        for key in list(consult_user_form.data.keys()):
            if key.startswith('form_administarcionUsuarios:boton_'):
                del consult_user_form.data[key]
        return consult_user_form.data

    def _send_consult_user_form(
        self,
        document: str,
        consult_user_form: FormData,
    ) -> SOATUserData:
        """Consult a user.

        Parameters
        ----------
        document : str
            User document.
        consult_user_form : FormData
            Form to consult the user.

        Returns
        -------
        SOATUserData
            User data.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        response = self.session.request(
            method=consult_user_form.method,
            url=BASE_PLATFORM_URL % consult_user_form.action,
            data=self._build_payload(document, consult_user_form),
            headers=HEADERS,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return self._extract_user_info(response.text)

    def _extract_user_info(self, user_info_response: str) -> SOATUserData:
        """Extract the information of the user.

        Parameters
        ----------
        user_info_response : str
            User info response content.

        Returns
        -------
        SOATUserData
            User data.

        Raises
        ------
        NotFoundError
            User was not found.

        """
        soup = get_html_soup(user_info_response)
        user_info_table = soup.find(
            'tbody',
            {'id': 'form_administarcionUsuarios:tabla_usuariosConsultados:tb'},
        )
        if not user_info_table:
            raise NotFoundError()
        user_info_rows = cast(Tag, user_info_table).find_all('tr')
        if not user_info_rows:
            raise NotFoundError()
        user_info_cells = cast(Tag, user_info_rows[0]).find_all('td')
        if not user_info_cells:
            raise NotFoundError()
        channels_element = soup.find('div', {'id': 'tooltip_canales[0]'})
        channels_id = channels_element.text.strip() if channels_element else ''
        return SOATUserData(
            state=user_info_cells[4].text.strip().upper() == 'HABILITADO',
            channels_id=channels_id,
        )

    def _extract_user_channels(self, channels_response: str) -> list[str]:
        """Extract the channels of the user.

        Parameters
        ----------
        channels_response : str
            Channels response content.

        Returns
        -------
        list[str]
            Channels of the user.

        """
        soup = get_html_soup(channels_response)
        channels_field = soup.find('input', {'id': 'campo_nombreCanal'})
        if not channels_field:
            return []
        channels = cast(Tag, channels_field).get('value')
        return [c['descripcion'] for c in json.loads(cast(str, channels))]

    def _get_user_channels(self, channels_id: str) -> list[str]:
        """Get the user channels.

        Parameters
        ----------
        channels_id : str
            ID of the element which contains
            the user channels.

        Returns
        -------
        list[str]
            User channels.

        Raises
        ------
        AutomationError
            If user channels could not be fetched.

        """
        user_channels_path = (
            f'/soat/faces/Administracion/Usuarios/MaestroUsuarios.jsp'
            f'?numId={channels_id}&operacion=editar'
        )
        response = self.session.get(BASE_PLATFORM_URL % user_channels_path)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar los canales del usuario.',
                detail=response.text,
            )
        return self._extract_user_channels(response.text)

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, document: str) -> SOATResponseDto:
        """Get the user state and channels.

        Parameters
        ----------
        document : str
            User document.

        Returns
        -------
        SOATResponseDto
            User state and channels.

        """
        try:
            document = extract_digits(document)
            self._authenticate()
            self._get_main_page()
            self._get_home_page()
            users_form = self._get_users_form()
            user_data = self._send_consult_user_form(document, users_form)
            user_channels = self._get_user_channels(user_data.channels_id)
            return SOATResponseDto(
                active=user_data.state,
                channels=user_channels,
            )
        finally:
            self.close_session()

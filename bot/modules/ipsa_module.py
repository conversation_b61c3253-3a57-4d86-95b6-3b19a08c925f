import re
from typing import cast
from urllib.parse import urljoin

from bs4 import Tag

from dtos.automation_dto import (
    IPSARemovedUserResponseDto,
    IPSAResponseDto,
    IPSARole,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.saml import SAML
from lib.tools import (
    retry,
    extract_digits,
    extract_trailing_letters,
    get_form_data,
    get_html_soup,
    strip_text,
)

APP_URL = 'https://ipsa-intercambios.suramericana.com/ipsa/index.jsp'
BASE_PLATFORM_URL = (
    'https://ipsa-intercambios.suramericana.com/ipsa/servlet/%s'
)
HOME_SERVLET_NAME = 'co.com.winet.ipsa.Inicio'
CONSULT_USER_PATH = 'co.com.winet.ipsa.Buscador'
INSPECT_USER_SERVLET_NAME = 'co.com.winet.seguridad.Frame'
GET_ROLES_SERVLET_NAME = (
    'co.com.winet.seguridad.ModificarSucursalesEmpleadoBottom'
)
REMOVE_ROLE_SERVLET_NAME = (
    'co.com.winet.seguridad.ModificarSucursalesEmpleadoBottom'
)


class IPSAModule(BaseModule):
    """Provide functions to consult and remove
    a user on IPSA.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Perform the SAML authentication.

        Parameters
        ----------
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        self.session = self.saml.authenticate(
            app_url=APP_URL,
            username=username,
            password=password,
        )

    def _get_ipsa_user_validation_form(self) -> FormData:
        """Send the request to get the user validation form.

        Returns
        -------
        FormData
            User validation form.

        Raises
        ------
        AutomationError
            If user validation form could not be obtained.

        """
        response = self.session.get(BASE_PLATFORM_URL % HOME_SERVLET_NAME)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de validación del usuario.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de validación del usuario.',
        )
        form_data.action = BASE_PLATFORM_URL % form_data.action
        return form_data

    def _validate_ipsa_user(self, ipsa_user_validation_form: FormData):
        """Send the request to validate the user.

        Parameters
        ----------
        ipsa_user_validation_form : FormData
            IPSA user validation form.

        Raises
        ------
        AutomationError
            If user could not be validated.

        """
        response = self.session.request(
            method=ipsa_user_validation_form.method,
            url=ipsa_user_validation_form.action,
            data=ipsa_user_validation_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo validar el usuario.',
                detail=response.text,
            )

    def _get_document_type(self, document: str) -> str:
        """Get the document type from the document.

        Parameters
        ----------
        document : str
            Document of the user to be consulted.

        Returns
        -------
        str
            Document type.

        """
        document_type = extract_trailing_letters(document)
        match document_type:
            case 'P':
                selected = 'PA'
            case 'E':
                selected = 'CE'
            case 'T':
                selected = 'TI'
            case 'X':
                selected = 'TE'
            case 'N':
                selected = 'NI'
            case _:
                selected = 'CC'
        return selected

    def _send_consult_user_form(self, document: str) -> str:
        """Consult a user by its document.

        Parameters
        ----------
        document : str
            Document of the user to be consulted.

        Returns
        -------
        str
            User HTML info.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        document_type = self._get_document_type(document)
        document_number = extract_digits(document)
        params = [
            '?Archivo=BuscadoresSegura',
            'Propiedad=Buscador.Administracion',
            'tipo=avanzado',
            'numregs=20',
            'operando0=%3D',
            f'pn={document_type}',
            'coincidir0=',
            'operando1=%3D',
            f'pn={document_number}',
            'coincidir1=',
            'operando2=%3D',
            'pn=',
            'operando3=%3D',
            'pn=',
            'operando4=%3D',
            'pn=',
            'operando5=%3D',
            'pn=',
            'ipsa.buscador.resultsbypage=20',
            'ipsa.buscador.maxresults=100',
            'orden=Tipo+Iden',
        ]
        query = '&'.join(params)
        url = urljoin(BASE_PLATFORM_URL % CONSULT_USER_PATH, query)
        response = self.session.get(url)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _extract_user_info_form(self, user_html_info: str) -> FormData:
        """Extract the form to get the whole user info.

        Parameters
        ----------
        user_html_info : str
            Consulted user HTML info.

        Returns
        -------
        FormData
            Form to get the whole user info.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(user_html_info)
        form = soup.find('form', {'name': 'formulario1'})
        if not form:
            raise NotFoundError()
        return get_form_data(
            cast(Tag, form),
            error_message=(
                'No se pudo encontrar el formulario de los datos'
                ' del usuario consultado.'
            ),
        )

    def _fetch_user_info(self, user_html_info: str) -> str:
        """Fetch the whole info of the consulted user.

        Parameters
        ----------
        user_html_info : str
            Consulted user HTML info.

        Returns
        -------
        str
            Whole user HTML info.

        Raises
        ------
        AutomationError
            If whole user info could not be fetched.

        """
        form_data = self._extract_user_info_form(user_html_info)
        response = self.session.post(
            BASE_PLATFORM_URL % INSPECT_USER_SERVLET_NAME,
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener los datos del usuario consultado.',
                detail=response.text,
            )
        return response.text

    def _get_ipsa_user_role_tags(self) -> list[Tag]:
        """Get the role HTML tags of the consulted user.

        Returns
        -------
        list[Tag]
            Role HTML tags.

        Raises
        ------
        AutomationError
            If error when getting the user roles.
        NotFoundError
            If user not found.

        """
        response = self.session.get(BASE_PLATFORM_URL % GET_ROLES_SERVLET_NAME)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener los roles del usuario.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        if not soup.table:
            raise NotFoundError()
        roles = soup.table.find_all('tr')
        no_branches_message = soup.table.find('tr', {'class': 'resulistasalt'})
        if not len(roles) > 2 and not no_branches_message:
            raise NotFoundError()
        return roles

    def _tag_to_ipsa_role(self, role_row: list[Tag]) -> IPSARole:
        """Wrap role info row into an IPSARole instance.

        Parameters
        ----------
        role_row : list[Tag]
            Role info row.

        """
        return IPSARole(
            branch=strip_text(role_row[0].text),
            position=strip_text(role_row[1].text),
            role=strip_text(role_row[2].text),
            status=strip_text(role_row[3].text),
        )

    def _role_tags_to_objects(self, roles: list[Tag]) -> list[IPSARole]:
        """Return a list of active roles for each tr tag.

        Fields order:
        - 0: Sucursal
        - 1: Cargo
        - 2: Rol
        - 3: Estado (Must be equal to "ACTIVO")

        Parameters
        ----------
        roles : list[Tag]
            List of tr tags with the roles information.

        Returns
        -------
        list[IPSARole]
            List of active roles.

        """
        output_role_list = []
        if not len(roles) > 2:
            return output_role_list
        for role in roles[2:]:
            fields: list[Tag] = role.find_all('td')
            if 'ACTIVO' not in fields[3].text.upper():
                continue
            output_role_list.append(self._tag_to_ipsa_role(fields))
        return output_role_list

    def _extract_role_ips(self, remove_role_cell_tag: Tag) -> str | None:
        """Extract the IPS of the role.

        Parameters
        ----------
        remove_role_cell_tag : Tag
            Cell tag containing the button
            to remove the role.

        Returns
        -------
        str | None
            IPS if role was found.

        """
        if not remove_role_cell_tag.a:
            return None
        href_value = remove_role_cell_tag.a.get('href')
        if not href_value:
            return None
        match = re.search(r'\( *\'(\w+)\' *\, *\'\w+\' *\)', str(href_value))
        if not match:
            return None
        return match.group(1)

    def _remove_role_by_ips(self, ips: str) -> bool:
        """Send the request to remove a role from a user.

        Parameters
        ----------
        ips : str
            IPS of the role.

        Returns
        -------
        bool
            Whether role was removed.

        """
        params = {'eliminar': ips}
        response = self.session.get(
            BASE_PLATFORM_URL % REMOVE_ROLE_SERVLET_NAME,
            params=params,
        )
        return response.ok

    def remove_user_roles(
        self,
        roles: list[Tag],
    ) -> IPSARemovedUserResponseDto:
        """Remove the roles of the user.

        Parameters
        ----------
        roles : list[Tag]
            List of tr tags with the roles information.

        Returns
        -------
        IPSARemovedUserResponseDto
            Removed and unremoved roles.

        """
        removed_roles = []
        unremoved_roles = []
        # First two rows are headers
        if not len(roles) > 2:
            return IPSARemovedUserResponseDto(
                message='No se encontraron roles para este usuario.',
                warning=True,
                removed_roles=removed_roles,
                unremoved_roles=unremoved_roles,
            )
        for role in roles[2:]:
            fields: list[Tag] = role.find_all('td')
            ips = self._extract_role_ips(fields[-1])
            if not ips:
                unremoved_roles.append(self._tag_to_ipsa_role(fields))
                continue
            removed = self._remove_role_by_ips(ips)
            if not removed:
                unremoved_roles.append(self._tag_to_ipsa_role(fields))
                continue
            removed_roles.append(self._tag_to_ipsa_role(fields))
        return self._get_remove_roles_dto(removed_roles, unremoved_roles)

    def _get_remove_roles_dto(
        self,
        removed_roles: list[IPSARole],
        unremoved_roles: list[IPSARole],
    ) -> IPSARemovedUserResponseDto:
        """Get the DTO response for the roles deletion.

        Parameters
        ----------
        removed_roles : list[IPSARole]
            List of removed roles.
        unremoved_roles : list[IPSARole]
            List of unremoved roles.

        Returns
        -------
        IPSARemovedUserResponseDto
            Deletion response.

        """
        if unremoved_roles:
            message = (
                'Algunos roles no pudieron ser eliminados,'
                ' por favor elimínelos manualmente.'
            )
            warning = True
        else:
            message = 'Roles eliminados correctamente.'
            warning = False
        return IPSARemovedUserResponseDto(
            message=message,
            warning=warning,
            removed_roles=removed_roles,
            unremoved_roles=unremoved_roles,
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, document: str) -> IPSAResponseDto:
        """Consult the roles of a user.

        Parameters
        ----------
        document : str
            User's document.

        Returns
        -------
        IPSAResponseDto
            Roles of the user.

        """
        try:
            self._authenticate()
            ipsa_user_validation_form = self._get_ipsa_user_validation_form()
            self._validate_ipsa_user(ipsa_user_validation_form)
            user_info = self._send_consult_user_form(document)
            self._fetch_user_info(user_info)
            role_tags = self._get_ipsa_user_role_tags()
            roles = self._role_tags_to_objects(role_tags)
            return IPSAResponseDto(roles=roles)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, document: str) -> IPSARemovedUserResponseDto:
        """Remove the roles of a user.

        Parameters
        ----------
        document : str
            User's document.

        Returns
        -------
        IPSARemovedUserResponseDto
            Removed roles from the user.

        """
        try:
            self._authenticate(
                username=str(config.RETIROS_APPS_CONNECTION_USER),
                password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
            )
            ipsa_user_validation_form = self._get_ipsa_user_validation_form()
            self._validate_ipsa_user(ipsa_user_validation_form)
            user_info = self._send_consult_user_form(document)
            self._fetch_user_info(user_info)
            role_tags = self._get_ipsa_user_role_tags()
            return self.remove_user_roles(role_tags)
        finally:
            self.close_session()

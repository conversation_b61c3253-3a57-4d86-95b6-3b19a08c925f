from typing import cast
from urllib.parse import urljoin

from bs4 import BeautifulSoup, ResultSet, Tag
from requests import Response

from dtos.automation_dto import (
    ViafirmaRemovedUserResponseDto,
    ViafirmaResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import Automation<PERSON>rror, NotFoundError
from lib.interfaces import FormData
from lib.tools import retry, get_form_data, get_html_soup, strip_text

APP_URL = 'https://signature.sura.com/inbox/index.jsf?cid=574214'
BASE_PLATFORM_URL = 'https://signature.sura.com/inbox/%s'
USERS_PATH = 'usuario/peticion/inbox.jsf'
CONSULT_USER_PATH = '/inbox/admin/user/list.jsf'


class ViafirmaModule(BaseModule):
    """Provide functions to consult and remove
    a user on Viafirma.
    """

    def _get_credentials(
        self,
        use_retiros_user: bool = False,
    ) -> tuple[str, str]:
        """Get the login credentials.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        Returns
        -------
        tuple[str, str]
            Connection credentials.

        """
        if use_retiros_user:
            return (
                str(config.VIAFIRMA_RETIROS_USER),
                str(config.VIAFIRMA_RETIROS_PASSWORD),
            )
        return (
            str(config.VIAFIRMA_USER),
            str(config.VIAFIRMA_PASSWORD),
        )

    def _get_login_form(self, use_retiros_user: bool = False) -> FormData:
        """Get the login form.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        Returns
        -------
        FormData
            Login form.

        Raises
        -------
        AutomationError
            If login form could not be obtained.

        """
        response = self.session.get(APP_URL)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de login.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup, error_message='No se encontró el formulario de login.'
        )
        form_data.action = urljoin(response.url, form_data.action)
        username, pwd = self._get_credentials(use_retiros_user)
        self._set_credentials_to_form(form_data, username, pwd)
        return form_data

    def _set_credentials_to_form(
        self, form_data: FormData, username: str, pwd: str
    ) -> None:
        """Set the credentials to the form.

        Parameters
        ----------
        form_data : FormData
            Form data.
        username : str
            Conn user.
        pwd : str
            Conn password.

        """
        fields = list(form_data.data.keys())
        for k in fields:
            if k.endswith(':login'):
                form_data.data[k] = username
            if k.endswith(':password'):
                form_data.data[k] = pwd

    def _send_login_form(self, login_form: FormData) -> None:
        """Send the login form.

        Parameters
        ----------
        login_form : FormData
            Login form.

        Raises
        ------
        AutomationError
            If login error.
        AutomationError
            If credentials are invalid.

        """
        response = self.session.request(
            method=login_form.method,
            url=login_form.action,
            data=login_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo iniciar sesión.',
                detail=response.text,
            )
        if 'Invalid credentials' in response.text:
            raise AutomationError(
                'Las credenciales de acceso no son válidas.',
                detail=response.text,
            )

    def _authenticate(self, use_retiros_user: bool = False) -> None:
        """Perform the authentication.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        """
        self.create_session()
        login_form = self._get_login_form(use_retiros_user)
        self._send_login_form(login_form)

    def _extract_users_form_data(
        self,
        users_form_response: Response,
        username: str,
    ) -> FormData:
        """Extract the users form data.

        Parameters
        ----------
        users_form_response : Response
            Users form response.
        username : str
            Username of the user to consult.

        Returns
        -------
        FormData
            Form data.

        Raises
        ------
        AutomationError
            If form was not found.

        """
        soup = get_html_soup(users_form_response.text)
        form = soup.find(id='formBusqueda')
        if not form:
            raise AutomationError(
                'No se pudo encontrar el formulario de consulta.',
                detail=users_form_response.text,
            )
        form_data = get_form_data(
            cast(Tag, form),
            'No se pudo encontrar el formulario de consulta.',
        )
        form_data.action = urljoin(users_form_response.url, form_data.action)
        fields = list(form_data.data.keys())
        for k in fields:
            if (
                k.endswith(':mostrarUsuariosInactivos')
                or str(form_data.data[k]).lower().strip() == 'mostrar todos'
            ):
                del form_data.data[k]
            if k.endswith(':textoBusqueda'):
                form_data.data[k] = username
        return form_data

    def _get_users_form(self, username: str) -> FormData:
        """Get the users form.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        FormData
            Users form.

        Raises
        ------
        AutomationError
            If form could not be obtained.
        AutomationError
            If user has no rights or credentials are invalid.

        """
        params = {
            'actionMethod': (
                'usuario/peticion/inbox.xhtml:'
                'adminUserController.actionList'
            )
        }
        response = self.session.get(
            BASE_PLATFORM_URL % USERS_PATH,
            params=params,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de consulta.',
                detail=response.text,
            )
        if 'Unauthorized' in response.text:
            raise AutomationError(
                message=(
                    'El usuario no tiene permisos para consultar'
                    ' o las credenciales de acceso no son válidas.'
                ),
                detail=response.text,
            )
        return self._extract_users_form_data(response, username)

    def _send_consult_user_form(self, consult_users_form: FormData) -> Response:
        """Consult the user.

        Parameters
        ----------
        consult_users_form : FormData
            User consulting form.

        Returns
        -------
        Response
            Consulted user response.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        response = self.session.request(
            method=consult_users_form.method,
            url=consult_users_form.action,
            data=consult_users_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response

    def _extract_user_action_link(
        self,
        consulted_user_response: Response,
        c_name: str,
        username: str,
    ) -> str:
        """Extract a link to operate an action on the user.

        Parameters
        ----------
        consulted_user_response : Response
            User info response content.
        c_name : str
            Class name of the link element.
        username : str
            Username of the user to be consulted.

        Returns
        -------
        str
            Link to perform an action on the user.

        Raises
        ------
        AutomationError
            If link to perform the action on the user
            could not be extracted.
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(consulted_user_response.text)
        if not soup.table or not soup.table.tbody:
            raise NotFoundError()
        rows = soup.table.tbody.find_all('tr')
        if not rows:
            raise NotFoundError()
        user_row = self._extract_user_row(username, rows)
        if not user_row:
            raise NotFoundError()
        edit_link_tag = user_row.find('a', {'class': c_name})
        if not edit_link_tag:
            raise NotFoundError()
        edit_link = cast(Tag, edit_link_tag).get('href', '')
        if not edit_link:
            raise NotFoundError()
        return urljoin(consulted_user_response.url, str(edit_link))

    def _extract_user_row(
        self, username: str, rows: ResultSet[Tag]
    ) -> Tag | None:
        """Extract the user row.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.
        rows : ResultSet[Tag]
            Rows of the table containing the users.

        Returns
        -------
        Tag | None
            User row.

        """
        if len(rows) == 1:
            return rows[0]

        for row in rows:
            username_td = row.find('td')
            if not username_td:
                continue
            if username_td.text.strip().upper() == username.upper():
                return row

        return None

    def _get_edit_user_form(self, edit_user_link: str) -> Response:
        """Get the form to edit the user.

        Parameters
        ----------
        edit_user_link : str
            Link to edit the user.

        Returns
        -------
        Response
            Edit user form response.

        Raises
        ------
        AutomationError
            If edit user form could not be fetched.

        """
        response = self.session.get(edit_user_link)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de edición del usuario.',
                detail=response.text,
            )
        return response

    def _delete_user(self, remove_user_link: str) -> None:
        """Delete the user.

        Parameters
        ----------
        remove_user_link : str
            Link to remove the user.

        Raises
        ------
        AutomationError
            If user could not be deleted.

        """
        response = self.session.get(remove_user_link)
        if not response.ok:
            raise AutomationError(
                'No se pudo retirar el usuario.',
                detail=response.text,
            )

    def _extract_state(self, soup: BeautifulSoup) -> bool:
        """Extract the state of the user.

        Parameters
        ----------
        soup: BeautifulSoup
            Edit user form soup.

        Returns
        -------
        bool
            State of the user.

        Raises
        ------
        AutomationError
            If state of the user could not be extracted.

        """
        state = soup.find(id='formUser:userboolIsActive')
        if not state:
            return False
        return cast(Tag, state).get('checked') == 'checked'

    def _extract_role(self, soup: BeautifulSoup) -> str:
        """Extract the role of the user.

        Parameters
        ----------
        soup: BeautifulSoup
            Edit user form soup.

        Returns
        -------
        str
            Role of the user.

        Raises
        ------
        AutomationError
            If role of the user could not be extracted.

        """
        roles = soup.find(id='formUser:userlevelAccess')
        if not roles:
            return 'N/A'
        selected_role = cast(Tag, roles).find('option', {'selected': True})
        if not selected_role:
            return 'N/A'
        return strip_text(selected_role.text)

    def _create_user_data_dto(
        self,
        user_html_info: str,
    ) -> ViafirmaResponseDto:
        """Create a response DTO from a user data.

        Parameters
        ----------
        user_html_info : str
            User HTML info.

        Returns
        -------
        ViafirmaResponseDto
            Requested user data.

        """
        soup = get_html_soup(user_html_info)
        return ViafirmaResponseDto(
            active=self._extract_state(soup),
            role=self._extract_role(soup),
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> ViafirmaResponseDto:
        """Consult the role of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        ViafirmaResponseDto
            Role of the user.

        """
        try:
            self._authenticate()
            users_form = self._get_users_form(username)
            user_response = self._send_consult_user_form(users_form)
            link = self._extract_user_action_link(
                user_response, 'editar', username
            )
            user_form = self._get_edit_user_form(link)
            return self._create_user_data_dto(user_form.text)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> ViafirmaRemovedUserResponseDto:
        """Remove a user.

        Parameters
        ----------
        username : str
            Username of the user to be removed.

        Returns
        -------
        ViafirmaRemovedUserResponseDto
            Removed user data.

        """
        try:
            self._authenticate(use_retiros_user=True)
            users_form = self._get_users_form(username)
            user_response = self._send_consult_user_form(users_form)
            link = self._extract_user_action_link(
                user_response, 'eliminar', username
            )
            self._delete_user(link)
            return ViafirmaRemovedUserResponseDto(
                message='Usuario retirado correctamente.'
            )
        finally:
            self.close_session()

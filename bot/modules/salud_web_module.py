from typing import cast

from bs4 import BeautifulSoup, Tag

from dtos.automation_dto import (
    SaludWebRemovedUserResponseDto,
    SaludWebResponseDto,
)
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.salud_web_retiros_session import SaludWebRetirosSession
from lib.salud_web_session import SaludWebSession
from lib.tools import async_retry, get_form_data, get_html_soup

APP_URL = 'https://saludweb.suramericana.com/saludweb'
BASE_PLATFORM_URL = 'https://saludweb.suramericana.com/saludweb/%s'
MENU_PATH = 'login-desplegarMenu.do'
CONSULT_USER_FORM_PATH = 'pri/admfd00001-buscar.do'
UPDATE_USER_FORM_PATH = 'pri/admin00006-grabar.do'
LOGOUT_PATH = 'pri/login-salir.do'


class SaludWebModule(BaseModule):
    """Provide functions to consult and remove
    a user on Salud Web.
    """

    def __init__(
        self,
        session: SaludWebSession,
        retiros_session: SaludWebRetirosSession,
    ) -> None:
        self.salud_web_session = session
        self.salud_web_retiros_session = retiros_session

    def _authenticate(self, use_retiros_user: bool = False) -> None:
        """Perform the SAML authentication.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        """
        if use_retiros_user:
            self.session = self.salud_web_retiros_session.authenticate()
        else:
            self.session = self.salud_web_session.authenticate()

    def _get_main_page(self) -> None:
        """Get the main page and menu. It is the last login step.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        response = self.session.get(BASE_PLATFORM_URL % MENU_PATH)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú principal.',
                detail=response.text,
            )

    def _send_consult_user_form(self, username: str) -> str:
        """Consult a user by a username.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        str
            HTML user info.

        Raises
        -------
        AutomationError
            If user could not be consulted.

        """
        data = {'modo': 'B', 'loggin': username.upper()}
        response = self.session.post(
            BASE_PLATFORM_URL % CONSULT_USER_FORM_PATH,
            data=data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _extract_user_state(self, soup: BeautifulSoup) -> bool:
        """Extract the state from the HTML user info.

        Parameters
        ----------
        soup : BeautifulSoup
            HTML user data soup.

        Returns
        -------
        bool
            Whether user is active.

        """
        state_input = soup.find('select', {'name': 'estado'})
        if not state_input:
            return False
        selected_state = cast(Tag, state_input).find(
            'option', {'selected': True}
        )
        return (
            cast(Tag, selected_state).get('value', 'I') == 'A'
            if selected_state
            else False
        )

    def _extract_user_role(self, soup: BeautifulSoup) -> str:
        """Extract the role from the HTML user info.

        Parameters
        ----------
        soup : BeautifulSoup
            HTML user data soup.

        Returns
        -------
        str
            Role of the user.

        """
        role_code_input = soup.find('input', {'name': 'codigoRol'})
        role_code = (
            cast(Tag, role_code_input).get('value')
            if role_code_input
            else None
        )
        role_name_input = soup.find('input', {'name': 'nombreRol'})
        role_name = (
            cast(Tag, role_name_input).get('value')
            if role_name_input
            else None
        )
        return f'{role_code} - {role_name}'

    def _get_user_info_soup(self, user_html_info: str) -> BeautifulSoup:
        """Returns a BeautifulSoup instance of the HTML user info.

        Parameters
        ----------
        user_html_info : str
            HTML user info.

        Returns
        -------
        BeautifulSoup
            HTML user data soup.

        Raises
        -------
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(user_html_info)
        name_input = soup.find('input', {'name': 'nombre'})
        if not name_input or not cast(Tag, name_input).get('value'):
            raise NotFoundError()
        return soup

    def _create_user_data_dto(self, user_info: str) -> SaludWebResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        SaludWebResponseDto
            Requested user data.

        """
        soup = self._get_user_info_soup(user_info)
        role = self._extract_user_role(soup)
        active = self._extract_user_state(soup)
        return SaludWebResponseDto(active=active, role=role)

    def _disable_user(self, user_info: str) -> str:
        """Disable a user.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        str
            Disabled user HTML info.

        Raises
        ------
        AutomationError
            If user could not be disabled.

        """
        soup = get_html_soup(user_info)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de actualización del usuario.',
        )
        form_data.action = BASE_PLATFORM_URL % UPDATE_USER_FORM_PATH
        form_data.data.update(
            {
                'accionesCambiadas': 'S',
                'estado': 'I',
                'codigoRol': '0',
                'nombreRol': 'GUEST',
            }
        )
        response = self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo retirar el usuario.',
                detail=response.text,
            )
        return response.text

    def _logout(self, use_retiros_user: bool = False) -> None:
        """Logout and close the session.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Logout with the Bot Retiros user, by default False.

        """
        self.session.get(BASE_PLATFORM_URL % LOGOUT_PATH)
        response = self.session.get(
            'https://seus.suramericana.com/acs/logout?service=Saludweb'
        )
        soup = get_html_soup(response.text)
        form_data = get_form_data(soup)
        response = self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
        )
        soup = get_html_soup(response.text)
        form_data = get_form_data(soup)
        self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
        )
        if use_retiros_user:
            self.salud_web_retiros_session.logout()
        else:
            self.salud_web_session.logout()

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> SaludWebResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        SaludWebResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            self._get_main_page()
            user_info = self._send_consult_user_form(username)
            return self._create_user_data_dto(user_info)
        except Exception as e:
            if not isinstance(e, NotFoundError):
                self._logout()
            raise e

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> SaludWebRemovedUserResponseDto:
        """Disables a user and removes its role
        on Salud Web platform.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        SaludWebRemovedUserResponseDto
            Disabled user data.

        Raises
        ------
        Exception
            Generic exception.
        """

        try:
            self._authenticate(use_retiros_user=True)
            self._get_main_page()
            user_info = self._send_consult_user_form(username)
            disabled_user_info = self._disable_user(user_info)
            user_data = self._create_user_data_dto(disabled_user_info)
            return SaludWebRemovedUserResponseDto(
                message='Usuario retirado correctamente.',
                active=user_data.active,
                role=user_data.role,
            )
        except Exception as e:
            if not isinstance(e, NotFoundError):
                self._logout(use_retiros_user=True)
            raise e

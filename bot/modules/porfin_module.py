from typing import Any

import xmltodict

from dtos.automation_dto import (
    PorfinApplication,
    PorfinRemoveUserResponseDto,
    PorfinResponseDto,
)
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.porfin_retiros_session import PorfinRetirosSession
from lib.porfin_session import PorfinForm, PorfinSession
from lib.task_queue_manager import manager
from lib.tools import async_retry

APP_URL = 'https://inversiones.suranet.com/seguro_sura/FrameworkServlet'


class PorfinModule(BaseModule):
    """Provide a function to consult the data of
    a user on Porfin.
    """

    def __init__(
        self,
        porfin: PorfinSession,
        porfin_retiros: PorfinRetirosSession,
    ) -> None:
        self.porfin_session = porfin
        self.porfin_retiros_session = porfin_retiros

    def _authenticate(self, use_retiros_user: bool = False) -> None:
        """Perform the authentication.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        """
        if use_retiros_user:
            self.session = self.porfin_retiros_session.authenticate()
        else:
            self.session = self.porfin_session.authenticate()

    def _check_user_exist(self, user_info_entry: list[Any]) -> bool:
        """Check if user exists.

        Parameters
        ----------
        user_info_entry : list[Any]
            XML-parsed user info entry.

        Returns
        -------
        bool
            Whether user exists.

        """
        try:
            return bool(user_info_entry[0]['R']['F'][0]['@V'])
        except (IndexError, KeyError):
            return False

    def _extract_state(self, user_info_entry: list[Any]) -> bool:
        """Extract the state from the user info entry.

        Parameters
        ----------
        user_info_entry : list[Any]
            XML-parsed user info entry.

        Returns
        -------
        bool
            Whether user is active.

        """
        try:
            return str(user_info_entry[0]['R']['F'][8]['@V']).strip() == 'A'
        except (IndexError, KeyError):
            return False

    def _extract_roles(self, user_info_entry: list[Any]) -> list[str]:
        """Extract the roles from the user info entry.

        Parameters
        ----------
        user_info_entry : list[Any]
            XML-parsed user info entry.

        Returns
        -------
        list[str]
            Roles of the user.

        """
        try:
            return [
                str(role['F']['@V']) for role in user_info_entry[1]['R'][1:]
            ]
        except (IndexError, KeyError):
            return []

    def _extract_applications(
        self,
        user_info_entry: list[Any],
    ) -> list[PorfinApplication]:
        """Extract the authorized applications
        from the user info entry.

        Parameters
        ----------
        user_info_entry : list[Any]
            XML-parsed user info entry.

        Returns
        -------
        list[PorfinApplication]
            Authorized applications of the user.

        """
        try:
            applications = []
            for application in user_info_entry[3]['R'][1:]:
                product = application['F'][0]['@V']
                code = application['F'][1]['@V']
                description = application['F'][2]['@V']
                applications.append(
                    PorfinApplication(
                        product=product,
                        code=code,
                        description=description,
                    )
                )
            return applications
        except (IndexError, KeyError):
            return []

    def _extract_user_data(self, user_data_response: str) -> PorfinResponseDto:
        """Extract the user data.

        Parameters
        ----------
        user_data_response : str
            USer data response content.

        Returns
        -------
        PorfinResponseDto
            User data.

        Raises
        ------
        AutomationError
            If user could not be validated.
        NotFoundError
            If user was not found.

        """
        try:
            data = xmltodict.parse(user_data_response.strip())
            user_info_entry = data['C1']['XD'].get('D')
        except Exception as e:
            raise AutomationError(
                'No se pudo validar la existencia del usuario.',
                detail=f'CONTENT: {user_data_response}; ERROR: {str(e)}',
            ) from e

        if not user_info_entry or not isinstance(user_info_entry, list):
            raise NotFoundError()
        if not self._check_user_exist(user_info_entry):
            raise NotFoundError()

        return self._create_user_data_dto(user_info_entry)

    def _create_user_data_dto(
        self,
        user_info_entry: list[dict[str, Any]],
    ) -> PorfinResponseDto:
        """Create a response DTO from a user info entry list.

        Parameters
        ----------
        user_info_entry : list[dict[str, Any]]
            User data entry list.

        Returns
        -------
        PorfinResponseDto
            Requested user data.

        """
        return PorfinResponseDto(
            active=self._extract_state(user_info_entry),
            roles=self._extract_roles(user_info_entry),
            applications=self._extract_applications(user_info_entry),
        )

    @async_retry(3)
    def _find_user(self, hc2: str, hc3: str, username: str) -> str:
        """Find a user by its username.

        Parameters
        ----------
        hc2 : str
            Second HC value of the form.
        hc3 : str
            Third HC value of the form.
        username : str
            Username of the user to be consulted.

        Returns
        -------
        str
            User's info.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        payload = f"""
<C1 N1="com.alfagl.seguro.formas.AGMRU032" HC="{hc3}" CP="F" MOC="traer">
    <XD SI="">
        <D N="B2">
            <R ID="0" ST="I">
                <F N="usu_codigo" V="{username}" />
            </R>
        </D>
        <D N="Toolbar" />
        <D N="Footer" />
    </XD>
    <XV E4="0" T6="traer" MOC="traer" CP="F" />
    <M1 N1="processEvent" />
</C1>"""
        headers = {
            'Content-Type': 'text/plain;charset=UTF-8',
            'HC': hc3,
            'Referer': (
                f'https://inversiones.suranet.com/seguro_sura/1Seguro2013/com'
                f'/alfagl/seguro/formas/AGMRU032.jsf?cn=com.alfagl.seguro.'
                f'formas.SGENT000&qm=F&pl=F&pf=com.alfagl.seguro.formas.'
                f'SGENT000&ssty=SS&hcp={hc2}'
            ),
        }
        response = self.session.post(
            APP_URL,
            data=payload.strip(),
            headers=headers,
        )
        if not response.ok or 'Internal Error' in response.text:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _inactivate_user(self, hc2: str, hc3: str) -> str:
        """Inactivate a user.

        Parameters
        ----------
        hc2 : str
            Second HC value of the form.
        hc3 : str
            Third HC value of the form.

        Returns
        -------
        str
            Response message indicating the result of the update.

        Raises
        ------
        AutomationError
            If user could not be inactivated.

        """
        payload = f"""
<C1 N1="com.alfagl.seguro.formas.AGMRU032" HC="{hc3}" CP="F" MOC="actualizar">
    <XD TD="B100" SI="">
        <D N="B2">
            <R ID="0" ST="I">
                <F N="usu_estado" V="I" />
            </R>
        </D>
        <D N="B100" CH="T" />
    </XD>
    <XV E4="0" T6="actualizar" MOC="actualizar" CP="F" />
    <M1 N1="processEvent" />
</C1>"""
        headers = {
            'Content-Type': 'text/plain;charset=UTF-8',
            'HC': hc3,
            'Referer': (
                f'https://inversiones.suranet.com/seguro_sura/1Seguro2013/com'
                f'/alfagl/seguro/formas/AGMRU032.jsf?cn=com.alfagl.seguro.'
                f'formas.SGENT000&qm=F&pl=F&pf=com.alfagl.seguro.formas.'
                f'SGENT000&ssty=SS&hcp={hc2}'
            ),
        }
        response = self.session.post(
            APP_URL,
            data=payload.strip(),
            headers=headers,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo inactivar el usuario.',
                detail=response.text,
            )
        return response.text

    def _logout(
        self,
        use_retiros_user: bool = False,
        close_form: PorfinForm | None = None,
    ) -> None:
        """Logout and close the session.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Logout with the Bot Retiros user, by default False.
        close_form : PorfinForm | None, optional
            Form to be closed, by default None.

        """
        session = (
            self.porfin_retiros_session
            if use_retiros_user
            else self.porfin_session
        )
        if close_form:
            session.close_form(close_form)
            session.logout()

    @manager.enqueue('PorfinModule')
    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> PorfinResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        PorfinResponseDto
            User data.

        """
        porfin_form = None
        try:
            self._authenticate()
            if self.porfin_session.last_form:
                porfin_form = self.porfin_session.last_form
            else:
                porfin_form = self.porfin_session.get_new_form()
            user_data_response = self._find_user(
                hc2=porfin_form.hc2, hc3=porfin_form.hc3, username=username
            )
            return self._extract_user_data(user_data_response)
        except Exception as e:
            if not isinstance(e, NotFoundError):
                self._logout(close_form=porfin_form)
            raise e

    @manager.enqueue('PorfinModule')
    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> PorfinRemoveUserResponseDto:
        """Inactivate a user.

        Parameters
        ----------
        username : str
            Username of the user to be inactivated.

        Returns
        -------
        PorfinRemoveUserResponseDto
            Inactivation response.

        """
        porfin_form = None
        try:
            self._authenticate(use_retiros_user=True)
            porfin_form = self.porfin_retiros_session.get_new_form()
            self._find_user(
                hc2=porfin_form.hc2, hc3=porfin_form.hc3, username=username
            )
            self._inactivate_user(hc2=porfin_form.hc2, hc3=porfin_form.hc3)
            return PorfinRemoveUserResponseDto(
                active=False,
                message='Usuario retirado correctamente.',
            )
        except Exception as e:
            if not isinstance(e, NotFoundError):
                self._logout(use_retiros_user=True, close_form=porfin_form)
            raise e

import re
from codecs import unicode_escape_decode
from typing import cast
from urllib.parse import quote, urljoin

from bs4 import BeautifulSoup, Tag

from dtos.automation_dto import BandejaEscalamientoResponseDto
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.saml import SAML
from lib.tools import retry, get_form_data, get_html_soup, search_group

APP_URL = 'https://v4xw80afexlsd3a-apexohipdn.adb.us-ashburn-1.oraclecloudapps.com/ords/f?p=105'
BASE_PLATFORM_URL = 'https://v4xw80afexlsd3a-apexohipdn.adb.us-ashburn-1.oraclecloudapps.com/ords/%s'
REGION_ID_REGEX = r'"regionId": ?"([^"]+)"{1}'
P_REQUEST_REGEX = r'"ajaxIdentifier": ?"([^"]+)"{1}'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'}
TABLE_COLUMNS_COUNT = 4


class BandejaEscalamientoModule(BaseModule):
    """Provide a function to consult the role of
    a user on Bandeja de Escalamiento.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def authenticate(self) -> None:
        """Perform the SAML authentication."""
        self.session = self.saml.authenticate(APP_URL)

    def _get_main_page(self) -> str:
        """Get the main page and menu. It is the last login step.

        Returns
        ------
        str
            Main page content.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        login_response = self.saml.get_login_response()
        soup = get_html_soup(login_response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de la página principal.',
        )
        response = self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú de la página principal.',
                detail=response.text,
            )
        return response.text

    def _extract_users_form_url(self, main_page_content: str) -> str:
        """Extract the URL of the users form.

        Parameters
        ----------
        main_page_content : str
            Main page content.

        Returns
        -------
        str
            The URL of the users form.

        Raises
        ------
        AutomationError
            If URL was not found.

        """
        matches = re.search(
            r'<a href="([^\"]+)" [^>]+>Usuarios<\/a>',
            main_page_content,
        )
        if not matches:
            raise AutomationError(
                'No se encontró la URL de la página de usuarios.',
                detail=main_page_content,
            )
        users_form_path = matches.group(1)
        if not users_form_path:
            detail = f'CONTENT: {main_page_content}; MATCH: {matches.group(0)}'
            raise AutomationError(
                'No se encontró la URL de la página de usuarios.',
                detail=detail,
            )
        return urljoin(APP_URL, users_form_path)

    def _get_users_form(self, main_page_content: str) -> str:
        """Get the users form content.

        Parameters
        ----------
        main_page_content : str
            Main page content.

        Returns
        -------
        str
            Users form content.

        Raises
        ------
        AutomationError
            If form could not be fetched.

        """
        users_form_url = self._extract_users_form_url(main_page_content)
        response = self.session.get(users_form_url)
        if not response.ok:
            raise AutomationError(
                'No se encontró la URL de la página de usuarios.',
                detail=f'CONTENT: {response.text}; URL: {users_form_url}',
            )
        return response.text

    def _extract_p_request_field(self, users_form_content: str) -> str | None:
        """Extract the p_request field from
        the users form content.

        Parameters
        ----------
        users_form_content : str
            Users form content.

        Returns
        -------
        str | None
            The value of the p_request field.

        """
        encoded_p_request = search_group(P_REQUEST_REGEX, users_form_content)
        if not encoded_p_request:
            return None
        decoded = unicode_escape_decode(encoded_p_request)[0]
        return quote(f'PLUGIN={decoded}', safe='')

    def _extract_x01_users_form_field(
        self,
        soup: BeautifulSoup,
        region_id: str | None,
    ) -> str | None:
        """Extract the x01 field from the users
        form soup using the passed region_id.

        Parameters
        ----------
        soup : BeautifulSoup
            Users form soup.
        region_id : str | None
            The value of the region_id field.

        Returns
        -------
        str | None
            The value of the x01 field.

        """
        if not region_id:
            return None
        x01_element = soup.find(id=f'{region_id}_worksheet_id')
        x01 = cast(Tag, x01_element).get('value') if x01_element else None
        return str(x01) if x01 else None

    def _extract_x02_users_form_field(
        self,
        soup: BeautifulSoup,
        region_id: str | None,
    ) -> str | None:
        """Extract the x02 field from the users
        form soup using the passed region_id.

        Parameters
        ----------
        soup : BeautifulSoup
            Users form soup.
        region_id : str | None
            The value of the region_id field.

        Returns
        -------
        str | None
            The value of the x02 field.

        """
        if not region_id:
            return None
        x02_element = soup.find(id=f'{region_id}_report_id')
        x02 = cast(Tag, x02_element).get('value') if x02_element else None
        return str(x02) if x02 else None

    def _extract_p_json_users_form_field(
        self, soup: BeautifulSoup
    ) -> str | None:
        """Extract the p_json field from
        the users form soup.

        Parameters
        ----------
        soup : BeautifulSoup
            Users form soup.

        Returns
        -------
        str | None
            The value of the p_json field.

        """
        p_salt_element = soup.find(id='pSalt')
        p_salt = (
            cast(Tag, p_salt_element).get('value', '')
            if p_salt_element
            else None
        )
        return quote('{"pageItems":null,"salt":"%s"}' % str(p_salt))

    def _extract_users_form_fields(
        self,
        users_form_content: str,
        soup: BeautifulSoup,
        form_data: FormData,
    ) -> tuple:
        """Extract the required fields to be sent
        in the request to consult the user.

        Parameters
        ----------
        users_form_content : str
            Users form content.
        soup : BeautifulSoup
            Users form soup.
        form_data : FormData
            Users form data.

        Returns
        -------
        tuple
            Extracted required fields.

        Raises
        ------
        AutomationError
            If required fields could not be extracted.

        """
        try:
            region_id = search_group(REGION_ID_REGEX, users_form_content)
            return (
                form_data.data.get('p_flow_id'),
                form_data.data.get('p_flow_step_id'),
                form_data.data.get('p_instance'),
                self._extract_p_request_field(users_form_content),
                region_id,
                self._extract_x01_users_form_field(soup, region_id),
                self._extract_x02_users_form_field(soup, region_id),
                self._extract_p_json_users_form_field(soup),
            )
        except Exception as e:
            raise AutomationError(
                'No se pudo extraer los campos del formulario de consulta.',
                detail=f'CONTENT: {users_form_content}; ERROR: {str(e)}',
            ) from e

    def _build_users_form_payload(
        self,
        user_form_fields: tuple,
        username: str,
    ) -> str:
        """Build the payload of the request to consult the user.

        Parameters
        ----------
        user_form_fields : str
            Users form required fields.
        username : str
            Username of the user to be consulted.

        Returns
        -------
        str
            Payload.

        """
        (
            p_flow_id,
            p_flow_step_id,
            p_instance,
            p_request,
            region_id,
            x01,
            x02,
            p_json,
        ) = user_form_fields
        return '&'.join(
            [
                f'p_flow_id={p_flow_id}',
                f'p_flow_step_id={p_flow_step_id}',
                f'p_instance={p_instance}',
                'p_debug=',
                f'p_request={p_request}',
                'p_widget_name=worksheet',
                'p_widget_mod=ACTION',
                'p_widget_action=QUICK_FILTER',
                'p_widget_num_return=50',
                f'x01={x01}',
                f'x02={x02}',
                f'f01={region_id}_column_search_current_column',
                f'f01={region_id}_search_field',
                f'f01={region_id}_row_select',
                'f02=',
                f'f02={username}',
                'f02=50',
                f'p_json={p_json}',
            ]
        )

    def _send_consult_user_form(
        self, users_form_content: str, username: str
    ) -> str:
        """Consult a user.

        Parameters
        ----------
        users_form_content : str
            Users form content.
        username : str
            Username of the user to be consulted.

        Returns
        -------
        str
            User HTML info.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        soup = get_html_soup(users_form_content)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de consulta de usuarios.',
        )
        users_form_fields = self._extract_users_form_fields(
            users_form_content,
            soup,
            form_data,
        )
        action = form_data.action.replace('wwv_flow.accept', 'wwv_flow.ajax')
        response = self.session.post(
            url=BASE_PLATFORM_URL % action,
            headers=HEADERS,
            data=self._build_users_form_payload(users_form_fields, username),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _extract_user_role(self, user_html_info: str) -> str:
        """Extract the role of the user.

        Parameters
        ----------
        user_html_info : str
            User HTML info.

        Returns
        -------
        str
            Role of the user.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(user_html_info)
        cells = soup.find_all('td', {'class': 'u-tC'})
        if not cells:
            raise NotFoundError()
        if len(cells) != TABLE_COLUMNS_COUNT:
            raise NotFoundError()
        role_cell = cells[-1]
        return role_cell.text.strip()

    def _create_user_data_dto(
        self, user_info: str
    ) -> BandejaEscalamientoResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        BandejaEscalamientoResponseDto
            Requested user data.

        """
        role = self._extract_user_role(user_info)
        return BandejaEscalamientoResponseDto(role=role)

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> BandejaEscalamientoResponseDto:
        """Consult the role of an user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        BandejaEscalamientoResponseDto
            Role of the user.

        """
        try:
            self.authenticate()
            main_page_content = self._get_main_page()
            users_form_content = self._get_users_form(main_page_content)
            user_info = self._send_consult_user_form(
                users_form_content, username
            )
            return self._create_user_data_dto(user_info)
        finally:
            self.close_session()

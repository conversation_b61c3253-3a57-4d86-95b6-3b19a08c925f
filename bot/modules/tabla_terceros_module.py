from datetime import datetime

from dtos.automation_dto import (
    TablaTercerosRemovedUserResponseDto,
    TablaTercerosResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.saml import SAML
from lib.tools import retry, get_xml_soup, strip_text

APP_URL = 'https://appsseg.suranet.com/aprovisionamiento/InicioServlet'
BASE_PLATFORM_URL = 'https://appsseg.suranet.com/aprovisionamiento/%s'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded'}
CONSULT_USER_PATH = 'EmpleadoListado?tipoRetorn=XML'
DISABLE_USER_PATH = 'EmpleadoActualizar'


class TablaTercerosModule(BaseModule):
    """Provide functions to consult and remove
    a user on Tabla de Terceros.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Performs the SAML authentication.

        Parameters
        ----------
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        self.session = self.saml.authenticate(
            app_url=APP_URL,
            username=username,
            password=password,
        )

    def _get_user_info_cells(self, user_xml_info: str) -> list[str]:
        """Get the cells containing the data of the user.

        Parameters
        ----------
        user_xml_info : str
            Consulted user XML response.

        Returns
        -------
        list[str]
            List of cells containing the user data fields.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        soup = get_xml_soup(user_xml_info)
        if not soup.rows or not soup.rows.row:
            raise NotFoundError()
        cells = soup.rows.row.find_all('cell')
        if not cells:
            raise NotFoundError()
        return [strip_text(cell.text) for cell in cells]

    def _create_user_data_dto(
        self,
        user_xml_info: str,
    ) -> TablaTercerosResponseDto:
        """Create a response DTO from a user data.

        Parameters
        ----------
        user_xml_info : str
            Consulted user XML response.

        Returns
        -------
        TablaTercerosResponseDto
            Requested user data.

        """
        cells = self._get_user_info_cells(user_xml_info)
        return TablaTercerosResponseDto(
            discharge_date=cells[7],
            leaving_date=cells[8],
        )

    def _send_consult_user_form(self, document: str) -> str:
        """Consult a user.

        Parameters
        ----------
        document : str
            Document of the user to consult.

        Returns
        -------
        str
            Consulted user XML response.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        data = {
            '_search': 'true',
            'rows': '1',
            'page': '1',
            'sidx': 'DSNOMBRE',
            'sord': 'asc',
            'KKDNI': document,
            'CDCOD_DEPTNO': 'NoValido',
            'CDOFICINA': 'NoValido',
        }
        response = self.session.post(
            BASE_PLATFORM_URL % CONSULT_USER_PATH,
            headers=HEADERS,
            data=data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar al usuario.',
                detail=response.text,
            )
        return response.text

    def _build_disable_user_data(
        self,
        user_xml_info: str,
        leaving_date: str,
    ) -> dict[str, str]:
        """Build the data to disable the user.

        Parameters
        ----------
        user_xml_info : str
            Consulted user XML response.
        leaving_date : str
            Leaving date in YYYY-MM-DD format.

        Returns
        -------
        dict[str, str]
            Data to disable the user.

        """
        cells = self._get_user_info_cells(user_xml_info)
        return {
            'KKDNI': cells[1],
            'DSNOMBRE': cells[2],
            'CDCOD_DEPTNO': cells[3],
            'CDOFICINA': cells[5],
            'FEALTA': cells[7],
            'FEBAJA': leaving_date,
            'DSNOMBRE_PROVEE': cells[9],
            'DSE_MAIL': cells[11],
            'CDSUBTIPO': cells[13] if cells[13] else 'NoValido',
            'CDCIA': cells[17] if cells[17] else 'NoValido',
            'CDCIA_MATRIZ': cells[19],
            'oper': 'edit',
            'post_id': cells[0],
        }

    def _extract_result_message(
        self,
        disabled_user_response: str,
    ) -> str | None:
        """Extract the result message.

        Parameters
        ----------
        disabled_user_response : str
            Disabled user XML response.

        Returns
        -------
        str | None
            Result message.

        """
        soup = get_xml_soup(disabled_user_response)
        result_message = soup.find('TEXTOUSUARIO')
        if not result_message:
            return None
        return strip_text(result_message.text)

    def _disable_user(
        self,
        user_xml_info: str,
    ) -> TablaTercerosRemovedUserResponseDto:
        """Disable a user.

        Parameters
        ----------
        user_xml_info : str
            Consulted user XML response.

        Returns
        -------
        TablaTercerosRemovedUserResponseDto
            Removed user data.

        Raises
        ------
        AutomationError
            If user could not be disabled.

        """
        leaving_date = datetime.today().strftime('%Y-%m-%d')
        data = self._build_disable_user_data(user_xml_info, leaving_date)
        response = self.session.post(
            BASE_PLATFORM_URL % DISABLE_USER_PATH,
            headers=HEADERS,
            data=data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo dar de baja al usuario.',
                detail=f'USER INFO: {user_xml_info}; RESULT: {response.text}',
            )
        result_message = self._extract_result_message(response.text)
        if result_message:
            return TablaTercerosRemovedUserResponseDto(
                leaving_date=leaving_date,
                message=result_message,
            )
        return TablaTercerosRemovedUserResponseDto(
            leaving_date=leaving_date,
            message=(
                'No se recibió ningún mensaje de respuesta.'
                ' Por favor verifique que el usuario haya sido dado de baja.'
            ),
            warning=True,
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, document: str) -> TablaTercerosResponseDto:
        """Consult the user by its document and get its data.

        Parameters
        ----------
        document : str
            Document of the user to be consulted.

        Returns
        -------
        TablaTercerosResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            user_info = self._send_consult_user_form(document)
            return self._create_user_data_dto(user_info)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(
        self, document: str
    ) -> TablaTercerosRemovedUserResponseDto:
        """Disable a user.

        Parameters
        ----------
        document : str
            Document of the user to be disabled.

        Returns
        -------
        TablaTercerosRemovedUserResponseDto
            Removed user data.

        """
        try:
            self._authenticate(
                username=str(config.RETIROS_APPS_CONNECTION_USER),
                password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
            )
            user_info = self._send_consult_user_form(document)
            return self._disable_user(user_info)
        finally:
            self.close_session()

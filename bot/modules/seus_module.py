from dataclasses import dataclass
from http.client import FORBIDDEN
from typing import Any

from requests import Response
from requests.auth import HTTPBasicAuth

from dtos.automation_dto import SEUSRemovedUserResponseDto, SEUSResponseDto
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.tools import retry

BASE_PLATFORM_URL = 'https://seusapi.suranet.com/api/%s'
MAX_PAGES = 15
REPOSITORIES = {
    'AD SOAT': 183,
    'AD Salud': 206,
    'AD Empleados': 62,
}
USER_PROFILES_PATH = 'users/users-by-profile'
USER_ROLES_PATH = 'users/users-by-roles'
USER_REPOSITORIES_PATH = 'user-information'
REMOVE_USER_PATH = 'user-information'


@dataclass
class SEUSUserData:
    """Repository and profiles of SEUS user."""

    repository: str
    profiles: list[dict[str, Any]]


class SEUSModule(BaseModule):
    """Provide a function to consult the profiles and roles
    of a user on SEUS.
    """

    def _set_basic_authorization(self, use_retiros_user: bool = False) -> None:
        """Set the Basic authentication.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        """
        if use_retiros_user:
            self.session.auth = HTTPBasicAuth(
                username=str(config.SEUS_RETIROS_USER),
                password=str(config.SEUS_RETIROS_PASSWORD),
            )
        else:
            self.session.auth = HTTPBasicAuth(
                username=str(config.SEUS_USER),
                password=str(config.SEUS_PASSWORD),
            )

    def _build_requests_payload(
        self,
        username: str,
        repository_id: int,
        page: int,
    ) -> dict[str, Any]:
        """Build the payload for the requests.

        Parameters
        ----------
        username : str
            Username of the user to consult.
        repository_id : int
            ID of repository.
        page : int
            Page.

        Returns
        -------
        dict[str, Any]
            Payload.

        """
        return {
            'onlyActive': False,
            'findIndirectProfile': True,
            'limit': 50,
            'page': page,
            'userByRepository': {
                'repositoryId': repository_id,
                'userFilter': {'user': username.strip(), 'userBy': 'L'},
            },
        }

    def _raise_if_forbidden(self, response: Response) -> None:
        """Raise if response status code is forbidden."""
        if response.status_code == FORBIDDEN:
            raise AutomationError(
                message=(
                    'El usuario de conexión no tiene los permisos'
                    ' suficientes para realizar esta consulta.'
                ),
                detail=response.text,
            )

    def _get_repo_id(self, repository: str) -> int:
        """Get the ID of the repository.

        Parameters
        ----------
        repository : str
            Name of the repository.

        Returns
        -------
        int
            ID of the repository.

        Raises
        ------
        AutomationError
            If repo ID is not found.

        """
        repo_id = REPOSITORIES.get(repository)
        if not repo_id:
            raise AutomationError(
                f'No se conoce el ID del repositorio "{repository}".'
            )
        return repo_id

    def _get_user_info_by_repo(self, username: str) -> list[dict[str, Any]]:
        """Get user info by repository.

        Parameters
        ----------
        username : str
            Username of the user to consult.

        Returns
        -------
        list[dict[str, Any]]
            Info of the user in each repository.

        Raises
        ------
        AutomationError
            If user data could not be fetched.
        NotFoundError
            If user was not found.

        """
        response = self.session.post(
            BASE_PLATFORM_URL % USER_REPOSITORIES_PATH,
            json={'username': username},
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener los repositorios del usuario.',
                detail=response.text,
            )
        data = response.json()
        if not data:
            raise NotFoundError()
        return data

    def _get_user_profiles(
        self,
        username: str,
        repo_id: int,
    ) -> list[dict[str, Any]]:
        """Get the profiles of the user.

        Parameters
        ----------
        username : str
            Username of the user to consult.
        repo_id : int
            ID of the repository of the user.

        Returns
        -------
        list[dict[str, Any]]
            Profiles of the user.

        Raises
        ------
        AutomationError
            If error when getting the profiles of the user.

        """
        page = 0
        profiles = []
        while page < MAX_PAGES:
            response = self.session.post(
                BASE_PLATFORM_URL % USER_PROFILES_PATH,
                json=self._build_requests_payload(username, repo_id, page),
            )
            if not response.ok:
                self._raise_if_forbidden(response)
                raise AutomationError(
                    message=(
                        'No se pudo enviar la petición para'
                        ' obtener los perfiles del usuario.'
                    ),
                    detail=response.text,
                )
            response_data = response.json()
            data = response_data.get('data')
            if not data:
                break
            profiles.extend(data)
            # If numPages is 0 or null, there is not more data to fetch
            if not response_data.get('numPages'):
                break
            page += 1
        return profiles

    def _get_user_roles(
        self,
        username: str,
        repo_id: int,
    ) -> list[dict[str, Any]]:
        """Get the roles of a user.

        Parameters
        ----------
        username : str
            Username of the user to consult.
        repo_id : int
            ID of the repository of the user.

        Returns
        -------
        list[dict[str, Any]]
            Roles of the user.

        Raises
        ------
        AutomationError
            If error when getting the roles of the user.

        """
        page = 0
        roles = []
        while page < MAX_PAGES:
            response = self.session.post(
                BASE_PLATFORM_URL % USER_ROLES_PATH,
                json=self._build_requests_payload(username, repo_id, page),
            )
            if not response.ok:
                self._raise_if_forbidden(response)
                raise AutomationError(
                    message=(
                        'No se pudo enviar la petición para'
                        ' obtener los roles del usuario.'
                    ),
                    detail=response.text,
                )
            response_data = response.json()
            data = response_data.get('data')
            if not data:
                break
            roles.extend(data)
            # If numPages is 0 or null, there is not more data to fetch
            if not response_data.get('numPages'):
                break
            page += 1
        return roles

    def _create_user_data_dto(
        self,
        repository: str,
        profiles: list[dict[str, Any]],
        roles: list[dict[str, Any]],
    ) -> SEUSResponseDto:
        """Create a response DTO from a user data.

        Parameters
        ----------
        repository : str
            Repository name.
        profiles : list[dict[str, Any]]
            Profiles of the user.
        roles : list[dict[str, Any]]
            Roles of the user.

        Returns
        -------
        SEUSResponseDto
            Requested user data.

        """
        return SEUSResponseDto(
            active=profiles[0]['isUserActive'],
            repository=repository,
            profiles=[p['profileName'] for p in profiles],
            roles=[r['roleName'] for r in roles],
        )

    def _get_repos_and_payload(
        self,
        user_data: list[dict[str, Any]],
    ) -> tuple[list[str], list[dict[str, int]]]:
        """Return the repositories and the payload to
        remove a user from those repositories.

        Parameters
        ----------
        user_data : list[dict[str, Any]]
            User data.

        Returns
        -------
        tuple[list[str], list[dict[str, int]]]
            Repositories and payload.

        """
        repos = []
        payload = []
        for user in user_data:
            repos.append(user['repositoryName'])
            payload.append({'userId': user['userId']})
        return repos, payload

    def _remove_user_from_repositories(
        self,
        repos: list[str],
        user_ids: list[dict[str, int]],
    ) -> None:
        """Remove a user from the given repositories.

        Parameters
        ----------
        repos : list[str]
            Repositories.
        user_ids : list[dict[str, int]]
            User IDs.

        Raises
        ------
        AutomationError
            If user could not be removed.

        """
        response = self.session.delete(
            BASE_PLATFORM_URL % REMOVE_USER_PATH, json=user_ids
        )
        if not response.ok or not response.json()['success']:
            raise AutomationError(
                'No se pudo retirar el usuario en los siguientes '
                f'repositorios: {", ".join(repos)}',
                detail=response.text,
            )

    def _remove_user_from_all_repositories(self, username: str) -> list[str]:
        """Remove a user from all repositories.

        Parameters
        ----------
        username : str
            Username of the user to be removed.

        Returns
        -------
        list[str]
            Repositories from which the user was removed.

        """
        user_data = self._get_user_info_by_repo(username)
        repos, payload = self._get_repos_and_payload(user_data)
        self._remove_user_from_repositories(repos, payload)
        return repos

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> SEUSResponseDto:
        """Get the user profiles and roles.

        Parameters
        ----------
        username : str
            Username of the user to consult.

        Returns
        -------
        SEUSResponseDto
            Requested user data.

        """
        try:
            self.create_session()
            self._set_basic_authorization()
            info_by_repo = self._get_user_info_by_repo(username)
            repository = info_by_repo[0]['repositoryName']
            repo_id = self._get_repo_id(repository)
            profiles = self._get_user_profiles(username, repo_id)
            roles = self._get_user_roles(username, repo_id)
            return self._create_user_data_dto(repository, profiles, roles)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> SEUSRemovedUserResponseDto:
        """Remove a user from all repositories.

        Parameters
        ----------
        username : str
            Username of the user to be removed.

        Returns
        -------
        SEUSRemovedUserResponseDto
            Removed user data.

        """
        try:
            self.create_session()
            self._set_basic_authorization(use_retiros_user=True)
            repos = self._remove_user_from_all_repositories(username)
            return SEUSRemovedUserResponseDto(
                message=(
                    'Usuario retirado correctamente de los siguientes '
                    f'repositorios: {", ".join(repos)}'
                )
            )
        finally:
            self.close_session()

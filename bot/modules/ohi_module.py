from http.client import NOT_FOUND
from typing import Any

from requests.auth import HTT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from dtos.automation_dto import OHIRemovedUserResponseDto, OHIResponseDto
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.tools import async_retry

APP_URL = 'https://idcs-fc4a28e7a1fe4ad8954b17c5362764ae.identity.oraclecloud.com/oauth2/v1/token/'
BASE_PLATFORM_URL = 'https://sura-ohi-api.oracleindustry.com/cla-api/%s'


class OHIModule(BaseModule):
    """Provide functions to consult and remove
    a user on OHI.
    """

    def _authenticate(self) -> str:
        """Perform the authentication.

        Returns
        -------
        str
            Access token.

        Raises
        ------
        AutomationError
            If authentication could not be performed.
        AutomationError
            If access token could not be obtained.
        AutomationError
            If access token was not found.

        """
        auth = HTTPBasicAuth(
            username=str(config.OHI_CLIENT_ID),
            password=str(config.OHI_CLIENT_SECRET),
        )
        data = {
            'grant_type': 'client_credentials',
            'scope': 'https://sura-ohi-api.oracleindustry.com/urn::cla-api',
        }
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        response = self.session.post(
            APP_URL,
            auth=auth,
            headers=headers,
            data=data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo realizar la autenticación.',
                detail=response.text,
            )
        try:
            access_token = response.json().get('access_token')
        except Exception as e:
            raise AutomationError(
                'No se pudo obtener el token de acceso.',
                detail=f'CONTENT: {response.text}; ERROR: {str(e)}',
            ) from e
        if not access_token:
            raise AutomationError(
                'No se encontró el token de acceso.',
                detail=response.text,
            )
        return access_token

    def _send_consult_user_form(
        self,
        access_token: str,
        username: str,
    ) -> Any:
        """Consult a user and return its data.

        Parameters
        ----------
        access_token : str
            Access token.
        username : str
            Username of the user to be consulted.

        Returns
        -------
        Any
            User data.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If user could not be consulted.

        """
        consult_user_path = f'users/{username}'
        headers = {
            'Accept': 'application/json;expand=all',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}',
        }
        response = self.session.get(
            BASE_PLATFORM_URL % consult_user_path,
            headers=headers,
        )
        if response.status_code == NOT_FOUND:
            raise NotFoundError()
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.json()

    def _create_user_data_dto(
        self,
        user_data: Any,
    ) -> OHIResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_data : Any
            User data object.

        Returns
        -------
        OHIResponseDto
            Requested user data.

        """
        return OHIResponseDto(
            active=user_data.get('active', False),
            roles=user_data.get('userRoleList', []),
        )

    def _disable_user(self, access_token: str, username: str) -> None:
        """Disable a user.

        Parameters
        ----------
        access_token : str
            Access token.
        username : str
            Username of the user to be disabled.

        Raises
        ------
        NotFoundError
            If user was not found.
        AutomationError
            If user could not be disabled.

        """
        headers = {
            'Accept': 'application/json;expand=all',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}',
        }
        data = {'loginName': username, 'active': False, 'userRoleList': []}
        response = self.session.put(
            BASE_PLATFORM_URL % 'users',
            headers=headers,
            json=data,
        )
        if response.status_code == NOT_FOUND:
            raise NotFoundError()
        if not response.ok:
            raise AutomationError(
                'No se pudo desactivar el usuario.',
                detail=response.text,
            )

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> OHIResponseDto:
        """Get the data of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        OHIResponseDto
            Data of the user.

        """
        try:
            self.create_session()
            access_token = self._authenticate()
            user_data = self._send_consult_user_form(access_token, username)
            return self._create_user_data_dto(user_data)
        finally:
            self.close_session()

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> OHIRemovedUserResponseDto:
        """Disable a user.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        OHIRemovedUserResponseDto
            Disabled user response.

        """
        try:
            self.create_session()
            access_token = self._authenticate()
            user_data = self._send_consult_user_form(access_token, username)
            self._disable_user(access_token, username)
            return OHIRemovedUserResponseDto(
                active=False,
                removed_roles=user_data.roles,
                message='Usuario retirado correctamente.',
            )
        finally:
            self.close_session()

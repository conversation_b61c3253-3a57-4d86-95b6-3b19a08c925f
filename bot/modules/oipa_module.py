from typing import Any

from dtos.automation_dto import OIPARemovedUserResponseDto, OIPAResponseDto
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.tools import retry, get_nested

ACTIVE_FLAG_VALUE = '01'
BASE_API_URL = 'http://corevidadsg.suramericana.com/EquisoftDesign/api/v1/%s'
HEADERS = {
    'accept': '*/*',
    'Content-Type': 'application/json',
}


class OIPAModule(BaseModule):
    """Provide functions to consult and remove
    a user on OIPA.
    """

    def _login(self) -> None:
        """Perform the login.

        Raises
        ------
        AutomationError
            If login fails.

        """
        data = {
            'username': str(config.OIPA_USER),
            'password': str(config.OIPA_PASSWORD),
            'loginEnvironmentId': config.OIPA_ENV,
        }
        response = self.session.post(BASE_API_URL % 'login', json=data)
        if not response.ok:
            raise AutomationError(
                'No se pudo iniciar sesión en OIPA.',
                detail=response.text,
            )

    def _fetch_user(self, username: str) -> dict[str, Any]:
        """Fetch a user.

        Parameters
        ----------
        username : str
            Username to fetch.

        Returns
        -------
        dict[str, Any]
            Data of the user.

        Raises
        ------
        AutomationError
            If user could not be fetched.
        NotFoundError
            If user was not found.

        """
        fetch_user_path = f'oipa/users/search?searchParam={username}'
        response = self.session.get(BASE_API_URL % fetch_user_path)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario en OIPA.',
                detail=response.text,
            )
        users = response.json()
        if not users:
            raise NotFoundError()
        return users[0]

    def _get_inactivate_user_payload(
        self, user_data: dict[str, Any]
    ) -> dict[str, Any]:
        deleted_groups = [
            {
                'securityGroupName': group['securityGroupName'],
                'effectiveFrom': group['effectiveFrom'],
                'effectiveTo': group['effectiveTo'],
            }
            for group in user_data['userSecurityGroups']
        ]
        return {
            'firstName': user_data['firstName'],
            'lastName': user_data['lastName'],
            'gender': user_data['sex'],
            'email': user_data['email'],
            'localeCode': user_data['localCode'],
            'password': None,
            'userStatus': '02',
            'userSecurityGroups': [],
            'deletedUserSecurityGroups': deleted_groups,
        }

    def _inactivate_user(
        self, user_data: dict[str, Any]
    ) -> OIPARemovedUserResponseDto:
        """Inactivate a user and delete its security groups.

        Parameters
        ----------
        user_data : dict[str, Any]
            Data of the user to be inactivated.

        Returns
        -------
        OIPARemovedUserResponseDto
            Inactivated user response.

        Raises
        ------
        AutomationError
            If user could not be inactivated.

        """
        username = user_data['clientNumber']
        inactivate_user_path = f'oipa/users/{username}'
        data = self._get_inactivate_user_payload(user_data)
        response = self.session.put(
            BASE_API_URL % inactivate_user_path,
            headers=HEADERS,
            json=data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo retirar el usuario en OIPA.',
                detail=response.text,
            )

        updated_user = response.json()
        return self._create_inactivated_user_data_dto(updated_user, user_data)

    def _create_inactivated_user_data_dto(
        self,
        inactivated_user: dict[str, Any],
        user_data: dict[str, Any],
    ) -> OIPARemovedUserResponseDto:
        """Create a response DTO from an inactivated user data.

        Parameters
        ----------
        inactivated_user : dict[str, Any]
            Inactivated user data.
        user_data : dict[str, Any]
            User data before inactivation.

        Returns
        -------
        OIPARemovedUserResponseDto
            Inactivated user response.

        """
        active = inactivated_user.get('userStatus') == ACTIVE_FLAG_VALUE
        return OIPARemovedUserResponseDto(
            active=active,
            warning=active,
            removed_groups=[
                group['securityGroupName']
                for group in user_data['userSecurityGroups']
            ],
            message='No se pudo inactivar el usuario.'
            if active
            else 'Usuario retirado correctamente.',
        )

    def _extract_company(self, user_data: dict[str, Any]) -> str:
        """Extract the company field of the user data.

        Parameters
        ----------
        user_data : dict[str, Any]
            User data.

        Returns
        -------
        str
            Company name.

        """
        return get_nested(user_data, 'company').get('companyName', 'N/A')

    def _create_user_data_dto(
        self,
        user_data: dict[str, Any],
    ) -> OIPAResponseDto:
        """Create a response DTO from a user data.

        Parameters
        ----------
        user_data : dict[str, Any]
            User data.

        Returns
        -------
        OIPAResponseDto
            Requested user data.

        """
        groups = [
            group.get('securityGroupName', 'N/A')
            for group in user_data.get('userSecurityGroups', [])
        ]
        return OIPAResponseDto(
            active=user_data.get('userStatus') == ACTIVE_FLAG_VALUE,
            company=self._extract_company(user_data),
            groups=groups,
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> OIPAResponseDto:
        """Get the user's company and groups on OIPA.

        Parameters
        ----------
        username : str
            Username of the user to consult.

        Returns
        -------
        OIPAResponseDto
            Company and groups of the user.

        """
        try:
            self.create_session()
            self._login()
            user_data = self._fetch_user(username)
            return self._create_user_data_dto(user_data)
        finally:
            self.close_session()

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> OIPARemovedUserResponseDto:
        """Inactivate a user on OIPA.

        Parameters
        ----------
        username : str
            Username of the user to be inactivated.

        Returns
        -------
        OIPARemovedUserResponseDto
            Inactivated user response.

        """
        try:
            self.create_session()
            self._login()
            user_data = self._fetch_user(username)
            return self._inactivate_user(user_data)
        finally:
            self.close_session()

import re
from typing import cast
from urllib.parse import urljoin

from bs4 import BeautifulSoup, Tag

from dtos.automation_dto import SEEEResponseDto
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.http.sessions import TLSSession
from lib.interfaces import FormData
from lib.saml import SAML
from lib.tools import (
    async_retry,
    extract_digits,
    get_form_data,
    get_html_soup,
    strip_text,
)

APP_URL = 'https://seee.suranet.com/workflowweb/default.aspx'
BASE_PLATFORM_URL = 'https://seee.suranet.com/workflowweb/%s'
MAIN_PAGE_PATH = 'default.aspx/default.aspx'
ALLOWED_DOCUMENT_TYPES = 'A|C|D|E|F|N|P|X|TF|TP|TE|TS'
DEFAULT_DOCUMENT_TYPE = 'C'
USERS_FORM_PATH = (
    'admon/seguridad/CreacionUsuarios.aspx?slinknombre'
    '=Creaci%F3n%20Usuarios&vartransfer='
)


class SEEEModule(BaseModule):
    """Provide a function to consult the roles of
    a user on SEEE.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(self) -> None:
        """Perform the SAML authentication."""
        self.session = self.saml.authenticate(
            APP_URL, TLSSession(timeout=(20, 35))
        )

    def _get_main_page(self) -> None:
        """Gets the main page and menu. It is the last login step.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        response = self.session.get(BASE_PLATFORM_URL % MAIN_PAGE_PATH)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú de la página principal.',
                detail=response.text,
            )

    def _extract_document_type(self, document: str) -> str:
        """Extract the document type.

        Parameters
        ----------
        document : str
            Document of the user.

        Returns
        -------
        str
            Document type.

        """
        match = re.match(f'^({ALLOWED_DOCUMENT_TYPES}).+', document)
        if not match:
            return DEFAULT_DOCUMENT_TYPE
        document_type = match.group(1)
        if not document_type:
            return DEFAULT_DOCUMENT_TYPE
        return document_type

    def _get_users_form(self, document: str) -> FormData:
        """Get the users consulting form.

        Parameters
        ----------
        document : str
            Document to consult.

        Returns
        -------
        FormData
            Users consulting form.

        Raises
        ------
        AutomationError
            If form could not be obtained.

        """
        response = self.session.get(BASE_PLATFORM_URL % USERS_FORM_PATH)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de consulta de usuarios.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de consulta de usuarios.',
        )
        form_data.data['strOperacion'] = 'BU'
        form_data.data['cdtipoID'] = self._extract_document_type(document)
        form_data.data['txtcdID'] = extract_digits(document)
        form_data.action = urljoin(response.url, form_data.action)
        return form_data

    def _extract_branches(self, soup: BeautifulSoup) -> list[str]:
        """Extract the branches.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the user HTML info.

        Returns
        -------
        list[str]
            Branches.

        """
        branches = []
        branches_table = soup.find(id='gridobjetoramo_dgr')
        if branches_table:
            branches_cells = cast(Tag, branches_table).find_all('td')
            for cell in branches_cells:
                cell_text = strip_text(cell.text)
                if cell_text.isdigit():
                    branches.append(cell_text)
        return branches

    def _extract_offices(self, soup: BeautifulSoup) -> list[str]:
        """Extract the offices.

        Parameters
        ----------
        soup : BeautifulSoup
            Soup of the user HTML info.

        Returns
        -------
        list[str]
            Offices.

        """
        offices = []
        offices_table = soup.find(id='gridobjetooficina_dgr')
        if offices_table:
            offices_cells = cast(Tag, offices_table).find_all('td')
            for cell in offices_cells:
                cell_text = strip_text(cell.text)
                if cell_text.isdigit():
                    offices.append(cell_text)
        return offices

    def _create_user_data_dto(
        self,
        user_info: str,
    ) -> SEEEResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        SEEEResponseDto
            Requested user data.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(user_info)
        name_input = soup.find(id='txtNombre')
        if not name_input:
            raise NotFoundError()
        return SEEEResponseDto(
            branches=self._extract_branches(soup),
            offices=self._extract_offices(soup),
        )

    def _send_consult_user_form(self, users_form: FormData) -> SEEEResponseDto:
        """Consult the user.

        Parameters
        ----------
        users_form : FormData
            Users form.

        Returns
        -------
        SEEEResponseDto
            User data.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        response = self.session.request(
            method=users_form.method,
            url=users_form.action,
            headers=headers,
            data=users_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return self._create_user_data_dto(response.text)

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, document: str) -> SEEEResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        document : str
            Document to consult.

        Returns
        -------
        SEEEResponseDto
            Data of the user.

        """
        try:
            self._authenticate()
            self._get_main_page()
            users_form = self._get_users_form(document)
            return self._send_consult_user_form(users_form)
        finally:
            self.close_session()

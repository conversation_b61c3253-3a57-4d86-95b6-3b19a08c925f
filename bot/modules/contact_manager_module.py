from dataclasses import dataclass
import json
import re
from typing import Any, cast
from urllib.parse import urlencode

from bs4 import BeautifulSoup, Tag
from requests import Response

from dtos.automation_dto import (
    ContactManagerRemovedUserResponseDto,
    ContactManagerResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.saml import SAML
from lib.tools import async_retry, filter_dict_list_by_field, get_html_soup

APP_URL = 'https://coreseguros.suramericana.com/ab/ContactManager.do'
HEADERS = {'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'}
COUNTRY_FIELD_ID = (
    'AdminUserSearchPage:UserSearchScreen:UserSearchDV'
    ':AdminUserSearchInputSet:globalAddressContainer'
    ':GlobalAddressInputSet:Country'
)
STATE_FIELD_ID = (
    'AdminUserSearchPage:UserSearchScreen:UserSearchDV'
    ':AdminUserSearchInputSet:globalAddressContainer'
    ':GlobalAddressInputSet:State'
)
CITY_FIELD_ID = (
    'AdminUserSearchPage:UserSearchScreen:UserSearchDV'
    ':AdminUserSearchInputSet:globalAddressContainer'
    ':GlobalAddressInputSet:Sura_Colombian_City'
)
USER_DETAIL_FIELDS = [
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Name:GlobalPersonNameInputSet:Prefix',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Name:GlobalPersonNameInputSet:FirstName',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Name:GlobalPersonNameInputSet:MiddleName',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Name:GlobalPersonNameInputSet:LastName',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Name:GlobalPersonNameInputSet:SecondLastName_Ext',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Name:GlobalPersonNameInputSet:Suffix',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Username',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:NamedUser',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Active',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:AccountLocked',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:VacationStatus',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:BackupUser',
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:SessionTimeout',
]
ACTIVE_FIELD_ID = (
    'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:Active'
)
LOCKED_FIELD_ID = 'UserDetailPopup:UserDetailScreen:UserDetailDV:UserDetailInputSet:AccountLocked'


@dataclass
class RemoveUserDTO:
    """Data class to remove a user."""

    initial_params: dict[str, Any]
    user_whole_info: list[dict[str, Any]]
    user_detail: dict[str, Any]
    roles_to_remove: dict[str, Any]
    edit_user_page_content_links: str


class ContactManagerModule(BaseModule):
    """Provide functions to consult and remove
    a user on Contact Manager.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Perform the SAML authentication.

        Parameters
        ----------
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        self.session = self.saml.authenticate(
            app_url=APP_URL,
            username=username,
            password=password,
        )

    def _get_main_page(self) -> Response:
        """Get the main page and menu. It is the last login step.

        Returns
        -------
        Response
            Main page response.

        Raises
        ------
        AutomationError
            If main page or menu could not be fetched.

        """
        response = self.session.get(APP_URL)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el menú de la página principal.',
                detail=response.text,
            )
        return response

    def _extract_initial_params(self, soup: BeautifulSoup) -> dict[str, Any]:
        if not soup.body or not soup.body.script:
            raise AutomationError(
                'No se encontraron los parámetros iniciales.',
                detail=soup.text,
            )
        try:
            content = re.sub(
                r'^gw\.app\.initPage\(',
                '',
                soup.body.script.text.strip(),
                re.MULTILINE | re.IGNORECASE,
            )
            content = '[' + content.replace(');', ']')
            parsed_content = json.loads(content)
            return {
                'QuickJump': '',
                'Admin:AdminMenuTree:AdminMenuTree_toggle': '',
                'Admin:MenuLinks_toggle': '',
                ':tblinks': parsed_content[0].get('links', {}).get('checksum'),
                ':tabs': parsed_content[0].get('tabs', {}).get('checksum'),
                'infoBar': parsed_content[0]
                .get('infoBar', {})
                .get('checksum'),
                'Admin:AdminMenuActions': parsed_content[0]
                .get('west', {})
                .get('actionsButton', {})
                .get('checksum'),
                'Admin:AdminMenuTree': parsed_content[0]
                .get('west', {})
                .get('menuTree', {})
                .get('checksum'),
                'Admin:MenuLinks': parsed_content[0]
                .get('west', {})
                .get('menuLinks', {})
                .get('checksum'),
                'csrfToken': self.session.cookies.get('csrfToken'),
            }
        except Exception as e:
            raise AutomationError(
                'No se pudo extraer los parámetros iniciales.',
                detail=f'CONTENT: {soup.text}; ERROR: {str(e)}',
            ) from e

    def _extract_spellcheck_iframe_url(self, soup: BeautifulSoup) -> str:
        spellcheck_frame = soup.find(id='spellcheck_frame')
        if not spellcheck_frame:
            raise AutomationError(
                'No se encontró la URL del "spellcheck_frame".',
                detail=soup.text,
            )
        spellcheck_frame_url = cast(Tag, spellcheck_frame).get('src')
        if not spellcheck_frame_url:
            raise AutomationError(
                'No se encontró la URL del "spellcheck_frame".',
                detail=soup.text,
            )
        return str(spellcheck_frame_url)

    def _get_initial_params(self, main_page_content: str) -> dict[str, Any]:
        """Get the initial params required by the next requests.

        Parameters
        ----------
        main_page_content : str
            Main page content.

        Returns
        -------
        dict[str, Any]
            Initial params.

        Raises
        ------
        AutomationError
            If the `spellcheck` iframe could not be fetched.

        """
        soup = get_html_soup(main_page_content)
        spellcheck_frame_url = self._extract_spellcheck_iframe_url(soup)
        response = self.session.get(spellcheck_frame_url)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el "spellcheck_frame".',
                detail=soup.text,
            )
        return self._extract_initial_params(soup)

    def _get_admin_page(self, initial_params: dict[str, Any]) -> None:
        """Get the admin page.

        Parameters
        ----------
        initial_params : dict[str, Any]
            Initial params.

        Raises
        ------
        AutomationError
            If admin page could not be fetched.

        """
        payload = urlencode(
            {
                **initial_params,
                'eventSource': 'TabBar:AdminTab_act',
                'objFocusId': 'TabBar:AdminTab',
            }
        )
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener la página de administración.',
                detail=response.text,
            )

    def _get_users_page(
        self,
        initial_params: dict[str, Any],
    ) -> list[dict[str, Any]]:
        """Get the users page.

        Parameters
        ----------
        initial_params : dict[str, Any]
            Initial params.

        Returns
        -------
        list[dict[str, Any]]
            If users page content.

        Raises
        ------
        AutomationError
            If users page could not be fetched.

        """
        payload = urlencode(
            {
                **initial_params,
                'eventSource': (
                    'Admin:MenuLinks:Admin_UsersAndSecurity'
                    ':UsersAndSecurity_AdminUserSearchPage_act'
                ),
            }
        )
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener la página de usuarios.',
                detail=f'CONTENT: {response.text}; PARAMS: {json.dumps(initial_params)}',
            )
        return response.json()

    def _build_consult_user_payload(
        self,
        username: str,
        initial_params: dict[str, Any],
        users_page_content: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """Build the payload to consult the user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.
        initial_params : dict[str, Any]
            Initial params.
        users_page_content : list[dict[str, Any]]
            Users page content.

        Returns
        -------
        dict[str, Any]
            Payload to consult the user.

        Raises
        ------
        AutomationError
            Payload could not be built.

        """
        try:
            users_page_tabs = users_page_content[0]['tabs']['checksum']
            return {
                'QuickJump': '',
                'Admin:AdminMenuTree:AdminMenuTree_toggle': '',
                'Admin:MenuLinks_toggle': '',
                'AdminUserSearchPage:UserSearchScreen:UserSearchDV:Username': username,
                'AdminUserSearchPage:UserSearchScreen:UserSearchDV:FirstName': '',
                'AdminUserSearchPage:UserSearchScreen:UserSearchDV:LastName': '',
                'AdminUserSearchPage:UserSearchScreen:UserSearchDV:Role': '',
                COUNTRY_FIELD_ID: 'CO',
                STATE_FIELD_ID: '',
                CITY_FIELD_ID: '',
                ':tblinks': initial_params[':tblinks'],
                ':tabs': users_page_tabs,
                'infoBar': initial_params['infoBar'],
                'Admin:AdminMenuActions': initial_params[
                    'Admin:AdminMenuActions'
                ],
                'Admin:AdminMenuTree': initial_params['Admin:AdminMenuTree'],
                'Admin:MenuLinks': initial_params['Admin:MenuLinks'],
                'csrfToken': initial_params['csrfToken'],
            }
        except Exception as e:
            raise AutomationError(
                'No se pudo construir el payload para consultar el usuario.',
                detail=(
                    f'CONTENT: {json.dumps(users_page_content)};'
                    f' PARAMS: {json.dumps(initial_params)}; ERROR: {str(e)}'
                ),
            ) from e

    def _send_consult_user_form(
        self,
        username: str,
        initial_params: dict[str, Any],
        users_page_content: list[dict[str, Any]],
    ) -> list[dict[str, Any]]:
        """Consult the user by its username.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.
        initial_params : dict[str, Any]
            Initial params.
        users_page_content : list[dict[str, Any]]
            Users page content.

        Returns
        -------
        list[dict[str, Any]]
            User data.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        data = self._build_consult_user_payload(
            username, initial_params, users_page_content
        )
        payload = urlencode(
            {
                **data,
                'eventSource': (
                    'AdminUserSearchPage:UserSearchScreen:UserSearchDV'
                    ':SearchAndResetInputSet:SearchLinksInputSet:Search_act'
                ),
                'objFocusId': (
                    'AdminUserSearchPage:UserSearchScreen:UserSearchDV'
                    ':SearchAndResetInputSet:SearchLinksInputSet:Search'
                ),
            }
        )
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar al usuario.',
                detail=(
                    f'CONTENT: {response.text};'
                    f' PARAMS: {json.dumps(initial_params)}'
                ),
            )
        return response.json()

    def _get_user_whole_info(
        self,
        username: str,
        initial_params: dict[str, Any],
        users_page_content: list[dict[str, Any]],
    ) -> list[dict[str, Any]]:
        """Get the user whole info.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.
        initial_params : dict[str, Any]
            Initial params.
        users_page_content : list[dict[str, Any]]
            Users page content.

        Returns
        -------
        list[dict[str, Any]]
            User whole info.

        Raises
        ------
        AutomationError
            If user whole info could not be fetched.

        """
        data = self._build_consult_user_payload(
            username, initial_params, users_page_content
        )
        payload = urlencode(
            {
                **data,
                'eventSource': (
                    'AdminUserSearchPage:UserSearchScreen'
                    ':UserSearchResultsLV:0:DisplayName_act'
                ),
                'objFocusId': (
                    'AdminUserSearchPage:UserSearchScreen'
                    ':UserSearchResultsLV:0:DisplayName'
                ),
            }
        )
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener los datos del usuario consultado.',
                detail=(
                    f'CONTENT: {response.text};'
                    f' PARAMS: {json.dumps(initial_params)}'
                ),
            )
        return response.json()

    def _get_edit_user_page(
        self,
        initial_params: dict[str, Any],
        user_whole_info: list[dict[str, Any]],
    ) -> list[dict[str, Any]]:
        """Get the edit user page.

        Parameters
        ----------
        initial_params : dict[str, Any]
            Initial params.
        user_whole_info : list[dict[str, Any]]
            User whole info.

        Returns
        -------
        list[dict[str, Any]]
            Edit user page content.

        Raises
        ------
        AutomationError
            Edit user page could not be fetched.

        """
        payload = urlencode(
            {
                'QuickJump': '',
                'Admin:AdminMenuTree:AdminMenuTree_toggle': '',
                'Admin:MenuLinks_toggle': '',
                ':tblinks': initial_params[':tblinks'],
                ':tabs': user_whole_info[0]['tabs']['checksum'],
                'infoBar': initial_params['infoBar'],
                'Admin:AdminMenuTree': initial_params['Admin:AdminMenuTree'],
                'Admin:MenuLinks': initial_params['Admin:MenuLinks'],
                'csrfToken': initial_params['csrfToken'],
                'eventSource': 'UserDetailPopup:UserDetailScreen:Edit_act',
                'objFocusId': 'UserDetailPopup:UserDetailScreen:Edit',
            }
        )
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener la página de edición del usuario.',
                detail=(
                    f'CONTENT: {response.text};'
                    f' PARAMS: {json.dumps(initial_params)}'
                ),
            )
        return response.json()

    def _extract_user_detail(
        self,
        content: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """Extract the detail fields of the user.

        Parameters
        ----------
        content : list[dict[str, Any]]
            Edit user page content.

        Returns
        -------
        dict[str, Any]
            User detail fields.

        Raises
        ------
        AutomationError
            If user detail fields could not be extracted.

        """
        try:
            key = 'items'
            user_data = {}
            fields = content[2][key][0][key][1][key][0][key][0][key][0][key]
            for field in fields:
                if 'id' not in field:
                    continue
                user_data[field['id']] = field.get('value', '')
            return {
                field: user_data.get(field, '') for field in USER_DETAIL_FIELDS
            }
        except Exception as e:
            raise AutomationError(
                'No se pudo extraer los detalles del usuario.',
                detail=f'CONTENT: {json.dumps(content)}; ERROR: {str(e)}',
            ) from e

    def _extract_roles_to_remove(
        self,
        edit_user_page_content: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """Extract the field name of each role
        to be removed from the user account.

        Parameters
        ----------
        edit_user_page_content : list[dict[str, Any]]
            Edit user page content.

        Returns
        -------
        dict[str, Any]
            Roles to be removed.

        Raises
        ------
        AutomationError
            If roles to be removed could not be extracted.

        """
        try:
            return {
                role.replace(':Description', ':_Checkbox'): 'true'
                for role in edit_user_page_content[4]['metaMap'].keys()
            }
        except Exception as e:
            raise AutomationError(
                'No se pudo extraer los roles a eliminar del usuario.',
                detail=(
                    f'CONTENT: {json.dumps(edit_user_page_content)};'
                    f' ERROR: {str(e)}'
                ),
            ) from e

    def _extract_edit_user_page_links(
        self,
        edit_user_page_content: list[dict[str, Any]],
    ) -> str:
        """Extract the checksum of the links field
        from the edit user page content.

        Parameters
        ----------
        edit_user_page_content : list[dict[str, Any]]
            Edit user page content.

        Returns
        -------
        str
            The checksum of the links field.

        Raises
        ------
        AutomationError
            If the checksum of the links field could not be extracted.

        """
        try:
            return edit_user_page_content[0]['links']['checksum']
        except Exception as e:
            raise AutomationError(
                message=(
                    'No se pudo extraer el checksum de los links'
                    ' de la página de edición del usuario.'
                ),
                detail=(
                    f'CONTENT: {json.dumps(edit_user_page_content)};'
                    f' ERROR: {str(e)}'
                ),
            ) from e

    def _remove_roles(
        self,
        data: RemoveUserDTO,
    ) -> dict[str, Any]:
        """Remove the roles from the user account.

        Parameters
        ----------
        data : RemoveUserDTO
            Data to remove the roles.

        Returns
        -------
        dict[str, Any]
            Removed roles response content.

        Raises
        ------
        AutomationError
            If roles could not be removed.

        """
        payload = urlencode(
            {
                'QuickJump': '',
                'Admin:AdminMenuTree:AdminMenuTree_toggle': '',
                'Admin:MenuLinks_toggle': '',
                **data.user_detail,
                **data.roles_to_remove,
                ':tblinks': data.edit_user_page_content_links,
                ':tabs': data.user_whole_info[0]['tabs']['checksum'],
                'infoBar': data.initial_params['infoBar'],
                'Admin:AdminMenuTree': data.initial_params[
                    'Admin:AdminMenuTree'
                ],
                'Admin:MenuLinks': data.initial_params['Admin:MenuLinks'],
                'csrfToken': data.initial_params['csrfToken'],
                'eventSource': (
                    'UserDetailPopup:UserDetailScreen:UserDetailDV'
                    ':UserRolesLV_tb:Remove_act'
                ),
                'objFocusId': (
                    'UserDetailPopup:UserDetailScreen:UserDetailDV'
                    ':UserRolesLV_tb:Remove'
                ),
            }
        )
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo eliminar los roles del usuario.',
                detail=(
                    f'CONTENT: {response.text};'
                    f' PARAMS: {json.dumps(data.initial_params)}'
                ),
            )
        return response.json()

    def _disable_user(
        self,
        data: RemoveUserDTO,
    ) -> dict[str, Any]:
        """Disable the user.

        Parameters
        ----------
        data : RemoveUserDTO
            Data to disable the user.

        Returns
        -------
        dict[str, Any]
            Disabled user response content.

        Raises
        ------
        AutomationError
            If user could not be disabled.

        """
        data.user_detail[ACTIVE_FIELD_ID] = 'false'
        data.user_detail[LOCKED_FIELD_ID] = 'true'
        payload = urlencode(
            {
                'QuickJump': '',
                'Admin:AdminMenuTree:AdminMenuTree_toggle': '',
                'Admin:MenuLinks_toggle': '',
                **data.user_detail,
                ':tblinks': data.edit_user_page_content_links,
                ':tabs': data.user_whole_info[0]['tabs']['checksum'],
                'infoBar': data.initial_params['infoBar'],
                'Admin:AdminMenuTree': data.initial_params[
                    'Admin:AdminMenuTree'
                ],
                'Admin:MenuLinks': data.initial_params['Admin:MenuLinks'],
                'csrfToken': data.initial_params['csrfToken'],
                'eventSource': 'UserDetailPopup:UserDetailScreen:Update_act',
                'objFocusId': 'UserDetailPopup:UserDetailScreen:Update',
            }
        )
        response = self.session.post(APP_URL, headers=HEADERS, data=payload)
        if not response.ok:
            raise AutomationError(
                'No se pudo retirar el usuario.',
                detail=(
                    f'CONTENT: {response.text};'
                    f' PARAMS: {json.dumps(data.initial_params)}'
                ),
            )
        return response.json()

    def _extract_text_field(
        self,
        user_data_fields: list[dict[str, Any]],
        field_value: Any,
        field_name: str = 'id',
    ) -> str | None:
        """Extract the value of a text field.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.
        field_value : Any
            Value of the field to extract.
        field_name : str, optional
            Name of the field to extract, by default 'id'.

        Returns
        -------
        str | None
            Value of the field if found.

        """
        field = filter_dict_list_by_field(
            user_data_fields, field_name, field_value
        )
        return field.get('editValue')

    def _extract_active_field(
        self,
        user_data_fields: list[dict[str, Any]],
    ) -> bool:
        """Extracts the active state of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        bool
            Whether user is active.

        Raises
        ------
        AutomationError
            If field not found.

        """
        active = self._extract_text_field(user_data_fields, ACTIVE_FIELD_ID)
        if active is None:
            raise AutomationError(
                'No se encontró el campo "Activo" del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            )

        return active == 'true'

    def _extract_locked_field(
        self,
        user_data_fields: list[dict[str, Any]],
    ) -> bool:
        """Extract the locked state of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        bool
            Whether user is locked.

        Raises
        ------
        AutomationError
            If field not found.

        """
        locked = self._extract_text_field(user_data_fields, LOCKED_FIELD_ID)
        if locked is None:
            raise AutomationError(
                'No se encontró el campo "Bloqueado" del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            )

        return locked == 'true'

    def _extract_roles_field(
        self,
        user_data_fields: list[dict[str, Any]],
    ) -> list[str]:
        """Extract the roles of the user.

        Parameters
        ----------
        user_data_fields : list[dict[str, Any]]
            User data fields.

        Returns
        -------
        list[str]
            Roles of the user.

        Raises
        ------
        AutomationError
            If functions could not be extracted.

        """
        field = filter_dict_list_by_field(
            user_data_fields, 'fieldLabel', 'Funciones'
        )
        try:
            user_functions = field['items'][0]['data'].get('root', [])
            return [role['c1']['text'] for role in user_functions]
        except (KeyError, IndexError, TypeError) as e:
            raise AutomationError(
                'No se pudo extraer las funciones del usuario.',
                detail=json.dumps(user_data_fields, ensure_ascii=False),
            ) from e

    def _extract_user_data_fields(
        self,
        data: list[dict[str, Any]],
    ) -> list[dict[str, Any]]:
        """Extract the user data fields.

        Parameters
        ----------
        data : list[dict[str, Any]]
            User data.

        Returns
        -------
        list[dict[str, Any]]
            User data fields.

        Raises
        -------
        NotFoundError
            If user was not found.

        """
        try:
            # Sixth "items" key contains the user data if found
            key = 'items'
            fields = data[2][key][0][key][1][key][0][key][0][key][0].get(key)
            if not fields:
                raise NotFoundError()
            return fields
        except (KeyError, IndexError, TypeError) as e:
            raise AutomationError(
                'No se pudo extraer los datos del usuario.',
                detail=json.dumps(data, ensure_ascii=False),
            ) from e

    def _create_user_data_dto(
        self,
        user_data: list[dict[str, Any]],
    ) -> ContactManagerResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_data : Any
            User data object.

        Returns
        -------
        ContactManagerResponseDto
            Requested user data.

        """
        user_data_fields = self._extract_user_data_fields(user_data)
        return ContactManagerResponseDto(
            active=self._extract_active_field(user_data_fields),
            locked=self._extract_locked_field(user_data_fields),
            roles=self._extract_roles_field(user_data_fields),
        )

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> ContactManagerResponseDto:
        """Consult the user by its username and get its data.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        ContactManagerResponseDto
            Requested user data.

        """
        try:
            self._authenticate()
            main_page_response = self._get_main_page()
            initial_params = self._get_initial_params(main_page_response.text)
            self._get_admin_page(initial_params)
            users_page_content = self._get_users_page(initial_params)
            self._send_consult_user_form(username, initial_params, users_page_content)
            user_whole_info = self._get_user_whole_info(
                username, initial_params, users_page_content
            )
            return self._create_user_data_dto(user_whole_info)
        finally:
            self.close_session()

    @async_retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(
        self,
        username: str,
    ) -> ContactManagerRemovedUserResponseDto:
        """Disable a user and remove its roles.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        ContactManagerRemovedUserResponseDto
            Removed user data.

        """
        try:
            self._authenticate(
                username=str(config.RETIROS_APPS_CONNECTION_USER),
                password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
            )
            main_page_response = self._get_main_page()
            initial_params = self._get_initial_params(main_page_response.text)
            self._get_admin_page(initial_params)
            users_page_content = self._get_users_page(initial_params)
            self._send_consult_user_form(username, initial_params, users_page_content)
            user_whole_info = self._get_user_whole_info(
                username, initial_params, users_page_content
            )
            user_data_fields = self._extract_user_data_fields(user_whole_info)
            edit_user_page_content = self._get_edit_user_page(
                initial_params, user_whole_info
            )
            user_detail = self._extract_user_detail(edit_user_page_content)
            roles_to_remove = self._extract_roles_to_remove(
                edit_user_page_content
            )
            edit_user_page_content_links = self._extract_edit_user_page_links(
                edit_user_page_content
            )
            data = RemoveUserDTO(
                initial_params,
                user_whole_info,
                user_detail,
                roles_to_remove,
                edit_user_page_content_links,
            )
            self._remove_roles(data)
            self._disable_user(data)
            return ContactManagerRemovedUserResponseDto(
                active=False,
                locked=True,
                removed_roles=self._extract_roles_field(user_data_fields),
                message='Usuario retirado correctamente.',
            )
        finally:
            self.close_session()

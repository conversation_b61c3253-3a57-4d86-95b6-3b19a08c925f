from typing import Any

from unidecode import unidecode

from dtos.automation_dto import (
    CaseTrackingRemovedUserResponseDto,
    CaseTrackingResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.tools import retry

BASE_PLATFORM_URL = 'https://segurossuraco.thecasetracking.com/%s'
LOGIN_PATH = 'api/sign_in'
LOGOUT_PATH = 'api/sign_out'
USERS_PATH = 'users'
HEADERS = {
    'Accept': 'application/json',
    'Content-type': 'application/json;charset=UTF-8',
}


class CaseTrackingModule(BaseModule):
    """Provide a function to consult the data of
    a user on Case Tracking.
    """

    def close_session(self, token: str | None = None) -> None:
        if token:
            headers = self._get_auth_token_header(token)
            headers.update(HEADERS)
            self.session.get(BASE_PLATFORM_URL % LOGOUT_PATH, headers=headers)
        return super().close_session()

    def _get_auth_token_header(self, token: str) -> dict[str, str]:
        """Return a dict containing the `AUTHTOKEN` header.

        Parameters
        ----------
        token : str
            Auth token.

        """
        return {'AUTHTOKEN': token}

    def _get_credentials(
        self,
        use_retiros_user: bool = False,
    ) -> tuple[str, str]:
        """Get the login credentials.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        Returns
        -------
        tuple[str, str]
            Login credentials.

        """
        if use_retiros_user:
            return (
                str(config.CASE_TRACKING_RETIROS_USER),
                str(config.CASE_TRACKING_RETIROS_PASSWORD),
            )
        return (
            str(config.CASE_TRACKING_USER),
            str(config.CASE_TRACKING_PASSWORD),
        )

    def _login(
        self,
        use_retiros_user: bool = False,
    ) -> str:
        """Get the auth token.

        Parameters
        ----------
        use_retiros_user : bool, optional
            Connect with the Bot Retiros user, by default False.

        Returns
        -------
        str
            Auth token.

        Raises
        ------
        AutomationError
            If login was not successful.
        AutomationError
            If login response could not be processed.

        """
        username, pwd = self._get_credentials(use_retiros_user)
        payload = {'email': username, 'password': pwd}
        response = self.session.post(
            url=BASE_PLATFORM_URL % LOGIN_PATH,
            headers=HEADERS,
            json=payload,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo iniciar sesión.',
                detail=response.text,
            )
        try:
            return response.json().get('auth_token')
        except Exception as e:
            raise AutomationError(
                'No se pudo procesar la respuesta del token de acceso.',
                detail=f'CONTENT: {response.text}; ERROR: {str(e)}',
            ) from e

    def _filter_found_users(
        self,
        fullname: str,
        users: list[dict[str, Any]],
    ) -> dict[str, Any]:
        """Filter the found users.

        Parameters
        ----------
        fullname : str
            Fullname of the user to be consulted.
        users : list[dict[str, Any]]
            Found users.

        Returns
        -------
        dict[str, Any]
            User data.

        Raises
        ------
        NotFoundError
            If user was not found.

        """
        for user in users:
            fullname_matches = (
                unidecode(user.get('name', '').lower())
                == unidecode(fullname).lower()
            )
            if (
                fullname_matches
                and user.get('id')
                and user.get('visible', True)
            ):
                return user
        raise NotFoundError()

    def _get_user(self, token: str, fullname: str) -> dict[str, Any]:
        """Get the user by its fullname.

        Parameters
        ----------
        token : str
            Auth token.
        fullname : str
            Fullname of the user to be consulted.

        Returns
        -------
        dict[str, Any]
            User data.

        Raises
        ------
        AutomationError
            If user could not be consulted.
        AutomationError
            If user response could not be processed.
        NotFoundError
            If user was not found.

        """
        response = self.session.get(
            url=BASE_PLATFORM_URL % USERS_PATH,
            headers=self._get_auth_token_header(token),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        try:
            users = response.json()
        except Exception as e:
            raise AutomationError(
                'No se pudo procesar la respuesta de consulta del usuario.',
                detail=f'CONTENT: {response.text}; ERROR: {str(e)}',
            ) from e
        return self._filter_found_users(fullname, users)

    def _get_user_category(
        self,
        token: str,
        category_id: str,
    ) -> dict[str, Any]:
        """Get the user's category data.

        Parameters
        ----------
        token : str
            Auth token.
        category_id : str
            Category ID.

        Returns
        -------
        dict[str, Any]
            Category data.

        Raises
        ------
        AutomationError
            If category could not be obtained.
        AutomationError
            If user category response could not be processed.

        """
        user_category_path = f'prm/user_categories/{category_id}'
        response = self.session.get(
            url=BASE_PLATFORM_URL % user_category_path,
            headers=self._get_auth_token_header(token),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar la categoría del usuario.',
                detail=response.text,
            )
        try:
            return response.json()
        except Exception as e:
            raise AutomationError(
                'No se pudo procesar la respuesta de consulta de categoría.',
                detail=f'CONTENT: {response.text}; ERROR: {str(e)}',
            ) from e

    def _create_user_data_dto(
        self,
        token: str,
        user: dict[str, Any],
    ) -> CaseTrackingResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        token : str
            Auth token.
        user : dict[str, Any]
            The data of the found user.

        Returns
        -------
        CaseTrackingResponseDto
            Requested user data.

        """
        user_category_id = user.get('user_category_id')
        category = {}
        if user_category_id:
            category = self._get_user_category(token, user_category_id)
        return CaseTrackingResponseDto(
            category=category.get('name', ''),
            permission_codes=category.get('permission_codes', []),
        )

    def _delete_user(self, token: str, user_id: str) -> None:
        """Delete a user.

        Parameters
        ----------
        token : str
            Auth token to delete the user.
        user_id : str
            User's ID.

        Raises
        ------
        AutomationError
            If user could not be deleted.

        """
        delete_user_path = f'users/{user_id}'
        response = self.session.delete(
            url=BASE_PLATFORM_URL % delete_user_path,
            headers=self._get_auth_token_header(token),
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo eliminar el usuario.',
                detail=response.text,
            )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, fullname: str) -> CaseTrackingResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        fullname : str
            Fullname of the user to be consulted.

        Returns
        -------
        CaseTrackingResponseDto
            Data of the user.

        """
        token = None
        try:
            self.create_session()
            token = self._login()
            user = self._get_user(token, fullname)
            user_data = self._create_user_data_dto(token, user)
            return user_data
        finally:
            self.close_session(token)

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, fullname: str) -> CaseTrackingRemovedUserResponseDto:
        """Remove a user.

        Parameters
        ----------
        fullname : str
            Fullname of the user to be removed.

        Returns
        -------
        CaseTrackingRemovedUserResponseDto
            Removed user response.

        """
        token = None
        try:
            self.create_session()
            token = self._login(use_retiros_user=True)
            user = self._get_user(token, fullname)
            self._delete_user(token, user['id'])
            return CaseTrackingRemovedUserResponseDto(
                message='Usuario retirado correctamente.'
            )
        finally:
            self.close_session(token)

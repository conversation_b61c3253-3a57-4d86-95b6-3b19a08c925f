from typing import cast

from bs4 import BeautifulSoup, Tag

from dtos.automation_dto import (
    AgendaWebRemovedUserResponseDto,
    AgendaWebResponseDto,
)
from lib import config
from lib.base_module import BaseModule
from lib.exceptions import AutomationError, NotFoundError
from lib.interfaces import FormData
from lib.saml import SAML
from lib.task_queue_manager import manager
from lib.tools import retry, get_form_data, get_html_soup, strip_text

APP_URL = 'https://agendaweb.suramericana.com/agenda'
BASE_PLATFORM_URL = 'https://agendaweb.suramericana.com/agenda/%s'
GET_ENTRY_FORM_PATH = 'login-validarUsuario.do'
SEND_ENTRY_FORM_PATH = 'login-desplegarMenu.do'
CONSULT_USER_FORM_PATH = 'pri/admfd00001-buscar.do'
UPDATE_USER_FORM_PATH = 'pri/admin00002-grabar.do'


class AgendaWebModule(BaseModule):
    """Provide functions to consult and remove
    a user on Agenda Web.
    """

    def __init__(self, saml: SAML) -> None:
        self.saml = saml

    def _authenticate(
        self,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Perform the SAML authentication.

        Parameters
        ----------
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        self.session = self.saml.authenticate(
            app_url=APP_URL,
            username=username,
            password=password,
        )

    def _get_entry_form(self) -> FormData:
        """Get the form to select the IPS and validate the user.

        Raises
        ------
        AutomationError
            If user could not be validated.

        """
        response = self.session.get(BASE_PLATFORM_URL % GET_ENTRY_FORM_PATH)
        if not response.ok:
            raise AutomationError(
                'No se pudo obtener el formulario de ingreso.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        return get_form_data(soup, 'No se encontró el formulario de ingreso')

    def _send_entry_form(self, entry_form: FormData) -> None:
        """Send the entry form. It is also the last login step.

        Parameters
        ----------
        entry_form : FormData
            Entry form.

        Raises
        ------
        AutomationError
            If form could not be sent.

        """
        entry_form.data['ips'] = '140111'
        response = self.session.request(
            method=entry_form.method,
            url=BASE_PLATFORM_URL % SEND_ENTRY_FORM_PATH,
            data=entry_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo enviar el formulario de ingreso.',
                detail=response.text,
            )

    def _extract_user_state(self, soup: BeautifulSoup) -> bool:
        """Extract the state from the HTML user info.

        Parameters
        ----------
        soup : BeautifulSoup
            HTML user data soup.

        Returns
        -------
        bool
            Whether user is active.

        """
        state_input = soup.find('input', {'name': 'estado', 'checked': True})
        if not state_input:
            return False
        state = cast(Tag, state_input).get('value', 'I')
        return strip_text(str(state)).upper() == 'A'

    def _extract_user_role(self, soup: BeautifulSoup) -> str:
        """Extract the role from the HTML user info.

        Parameters
        ----------
        soup : BeautifulSoup
            HTML user data soup.

        Returns
        -------
        str
            Role of the user.

        """
        role_code_input = soup.find('input', {'name': 'codigoRol'})
        role_code = (
            cast(Tag, role_code_input).get('value')
            if role_code_input
            else None
        )
        role_name_input = soup.find('input', {'name': 'nombreRol'})
        role_name = (
            cast(Tag, role_name_input).get('value')
            if role_name_input
            else None
        )
        return f'{role_code} - {role_name}'

    def _get_user_info_soup(self, user_info: str) -> BeautifulSoup:
        """Return a BeautifulSoup instance of the HTML user info.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        BeautifulSoup
            HTML user data soup.

        Raises
        -------
        NotFoundError
            If user was not found.

        """
        soup = get_html_soup(user_info)
        name_input = soup.find('input', {'name': 'nombre'})
        if not name_input or not cast(Tag, name_input).get('value'):
            raise NotFoundError()
        return soup

    def _create_user_data_dto(self, user_info: str) -> AgendaWebResponseDto:
        """Create a response DTO from a HTML user info.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        AgendaWebResponseDto
            Requested user data.

        """
        soup = self._get_user_info_soup(user_info)
        role = self._extract_user_role(soup)
        active = self._extract_user_state(soup)
        return AgendaWebResponseDto(active=active, role=role)

    def _send_consult_user_form(self, username: str) -> str:
        """Consult a user by a username.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        str
            User HTML info.

        Raises
        ------
        AutomationError
            If user could not be consulted.

        """
        data = {'modo': 'B', 'loggin': username.upper()}
        response = self.session.post(
            BASE_PLATFORM_URL % CONSULT_USER_FORM_PATH,
            data=data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo consultar el usuario.',
                detail=response.text,
            )
        return response.text

    def _disable_user(self, user_info: str) -> str:
        """Disable a user.

        Parameters
        ----------
        user_info : str
            HTML user info.

        Returns
        -------
        str
            Disabled user HTML info.

        Raises
        ------
        AutomationError
            If user could not be disabled.

        """
        soup = get_html_soup(user_info)
        form_data = get_form_data(
            soup,
            'No se encontró el formulario de actualización del usuario.',
        )
        form_data.action = BASE_PLATFORM_URL % UPDATE_USER_FORM_PATH
        form_data.data.pop('usuarioAutomatico', None)
        form_data.data.update(
            {
                'accionesCambiadas': 'S',
                'estado': 'I',
                'codigoRol': '0',
                'nombreRol': 'GUEST',
            }
        )
        response = self.session.request(
            method=form_data.method,
            url=form_data.action,
            data=form_data.data,
        )
        if not response.ok:
            raise AutomationError(
                'No se pudo retirar el usuario.',
                detail=response.text,
            )
        return response.text

    def _create_inactivated_user_data_dto(
        self,
        disabled_user_info: str,
    ) -> AgendaWebRemovedUserResponseDto:
        """Create a response DTO from a disabled user data.

        Parameters
        ----------
        disabled_user_info : str
            HTML info of the disabled user.

        Returns
        -------
        AgendaWebRemovedUserResponseDto
            Disabled user response.

        """
        user_data = self._create_user_data_dto(disabled_user_info)
        return AgendaWebRemovedUserResponseDto(
            message='El usuario no pudo ser desactivado.'
            if user_data.active
            else 'Usuario retirado correctamente.',
            warning=user_data.active,
            active=user_data.active,
            role=user_data.role,
        )

    @manager.enqueue('AgendaWebModule')
    @retry(times=1, raise_exceptions=(NotFoundError,))
    def consult_user(self, username: str) -> AgendaWebResponseDto:
        """Consult the data of a user.

        Parameters
        ----------
        username : str
            Username of the user to be consulted.

        Returns
        -------
        AgendaWebResponseDto
            Data of the user.

        """
        try:
            self._authenticate()
            entry_form = self._get_entry_form()
            self._send_entry_form(entry_form)
            user_info = self._send_consult_user_form(username)
            return self._create_user_data_dto(user_info)
        finally:
            self.close_session()

    @manager.enqueue('AgendaWebModule')
    @retry(times=1, raise_exceptions=(NotFoundError,))
    def remove_user(self, username: str) -> AgendaWebRemovedUserResponseDto:
        """Disable a user and remove its role.

        Parameters
        ----------
        username : str
            Username of the user to be disabled.

        Returns
        -------
        AgendaWebRemovedUserResponseDto
            Disabled user data.

        """
        try:
            self._authenticate(
                username=str(config.RETIROS_APPS_CONNECTION_USER),
                password=str(config.RETIROS_APPS_CONNECTION_PASSWORD),
            )
            entry_form = self._get_entry_form()
            self._send_entry_form(entry_form)
            user_info = self._send_consult_user_form(username)
            disabled_user_info = self._disable_user(user_info)
            return self._create_inactivated_user_data_dto(disabled_user_info)
        finally:
            self.close_session()

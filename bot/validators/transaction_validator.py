from lib.base_injectable import BaseInjectable
from lib.base_validator import BaseValidator
from lib.validator.data_validator import DataValidator
from lib.validator.validator import Validator
from services.transaction_service import TransactionService


class TransactionValidator(BaseInjectable, BaseValidator):
    """Validate request data of the transaction controller endpoints."""

    service: TransactionService

    def __init__(self, service: TransactionService) -> None:
        self.service = service

    async def validate_export(
        self,
        action: str,
        has_privileges: str,
        include_not_found: str,
        include_errors: str,
    ) -> bool:
        validator = DataValidator()
        validator.add(
            'action',
            Validator[str](action)
            .required()
            ._in(
                [
                    'all',
                    'consult',
                    'remove',
                    'previous_deletions',
                ]
            ),
        )
        validator.add(
            'hasPrivileges',
            Valida<PERSON>[str](has_privileges).required()._in(['true', 'false']),
        )
        validator.add(
            'includeNotFound',
            Validator[str](include_not_found)
            .required()
            ._in(['true', 'false']),
        )
        validator.add(
            'includeErrors',
            Validator[str](include_errors).required()._in(['true', 'false']),
        )
        return await validator.validate()

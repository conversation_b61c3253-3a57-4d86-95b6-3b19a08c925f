from lib.base_middleware import BaseMiddleware


class LanguageMiddleware(BaseMiddleware):

    async def before_dispatch(self) -> None:
        """Gets the language from the Accept-Language header."""

        await super().before_dispatch()
        accept_language = self.request.headers.get('Accept-Language')
        if accept_language is None:
            accept_language = 'es_CO'
        self.request.state.language = accept_language

import asyncio
import warnings

import urllib3
import uvicorn
from beanie import init_beanie
from pymongo import AsyncMongoClient
from pymongo.errors import OperationFailure, ServerSelectionTimeoutError
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.authentication import AuthenticationMiddleware
from starlette.middleware.cors import CORSMiddleware
from starlette.middleware.trustedhost import TrustedHostMiddleware
from urllib3.exceptions import InsecureRequestWarning

from controllers import controllers
from lib import config
from lib.consolidated_report_task import save_consolidated_report
from lib.injectables import Injectables
from lib.logger import LoggerConfig, logger
from lib.routes import Route, routes
from lib.user_session import UserSessionBackend
from middlewares.language_middleware import LanguageMiddleware
from models import models

LoggerConfig.set_conf('bot.log', '/usr/logs')
warnings.simplefilter('ignore', InsecureRequestWarning)
urllib3.disable_warnings()


middleware = [
    Middleware(
        TrustedHostMiddleware,
        allowed_hosts=['*'],
    ),
    Middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_methods=['*'],
        allow_headers=['*'],
        expose_headers=['Authorization', 'Content-Disposition'],
    ),
    Middleware(
        AuthenticationMiddleware,
        backend=UserSessionBackend(),
        on_error=UserSessionBackend.on_auth_error,
    ),
    Middleware(LanguageMiddleware),
]


async def on_startup():
    try:
        # Injectables
        Injectables.execute()

        # Database connection
        user = config.DATABASE_USER
        password = config.DATABASE_PASSWORD
        database = config.DATABASE_NAME
        client = AsyncMongoClient(
            f'mongodb://{user!s}:{password!s}@db:27017/{database}'
        )
        await init_beanie(database=client[database], document_models=models)

        # Consolidated report task
        asyncio.create_task(save_consolidated_report())

    except Exception as e:
        config.health.status = False
        if isinstance(e, ServerSelectionTimeoutError):
            config.health.error_message = 'No se pudo establecer la conexión al contenedor de la base de datos.'
        if isinstance(e, OperationFailure):
            config.health.error_message = (
                'Error en la conexión a la base de datos.'
            )
        else:
            config.health.error_message = (
                'No se pudo iniciar el servidor correctamente.'
            )
        logger.error(str(e), exc_info=True)


async def on_shutdown():
    pass


for cls in controllers:
    for name, wrapper in cls.__dict__.items():
        if hasattr(wrapper, 'func'):
            route = Route(
                wrapper.path,
                cls,
                wrapper.func,
                methods=[wrapper.method],
            )
            routes.append(route)


app = Starlette(
    debug=config.DEBUG_AND_RELOAD,
    routes=routes,
    middleware=middleware,
    on_startup=[on_startup],
    on_shutdown=[on_shutdown],
)


if __name__ == '__main__':
    uvicorn.run(
        app='main:app',
        host='0.0.0.0',
        port=8010,
        log_level='info',
        ssl_certfile=config.SSL_CERTFILE,
        ssl_keyfile=config.SSL_KEYFILE,
        ssl_ciphers=config.SSL_CIPHERS,
        ssl_version=config.SSL_VERSION,
        reload=config.DEBUG_AND_RELOAD,
    )

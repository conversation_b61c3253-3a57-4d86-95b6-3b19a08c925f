from typing import Literal

from lib.base_injectable import BaseInjectable
from lib.logger import logger

from dtos.log_dto import LogRequestDto, LogResponseDto
from dtos.automation_dto import ApplicationResultResponseDto
from models.transaction import Transaction
from services.log_service import LogService


class Log(BaseInjectable):
    """Provides the `create` function to create logs."""

    service: LogService

    def __init__(self, service: LogService) -> None:
        """Creates a dependency object of this module.

        Parameters
        ----------
        service : LogService
            Injected dependency of the logs's service.
        """

        super().__init__()
        self.service = service

    def get_state(self, result: ApplicationResultResponseDto) -> Literal['done', 'error', 'ignored', 'user_not_found']:
        """Gets the state of the log
        from the automation result.

        Returns
        -------
        Literal['done', 'error', 'ignored', 'user_not_found']
            State.
        """

        if result.ignored:
            return 'ignored'
        elif result.userNotFound:
            return 'user_not_found'
        elif result.error:
            return 'error'
        else:
            return 'done'

    async def create(self,
                    transaction: Transaction,
                    username: str,
                    moduleName: str,
                    moduleFuncArgsSpec: str,
                    moduleFuncArgs: list[object],
                    result: ApplicationResultResponseDto) -> LogResponseDto | None:
        """Creates a log.

        Parameters
        ----------
        transaction : Transaction
            `Transaction` object
        username : str
            Consulted username.
        moduleName : str
            Application module name.
        moduleFuncArgsSpec : str
            Specifications of `main` of
            the application module name.
        moduleFuncArgs : list[object]
            List of args passed to the `main` function
            of the application module name.
        result : ApplicationResultResponseDto
            Result of the execution.

        Returns
        ----------
        LogResponseDto | None
            `Log` model object if created.
        """

        try:
            created_by = None
            removed_by = None
            if transaction.action == 'consult':
                created_by = transaction.createdBy
            else:
                removed_by = transaction.createdBy
            log_dto = LogRequestDto(
                transactionId=transaction.id,
                username=username,
                consultedBy=created_by,
                removedBy=removed_by,
                moduleName=moduleName,
                moduleFuncArgsSpec=moduleFuncArgsSpec,
                moduleFuncArgs=moduleFuncArgs,
                application=result.application,
                applicationName=result.applicationName,
                status=self.get_state(result),
                action=transaction.action,
                data=result.data.to_response() if result.data else None,
                message=result.error,
                detail=result.errorDetail,
                startTime=result.startTime,
                endTime=result.endTime,
                timeDiff=result.timeDiff,
                userNotFound=result.userNotFound,
                ignored=result.ignored,
                warning=result.warning)
            return await self.service.create(log_dto)
        except Exception as e:
            logger.error(f'No se pudo crear el log para el módulo {moduleName}: {str(e)}')

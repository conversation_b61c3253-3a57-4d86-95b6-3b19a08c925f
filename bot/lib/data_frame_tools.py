import csv

from io import Bytes<PERSON>, <PERSON><PERSON>
from typing import Op<PERSON>, Literal, Any, cast
from openpyxl import load_workbook, Workbook
from openpyxl.worksheet.worksheet import Worksheet
from starlette.datastructures import UploadFile

from lib.exceptions import BadRequestError
from lib.base_injectable import BaseInjectable
from i18n import I18N


class DataFrame(BaseInjectable):
    """Reads a XLSX data frame whether from
    a file content stream or an uploaded file.

    Provides the following functions:

    - read_from_file: Reads data frame from an uploaded file.
    - read_from_bytes: Reads data frame from a file content stream.
    - get_workbook: Returns the generated `Workbook` instance.
    - get_active_worksheet: Returns the active worksheet of the `Workbook` instance.
    - get_rows: Gets the list of rows of the data frame.
    - get_row_value: Gets a value of a row of the data frame.
    """

    __workbook: Workbook
    __worksheet: Worksheet

    def __init__(self, i18n: I18N) -> None:
        """Creates a dependency object of this module.

        Parameters
        ----------
        i18n : I18N
            Injected dependency of the translation module.
        """

        self.i18n = i18n

    def __create_from_bytes(self, file_content: bytes) -> None:
        """Creates the workbook from a file content stream.

        Parameters
        ----------
        file_content : bytes
            File content stream.
        """

        self.__workbook: Workbook = load_workbook(filename=BytesIO(file_content))
        self.__worksheet = cast(Worksheet, self.__workbook.active)

    def __create_from_csv(self, file_content: bytes) -> None:
        """Creates the workbook from a CSV file content stream.

        Parameters
        ----------
        file_content : bytes
            CSV file content stream.
        """

        self.__workbook = Workbook()
        self.__worksheet = cast(Worksheet, self.__workbook.active)
        decoded = file_content.decode(errors='replace')
        csv_data = csv.reader(StringIO(decoded))
        for row in csv_data:
            self.__worksheet.append(row)

    async def read_from_file(self, file: UploadFile) -> None:
        """Reads data frame from an uploaded file.

        Parameters
        ----------
        file : UploadFile
            Uploaded file.

        Raises
        ------
        BadRequestError
            Generic error.
        """

        try:
            file_content = await file.read()
            if file.content_type == 'text/csv' or file.filename.endswith('.csv'):
                self.__create_from_csv(file_content)
            else:
                self.__create_from_bytes(file_content)

        except Exception as error:
            raise BadRequestError(self.i18n('an_error_has_occurred', format_values={'error': str(error)}))

    def read_from_bytes(self, file_content: bytes) -> None:
        """Reads data frame from a file content stream.

        Parameters
        ----------
        file_content : bytes
            File content stream.
        """

        self.__create_from_bytes(file_content)

    def get_workbook(self) -> Workbook:
        """Returns the `Workbook` instance.

        Returns
        -------
        Workbook
            Workbook instance.
        """

        return self.__workbook

    def get_active_worksheet(self) -> Worksheet:
        """Returns the active worksheet.

        Returns
        -------
        Worksheet
            Active worksheet.
        """

        return self.__worksheet

    def get_rows(self,
                min_row: Optional[int] = None,
                max_row: Optional[int] = None,
                min_col: Optional[int] = None,
                max_col: Optional[int] = None,
                values_only: Optional[Literal[True]] = None) -> list[Any]:
        """Gets the list of rows based on the
        passed arguments.

        Parameters
        ----------
        min_row : Optional[int], optional
            Min row number, by default None.
        max_row : Optional[int], optional
            Max row number, by default None.
        min_col : Optional[int], optional
            Min column number, by default None.
        max_col : Optional[int], optional
            Max column number, by default None.
        values_only : Optional[Literal[True]], optional
            Return values instead of `Cell` objects, by default None.

        Returns
        -------
        list[Any]
            List of rows.
        """

        return list(self.__worksheet.iter_rows(
            min_row=min_row,
            max_row=max_row,
            min_col=min_col,
            max_col=max_col,
            values_only=True if values_only else False))

    def get_row_value(self, row: list[Any], index: int) -> str | None:
        """Gets a value from a row.

        Parameters
        ----------
        row : list[Any]
            Row.
        index : int
            Index of the value.

        Returns
        -------
        Any
            Stringified value if exist.
        """

        return str(row[index]).strip() if row[index] is not None else None

    def row_is_empty(self, row: list[Any]) -> bool:
        """Checks if a row is empty.

        Parameters
        ----------
        row : list[Any]
            Row.

        Returns
        -------
        bool
            Whether row is empty.
        """

        for cell in row:
            if cell:
                return False
        return True

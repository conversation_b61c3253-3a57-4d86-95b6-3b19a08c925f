import ssl
from dataclasses import dataclass

from starlette.config import Config
from starlette.datastructures import Secret
from uvicorn.config import SSL_PROTOCOL_VERSION


@dataclass
class Health:
    status: bool
    error_message: str | None = None


health = Health(status=True)
_config = Config()

# General settings
PROD = _config('PROD', cast=bool, default=False)
DEBUG_AND_RELOAD = not PROD
PROTOCOL = 'https' if PROD else 'http'
SERVER_NAME = _config('SERVER_NAME', cast=str, default='localhost')
FRONTEND_PORT = (
    _config('FRONTEND_PORT', cast=int, default=8090) if PROD else 8080
)
LAB_ENV = _config('LAB_ENV', cast=bool, default=False)

# SSL settings
SSL_CERTFILE = '/etc/ssl/certs/cert.pem' if PROD else None
SSL_KEYFILE = '/etc/ssl/certs/key.pem' if PROD else None
SSL_CIPHERS = (
    _config(
        'SSL_CIPHERS',
        cast=str,
        default=(
            'EECDH+AESGCM:EDH+AESGCM:ECDHE-RSA-AES128-GCM-SHA256:AES256+EECDH:'
            'DHE-RSA-AES128-GCM-SHA256:AES256+EDH:ECDHE-RSA-AES256-GCM-SHA384:'
            'DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128'
            '-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-'
            'SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:'
            'ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128'
            '-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES'
            '-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4'
        ),
    )
    if PROD
    else 'TLSv1'
)
SSL_VERSION = ssl.PROTOCOL_TLS if PROD else SSL_PROTOCOL_VERSION

# Database settings
DATABASE_NAME = _config('MONGO_INITDB_DATABASE', cast=str, default='sura')
DATABASE_USER = _config('DATABASE_USER', cast=Secret)
DATABASE_PASSWORD = _config('DATABASE_PASSWORD', cast=Secret)

# Authentication settings
SERVICE = _config('SERVICE', cast=str, default='BotConsultaUsuarios')
SERVICE_URL = _config(
    'SERVICE_URL',
    cast=str,
    default='https://seus.sura.com/conf/configuration/sso-api',
)
SERVICE_CONF_CACHE_TIME = _config(
    'SERVICE_CONF_CACHE_TIME', cast=int, default=7
)
USE_AUTHORIZATION = _config('USE_AUTHORIZATION', cast=bool, default=False)

# Data query settings
ROWS_PER_PAGE = _config('ROWS_PER_PAGE', cast=int, default=25)
EXPORT_LIMIT = _config('EXPORT_LIMIT', cast=int, default=1000)

# PDF settings
PDF_MAIN_LOGO_IMG = _config(
    'PDF_MAIN_LOGO_IMG', cast=str, default='static/sura_logo.png'
)
PDF_SECONDARY_LOGO_IMG = _config(
    'PDF_SECONDARY_LOGO_IMG', cast=str, default='static/arus_logo.png'
)

# Mailer settings
MAILER_ENABLED = _config('MAILER_ENABLED', cast=bool, default=False)
MAILER_URL = _config(
    'MAILER_URL',
    cast=str,
    default='https://mosaico.arus.com.co:3000/mailer/withAttachments',
)
MAILER_TYPE = _config('MAILER_TYPE', cast=str, default='divulgacion_mosaico')
MAILER_CONTRACT = _config(
    'MAILER_CONTRACT', cast=str, default='5d682412e0a6e100062cc5cc'
)
MAILER_SEND_FROM = _config(
    'MAILER_SEND_FROM', cast=str, default='<EMAIL>'
)

# Automation settings
ENABLE_BOT_RETIROS = _config('ENABLE_BOT_RETIROS', cast=bool, default=False)
APPS_CONNECTION_USER = _config('APPS_CONNECTION_USER', cast=Secret)
APPS_CONNECTION_PASSWORD = _config('APPS_CONNECTION_PASSWORD', cast=Secret)
RETIROS_APPS_CONNECTION_USER = _config(
    'RETIROS_APPS_CONNECTION_USER', cast=Secret
)
RETIROS_APPS_CONNECTION_PASSWORD = _config(
    'RETIROS_APPS_CONNECTION_PASSWORD', cast=Secret
)
CONSULTED_USERS_TIMEOUT_MINUTES = _config(
    'CONSULTED_USERS_TIMEOUT_MINUTES', cast=int, default=10
)
CONSULT_USERS_MAX = _config('CONSULT_USERS_MAX', cast=int, default=20)
CONSOLIDATED_TIME = _config('CONSOLIDATED_TIME', cast=str, default='19:00')
CONSOLIDATED_INTERVAL = _config('CONSOLIDATED_INTERVAL', cast=int, default=30)
TASK_QUEUE_MANAGER_DEFAULT_TIMEOUT = _config(
    'TASK_QUEUE_MANAGER_DEFAULT_TIMEOUT', cast=float, default=25.0
)

# Active directory settings
LDAP_DOMAIN = _config('LDAP_DOMAIN', cast=str, default='')
LDAP_USER = _config('LDAP_USER', cast=Secret)
LDAP_PASSWORD = _config('LDAP_PASSWORD', cast=Secret)
LDAP_BASE_DN = _config('LDAP_BASE_DN', cast=str, default='')
LDAP_SERVER = _config('LDAP_SERVER', cast=str, default='')
LDAP_USE_SSL = _config('LDAP_USE_SSL', cast=bool, default=True)

# Salesforce settings
SALESFORCE_DOMAIN = _config('SALESFORCE_DOMAIN', cast=str, default='login')
SALESFORCE_USER = _config('SALESFORCE_USER', cast=Secret, default='')
SALESFORCE_PASSWORD = _config('SALESFORCE_PASSWORD', cast=Secret, default='')
SALESFORCE_SECURITY_TOKEN = _config(
    'SALESFORCE_SECURITY_TOKEN', cast=Secret, default=''
)
SALESFORCE_RETIROS_USER = _config(
    'SALESFORCE_RETIROS_USER', cast=Secret, default=''
)
SALESFORCE_RETIROS_PASSWORD = _config(
    'SALESFORCE_RETIROS_PASSWORD', cast=Secret, default=''
)
SALESFORCE_RETIROS_SECURITY_TOKEN = _config(
    'SALESFORCE_RETIROS_SECURITY_TOKEN', cast=Secret, default=''
)
SALESFORCE_CLIENT_ID = _config('SALESFORCE_CLIENT_ID', cast=Secret, default='')
SALESFORCE_CLIENT_SECRET = _config(
    'SALESFORCE_CLIENT_SECRET', cast=Secret, default=''
)

# Health Cloud settings
HEALTH_CLOUD_DOMAIN = _config('HEALTH_CLOUD_DOMAIN', cast=str, default='login')
HEALTH_CLOUD_USER = _config('HEALTH_CLOUD_USER', cast=Secret, default='')
HEALTH_CLOUD_PASSWORD = _config(
    'HEALTH_CLOUD_PASSWORD', cast=Secret, default=''
)
HEALTH_CLOUD_SECURITY_TOKEN = _config(
    'HEALTH_CLOUD_SECURITY_TOKEN', cast=Secret, default=''
)
HEALTH_CLOUD_RETIROS_USER = _config(
    'HEALTH_CLOUD_RETIROS_USER', cast=Secret, default=''
)
HEALTH_CLOUD_RETIROS_PASSWORD = _config(
    'HEALTH_CLOUD_RETIROS_PASSWORD', cast=Secret, default=''
)
HEALTH_CLOUD_RETIROS_SECURITY_TOKEN = _config(
    'HEALTH_CLOUD_RETIROS_SECURITY_TOKEN', cast=Secret, default=''
)
HEALTH_CLOUD_CLIENT_ID = _config(
    'HEALTH_CLOUD_CLIENT_ID', cast=Secret, default=''
)
HEALTH_CLOUD_CLIENT_SECRET = _config(
    'HEALTH_CLOUD_CLIENT_SECRET', cast=Secret, default=''
)

# SEUS settings
SEUS_USER = _config('SEUS_USER', cast=Secret, default='')
SEUS_PASSWORD = _config('SEUS_PASSWORD', cast=Secret, default='')
SEUS_RETIROS_USER = _config('SEUS_RETIROS_USER', cast=Secret, default='')
SEUS_RETIROS_PASSWORD = _config(
    'SEUS_RETIROS_PASSWORD', cast=Secret, default=''
)

# Confluence settings
CONFLUENCE_ORG_ID = _config('CONFLUENCE_ORG_ID', cast=str, default='')
CONFLUENCE_API_TOKEN = _config('CONFLUENCE_API_TOKEN', cast=Secret, default='')

# Porfin settings
PORFIN_USER = _config('PORFIN_USER', cast=Secret, default='')
PORFIN_PASSWORD = _config('PORFIN_PASSWORD', cast=Secret, default='')
PORFIN_RETIROS_USER = _config('PORFIN_RETIROS_USER', cast=Secret, default='')
PORFIN_RETIROS_PASSWORD = _config(
    'PORFIN_RETIROS_PASSWORD', cast=Secret, default=''
)

# Viafirma settings
VIAFIRMA_USER = _config('VIAFIRMA_USER', cast=Secret, default='')
VIAFIRMA_PASSWORD = _config('VIAFIRMA_PASSWORD', cast=Secret, default='')
VIAFIRMA_RETIROS_USER = _config(
    'VIAFIRMA_RETIROS_USER', cast=Secret, default=''
)
VIAFIRMA_RETIROS_PASSWORD = _config(
    'VIAFIRMA_RETIROS_PASSWORD', cast=Secret, default=''
)

# Eventos Adversos settings
EVENTOS_ADVERSOS_USER = _config(
    'EVENTOS_ADVERSOS_USER', cast=Secret, default=''
)
EVENTOS_ADVERSOS_PASSWORD = _config(
    'EVENTOS_ADVERSOS_PASSWORD', cast=Secret, default=''
)
EVENTOS_ADVERSOS_RETIROS_USER = _config(
    'EVENTOS_ADVERSOS_RETIROS_USER', cast=Secret, default=''
)
EVENTOS_ADVERSOS_RETIROS_PASSWORD = _config(
    'EVENTOS_ADVERSOS_RETIROS_PASSWORD', cast=Secret, default=''
)

# Case Tracking settings
CASE_TRACKING_USER = _config('CASE_TRACKING_USER', cast=Secret, default='')
CASE_TRACKING_PASSWORD = _config(
    'CASE_TRACKING_PASSWORD', cast=Secret, default=''
)
CASE_TRACKING_RETIROS_USER = _config(
    'CASE_TRACKING_RETIROS_USER', cast=Secret, default=''
)
CASE_TRACKING_RETIROS_PASSWORD = _config(
    'CASE_TRACKING_RETIROS_PASSWORD', cast=Secret, default=''
)

# OIPA settings
OIPA_USER = _config('OIPA_USER', cast=Secret, default='')
OIPA_PASSWORD = _config('OIPA_PASSWORD', cast=Secret, default='')
OIPA_ENV = _config('OIPA_ENV', cast=str, default='ID_PDN_01')

# IDM settings
IDM_USER = _config('IDM_USER', cast=Secret, default='')
IDM_PASSWORD = _config('IDM_PASSWORD', cast=Secret, default='')

# MSAL settings
MSAL_CLIENT_ID = _config('MSAL_CLIENT_ID', cast=Secret, default='')
MSAL_CLIENT_SECRET = _config('MSAL_CLIENT_SECRET', cast=Secret, default='')
MSAL_TENANT_ID = _config('MSAL_TENANT_ID', cast=Secret, default='')

# OHI settings
OHI_CLIENT_ID = _config('OHI_CLIENT_ID', cast=Secret, default='')
OHI_CLIENT_SECRET = _config('OHI_CLIENT_SECRET', cast=Secret, default='')

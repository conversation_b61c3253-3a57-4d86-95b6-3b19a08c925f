from requests import Session

from lib.http.sessions import TLSSession
from lib.base_injectable import BaseInjectable


class BaseModule(BaseInjectable):
    """Base class for the creation of automation modules."""

    session: Session

    def __str__(self) -> str:
        """Returns the class name of this instance."""

        return self.__class__.__name__

    def create_session(self) -> None:
        """Creates a new `TLSSession` instance."""

        self.session = TLSSession()

    def close_session(self) -> None:
        """Closes the session if exists."""

        if hasattr(self, 'session'):
            self.session.close()

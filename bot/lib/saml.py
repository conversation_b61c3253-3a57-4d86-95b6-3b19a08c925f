from typing import cast
from urllib.parse import urljoin

from bs4 import Tag
from requests import Response, Session

from lib import config
from lib.base_injectable import BaseInjectable
from lib.exceptions import AutomationError
from lib.http.sessions import TLSSession
from lib.interfaces import FormData
from lib.tools import get_form_data, get_html_soup

LOGIN_URL = 'https://seus.sura.com/idp/login'


class SAML(BaseInjectable):
    """Provides the login function to authenticate
    in SURA applications that implements SAML authentication.
    """

    __login_response: Response | None
    """Response of the `SAMLResponse` service
    (last login response).
    """

    def __init__(self) -> None:
        self.__login_response = None

    def get_saml_request(self, login_url: str) -> FormData:
        """Get the SAML request necessary to
        get the login form.

        Parameters
        ----------
        login_url : str
            Application login URL.

        Returns
        -------
        FormData
            SAML request form.

        """
        response = self.session.get(login_url)
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup, error_message='No se pudo obtener el SAMLRequest.'
        )
        form_data.action = urljoin(response.url, form_data.action)
        return form_data

    def get_login_form(self, saml_request_form: FormData) -> FormData:
        """Get the login form metadata.

        Parameters
        ----------
        saml_request_form : FormData
            SAML request form data returned by
            the `get_saml_request` function.

        Returns
        -------
        FormData
            Login form data.

        Raises
        ------
        AutomationError
            Login metadata could not be obtained.

        """
        response = self.session.request(
            method=saml_request_form.method,
            url=saml_request_form.action,
            data=saml_request_form.data,
        )
        if not response.ok:
            raise AutomationError(
                'Error al obtener los metadatos del login.',
                detail=response.text,
            )
        soup = get_html_soup(response.text)
        return get_form_data(
            soup, error_message='No se pudo obtener los metadatos del login.'
        )

    def set_login_data_credentials(
        self,
        login_form: FormData,
        username: str | None = None,
        password: str | None = None,
    ) -> None:
        """Set the connection credentials and the
        country and tag fields.

        Parameters
        ----------
        login_form : FormData
            Login form returned by the
            `get_login_form` function.
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        """
        login_form.data['username'] = username or str(
            config.APPS_CONNECTION_USER
        )
        login_form.data['password'] = password or str(
            config.APPS_CONNECTION_PASSWORD
        )
        login_form.data.pop('countries', None)
        login_form.data['country'] = 'CO'
        login_form.data.pop('tags', None)
        login_form.data['tag'] = 'EMPLEADOS'

    def login(
        self,
        login_form: FormData,
        username: str | None = None,
        password: str | None = None,
    ) -> FormData:
        """Send the login request to get the SSO form.

        Parameters
        ----------
        login_form : FormData
            Login form returned by the
            `get_login_form` function.
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        Returns
        -------
        FormData
            SSo form data.

        Raises
        ------
        AutomationError
            If SAML response could not be obtained.

        """
        self.set_login_data_credentials(login_form, username, password)
        response = self.session.post(url=LOGIN_URL, data=login_form.data)
        if not response.ok:
            raise AutomationError(
                'Error al obtener el SAMLResponse.', detail=response.text
            )
        soup = get_html_soup(response.text)
        form_data = get_form_data(
            soup, error_message='No se pudo obtener el SAMLResponse.'
        )
        form_data.action = urljoin(response.url, form_data.action)
        return form_data

    def send_saml_response(self, saml_response: FormData) -> None:
        """Send the SAML response necessary to perform the SSO.

        Parameters
        ----------
        saml_response : FormData
            SAML response returned by the `login` function.

        Raises
        ------
        AutomationError
            If SAML response could not be sent.

        """
        response = self.session.request(
            method=saml_response.method,
            url=saml_response.action,
            data=saml_response.data,
        )
        if not response.ok:
            raise AutomationError(
                'Error al enviar el SAMLResponse.', detail=response.text
            )
        self.__login_response = response

    def fetch_app_page(self) -> Response:
        """Fetch the app page.

        The login must have been performed before
        calling this function.

        Returns
        -------
        Response
            App page response.

        Raises
        ------
        AutomationError
            If login response is not an ACS response.
        AutomationError
            If aCS register form was not found.
        AutomationError
            If next site url field was not found.
        AutomationError
            If next site url field is empty.

        """
        acs_response = self.get_login_response()
        errors_detail = (
            f'URL: {acs_response.url}; CONTENT: {acs_response.text}'
        )
        if 'acs' not in acs_response.url:
            raise AutomationError(
                'La respuesta no es de tipo ACS.', detail=errors_detail
            )

        soup = get_html_soup(acs_response.text)
        phone_register_container_element = cast(
            Tag | None, soup.find(id='registerPage')
        )
        if phone_register_container_element is None:
            raise AutomationError(
                'No se encontró el formulario de registro ACS.',
                detail=errors_detail,
            )

        go_to_element = cast(
            Tag | None, phone_register_container_element.find(id='goTo')
        )
        if go_to_element is None:
            raise AutomationError(
                'No se encontró el campo de la URL del sitio.',
                detail=errors_detail,
            )

        next_site = go_to_element.get('value')
        if next_site is None:
            raise AutomationError(
                'El campo de la URL del sitio está vacío.',
                detail=errors_detail,
            )

        return self.session.get(url=str(next_site))

    def authenticate(
        self,
        app_url: str,
        session: Session | None = None,
        username: str | None = None,
        password: str | None = None,
    ) -> Session:
        """Perform the SAML authentication on the
        passed application login URL.

        Parameters
        ----------
        app_url : str
            Application login URL.
        session : Session | None, optional
            Session, by default None.
        username : str | None, optional
            Username, by default None.
        password : str | None, optional
            Password, by default None.

        Returns
        -------
        Session
            Use this session to send the next requests with
            the current SAML session cookie.

        """
        if session:
            self.session = session
        else:
            self.session = TLSSession()

        saml_request_form = self.get_saml_request(app_url)
        login_form = self.get_login_form(saml_request_form)
        saml_response = self.login(login_form, username, password)
        self.send_saml_response(saml_response)
        return self.session

    def get_login_response(self) -> Response:
        """Return the response of the `SAMLResponse`
        service (last login response).

        Raises
        ------
        AutomationError
            If authentication was not successful.

        """
        if not self.__login_response:
            raise AutomationError(
                'La autenticación no ha tenido éxito o no se ha realizado.'
            )
        return self.__login_response

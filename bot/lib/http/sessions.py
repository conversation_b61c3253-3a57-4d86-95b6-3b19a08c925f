import requests
from urllib3.util.retry import Retry

from lib.http.adapters import TLSAdapter
from lib.http.cookie_policies import AllowsEmptyDomainsPolicy


class TLSSession(requests.Session):
    """A session that accepts unsafe and insecure connections
    and allows empty domain cookies.
    """

    def __init__(self, timeout=(7, 60)) -> None:
        """Create a TLS session.

        It implements the AllowsEmptyDomainsPolicy
        policy to allow empty domain cookies.

        Parameters
        ----------
        timeout : int | tuple[int, int] | None, optional
            Requests timeout in seconds, by default (7, 60).

        """
        super().__init__()
        self.verify = False
        self.timeout = timeout
        max_retries = Retry(
            total=3,
            backoff_factor=1.5,
            allowed_methods=[
                'POST',
                'GET',
                'HEAD',
                'OPTIONS',
                'PUT',
                'PATCH',
                'DELETE',
            ],
        )
        self.mount('https://', TLSAdapter(max_retries=max_retries))
        self.cookies.set_policy(AllowsEmptyDomainsPolicy())

    def request(
        self, method: str | bytes, url: str | bytes, **kwargs
    ) -> requests.Response:
        kwargs.setdefault('timeout', self.timeout)
        return super().request(method, url, **kwargs)

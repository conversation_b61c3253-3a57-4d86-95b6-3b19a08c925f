import ssl

from requests.adapters import HTTPAdapter


class TLSAdapter(HTTPAdapter):
    """HTTP adapter to accept unsafe and insecure connections."""

    def init_poolmanager(self, *args, **kwargs):
        ctx = ssl.create_default_context()

        # Hostname verification is disabled in the SSL/TLS context.
        # This allows connections to servers that do not exactly match the hostname in the SSL certificate.
        ctx.check_hostname = False

        # Set the security level to the lowest
        ctx.set_ciphers("DEFAULT@SECLEVEL=1")

        # 0x4 = OP_NO_COMPRESSION
        # This is done for security reasons, as SSL/TLS compression can be vulnerable to compression attacks.
        ctx.options |= 0x4

        # Rewrite ssl context
        kwargs["ssl_context"] = ctx
        return super(TLSAdapter, self).init_poolmanager(*args, **kwargs)

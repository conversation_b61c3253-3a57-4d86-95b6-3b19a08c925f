from dataclasses import dataclass
from typing import Any

import httpx
import orjson

from dtos.removed_user_dto import RemovedUserResponseDto
from lib.base_injectable import BaseInjectable

BASE_URL = 'https://httpbin.org/post'  # TODO: Replace by IDM url
HEADERS = {'Content-Type': 'application/json'}


@dataclass
class IDMResponse:
    content: Any
    is_success: bool


class IDMClient(BaseInjectable):
    async def send_removed_user_result(
        self, removed_user: RemovedUserResponseDto
    ) -> IDMResponse:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                BASE_URL,
                content=removed_user.to_json(),
                headers=HEADERS,
            )
            try:
                content = orjson.loads(response.text)
            except orjson.JSONDecodeError:
                content = response.text
            return IDMResponse(
                content=content,
                is_success=response.is_success,
            )

from datetime import datetime, timezone
from typing import Any, Dict, <PERSON>ric, <PERSON>Var, Union, cast

from beanie import (
    Document,
    Insert,
    Link,
    PydanticObjectId,
    Replace,
    Update,
    before_event,
)
from beanie.odm.fields import ExpressionField

T = TypeVar('T')


class BaseModel(Document, Generic[T]):
    """Beanie model representing a Mongo collection."""

    isDeleted: bool = False
    isActive: bool = True
    createdAt: datetime = datetime.now()
    updatedAt: datetime = datetime.now()
    createdAtUTC: datetime = datetime.now(timezone.utc)
    updatedAtUTC: datetime = datetime.now(timezone.utc)

    @before_event(Insert)
    def create_dates(self):
        """Create the "createdAt" and "updatedAt" fields."""
        self.createdAt = datetime.now()
        self.updatedAt = datetime.now()
        self.createdAtUTC = datetime.now(timezone.utc)
        self.updatedAtUTC = datetime.now(timezone.utc)

    @before_event(Update, Replace)
    def update_dates(self):
        """Update the "updatedAt" field."""
        self.updatedAt = datetime.now()
        self.updatedAtUTC = datetime.now(timezone.utc)

    def set(self, expression: Dict[Union[ExpressionField, str], Any], **kwargs):
        expression['updatedAt'] = datetime.now()
        expression['updatedAtUTC'] = datetime.now(timezone.utc)
        return super().set(expression, **kwargs)

    @classmethod
    async def get_link(cls, id: str | PydanticObjectId) -> Link[T] | None:
        """Return a `Link` object of the document."""
        if isinstance(id, str):
            id = PydanticObjectId(id)
        return cast(Link[T] | None, await cls.get(id))

    class ProjectionConfig:
        """Projects fields.

        Example:
            Projecting the "user" field of a post
            to return the username of the post's user.
            ::

            class User(BaseModel):
                username: str
                ...

            class Post(BaseModel):
                text: str
                user: Link[User]
                ...

                class ProjectionConfig:
                    user = 'user.username' # Project the user's "username" field in the "user" field

        After the projection, the type of the field will be
        the type of the projected value. As the above example,
        the type of the "user" field is Link[User] before projection
        and it is casted to srt which is the type of the "username" field.
        """

        pass

    class TranslationConfig:
        """Translate the name of the fields
        when exporting to XLSX and the values
        of a field using the I18N translator module.

        Example:
            Translating the name of the fields and
            the values of the "state" field of a post.
            ::

            class Post(BaseModel):
                text: str
                user: Link[User]
                state: Literal['published', 'removed', 'hidden']
                ...

                class TranslationConfig:
                    columns = {
                        'text': '<i18n_translation_key>',
                        'user': '<i18n_translation_key>',
                        'state': '<i18n_translation_key>'
                    }
                    data = {
                        'state': {
                            'published': '<i18n_translation_key>',
                            'removed': '<i18n_translation_key>',
                            'hidden': '<i18n_translation_key>'
                        }
                    }

        The "columns" dict takes the model's fields and translate
        the name of each field replacing it by a I18N text.

        The "data" dict takes the model's fields and translate
        the posible values of each field replacing it by a I18N text.
        """

        pass

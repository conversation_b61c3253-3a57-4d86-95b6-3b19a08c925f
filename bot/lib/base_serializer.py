import typing

from pydantic import BaseModel, ConfigDict

T = typing.TypeVar('T', bound='BaseSerializer')


class BaseSerializer(BaseModel):
    """Extension of the Pydantic BaseModel class
    to convert Beanie documents to DTOs and
    DTOs to dicts.
    """

    model_config = ConfigDict(from_attributes=True)

    def to_dict(self) -> dict[str, typing.Any]:
        """Serialize DTO to dict.

        Returns
        -------
        dict[str, typing.Any]
            Serialized DTO.

        """
        return self.model_dump()

    @classmethod
    def from_orm(cls: typing.Type[T], obj: typing.Any) -> T:
        """Convert a model object to a DTO
        in order to be serialized.

        Parameters
        ----------
        cls : typing.Type[T]
            DTO class reference.
        obj : typing.Any
            Model object.

        Returns
        -------
        T
            DTO.

        """
        return super().model_validate(obj)

    @classmethod
    def from_orm_many(cls: typing.Type[T], objs: list[typing.Any]) -> list[T]:
        """Convert a list of model objects to DTOs
        in order to be serialized.

        Parameters
        ----------
        cls : typing.Type[T]
            DTO class reference.
        objs : list[typing.Any]
            List of model objects.

        Returns
        -------
        list[T]
            List of DTOs.

        """
        return_data: list[T] = []
        for obj in objs:
            return_data.append(cls.model_validate(obj))
        return return_data

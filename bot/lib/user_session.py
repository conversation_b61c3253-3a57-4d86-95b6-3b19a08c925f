import base64

from starlette.authentication import (
    AuthCredentials,
    AuthenticationBackend,
    AuthenticationError,
)
from starlette.requests import HTTPConnection

from lib import config
from i18n import I18<PERSON>
from lib.active_directory import <PERSON><PERSON><PERSON>, ActiveDirectory
from lib.responses import ErrorResponse
from lib.seus import SEUS, AuthenticatedUserData
from lib.tools import split_authorization_header
from lib.validator.error_validator import ErrorValidator

DEFAULT_REPOSITORY = 'AD Empleados'


class UserSession:
    """User session data."""

    session_token: str = ''
    username: str = ''

    dni: str | None = None
    email: str | None = None
    fullname: str | None = None
    repository: str = DEFAULT_REPOSITORY
    roles: list[str] = []

    error_message: str | None = None

    def __init__(self) -> None:
        self._ad = ActiveDirectory()
        self._seus = SEUS(config.SERVICE)

    @property
    def is_authenticated(self) -> bool:
        if self.error_message:
            return False
        if not hasattr(self, 'session_token') or not hasattr(self, 'username'):
            return False
        return True if self.session_token else False

    def has_role(self, role: str) -> bool:
        return role in self.roles or not config.USE_AUTHORIZATION

    async def login_with_ad(self, username: str, cred: str) -> None:
        ad_user = await self._ad.async_login(username, cred)
        self._fill_from_ad_user(ad_user)
        self.roles = await self._seus.get_authorization_roles(
            username, self.repository
        )

    async def login_with_seus(self, session_token: str) -> None:
        user_data = await self._seus.get_authenticated_user_data(session_token)
        if user_data:
            self._fill_from_authenticated_data(user_data)
        else:
            self.error_message = (
                'La sesión ha expirado, por favor inicie sesión nuevamente.'
            )

    def _fill_from_authenticated_data(
        self, auth_data: AuthenticatedUserData
    ) -> None:
        self.session_token = auth_data.session_token
        self.username = auth_data.username
        self.dni = auth_data.dni
        self.email = auth_data.email
        self.fullname = auth_data.fullname
        self.repository = auth_data.repository
        self.roles = auth_data.roles

    def _fill_from_ad_user(self, ad_user: ADUser) -> None:
        self.session_token = ad_user.create_jwt()
        self.username = ad_user.sam_account_name
        self.dni = ad_user.document
        self.email = ad_user.user_principal_name
        self.fullname = ad_user.fullname


class UserSessionBackend(AuthenticationBackend):
    """Authentication middleware backend"""

    __i18n: I18N

    def __init__(self, *args, **kwargs) -> None:
        self.__i18n = I18N()
        super().__init__(*args, **kwargs)

    async def authenticate(
        self, conn: HTTPConnection
    ) -> tuple[AuthCredentials, UserSession] | None:
        """Validate the session token.

        Parameters
        ----------
        conn : HTTPConnection
            Connection instance.

        Returns
        -------
        tuple[AuthCredentials, UserSession] | None
            If the authentication was correct,
            returns a two-elements tuple containing
            the authentication credentials and User session object.

        Raises
        ------
        AuthenticationError
            If an unknown error has occurred.

        """
        user = UserSession()
        self.__i18n.language = conn.headers.get('Accept-Language')

        try:
            auth = conn.headers.get('Authorization')
            if auth:
                scheme, credentials = split_authorization_header(auth)
                if scheme == 'bearer':
                    await user.login_with_seus(credentials)
                elif scheme == 'basic':
                    decoded = base64.b64decode(credentials).decode('ascii')
                    username, _, cred = decoded.partition(':')
                    await user.login_with_ad(username, cred)
                else:
                    raise ValueError(f'Esquema no soportado: {scheme}')
            else:
                session_token = conn.cookies.get('appTag')
                if session_token:
                    await user.login_with_seus(session_token)

        except KeyError:
            user.error_message = self.__i18n('invalid_token')

        except Exception as e:
            user.error_message = self.__i18n(
                'an_error_has_occurred', format_values={'error': str(e)}
            )

        return (AuthCredentials(), user)

    @staticmethod
    def on_auth_error(
        _: HTTPConnection, exc: AuthenticationError
    ) -> ErrorResponse:
        """Called when an exception is raised during
        the authentication process to return an
        401 error response.

        Parameters
        ----------
        _ : HTTPConnection
            Connection object.
        exc : AuthenticationError
            Auth error details.

        Returns
        -------
        ErrorResponse
            401 error response.

        """
        error_validator = ErrorValidator()
        error_validator.add('common', str(exc))
        return ErrorResponse(error_validator, status_code=401)

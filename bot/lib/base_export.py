from dataclasses import dataclass

from lib.base_injectable import BaseInjectable


@dataclass
class File:
    """File descriptor class."""

    stream: bytes
    filename: str
    media_type: str
    headers: dict[str, str]
    path: str | None = None


class BaseExport(BaseInjectable):
    """Base class for export modules."""

    _filename: str
    _extension: str

    def _get_full_filename(self) -> str:
        """Gets the full filename of the file.
        Concatenates filename and extension.

        Returns
        -------
        str
            Full filename.
        """

        return f'{self._filename}.{self._extension}'

    def _get_headers(self, full_filename: bool = False) -> dict[str, str]:
        """Gets the headers required for the clients
        to download the file.

        Parameters
        ----------
        full_filename : bool, optional
            Full filename, by default False.

        Returns
        -------
        dict[str, str]
            Headers.
        """

        return {
            'Content-Disposition': f'attachment;filename={self._get_full_filename() if full_filename else self._filename}',
            'Access-Control-Expose-Headers': 'Content-Disposition'
        }

    def _get_media_type_from_extension(self) -> str:
        """Gets the media type of the file
        based on the extension.

        Returns
        -------
        str
            Media type.
        """

        if self._extension == 'xlsx':
            return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif self._extension == 'json':
            return 'application/json'
        elif self._extension == 'pdf':
            return 'application/pdf'
        else:
            return 'text/csv'

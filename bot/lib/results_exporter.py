import html
import time
import math

from typing import Any, Literal
from collections.abc import Generator
from datetime import datetime, timedelta
from collections import OrderedDict
from reportlab.platypus import Spacer
from reportlab.lib.enums import TA_LEFT

from i18n import I18N
from dtos.consulted_user_dto import ConsultedUserResponseDto
from dtos.removed_user_dto import RemovedUserResponseDto
from models import ConsultedUser, RemovedUser, Transaction, DailyConsolidated
from lib.base_injectable import BaseInjectable
from lib import config
from lib.pdf import PDF
from lib.pdf.interfaces import FrontPage, TOC, TableColumn, TableProperties


TITLE = 'Resultados - Bot Consulta y Retiro'
AUTHOR = 'ARUS S.A.'
DATES_FORMAT = r'%d/%m/%Y %I:%M:%S %p'
EXCLUDED_APPLICATIONS = ['activeDirectory']

ResultType = (
    ConsultedUser
    | ConsultedUserResponseDto
    | RemovedUser
    | RemovedUserResponseDto
)
ResultsType = (
    list[ConsultedUser]
    | list[ConsultedUserResponseDto]
    | list[RemovedUser]
    | list[RemovedUserResponseDto]
)


class ResultsExporter(BaseInjectable):
    """Exports `ConsultedUser`, `RemovedUser`,
    `ConsultedUserResponseDto` and `RemovedUserResponseDto`
    instances or list of instances to a PDF file.
    """

    def __init__(self, pdf: PDF, i18n: I18N) -> None:
        self.pdf = pdf
        self.i18n = i18n

    def __build(self) -> bytes | None:
        return self.pdf.build(
            title=TITLE,
            author=AUTHOR,
            main_logo_path=str(config.PDF_MAIN_LOGO_IMG),
            secondary_logo_path=str(config.PDF_SECONDARY_LOGO_IMG),
        )

    def __get_query_time_message(self, query_time: float) -> str:
        """Get the query time message.

        Parameters
        ----------
        query_time : float
            Query time in seconds.

        Returns
        -------
        str
            Query time message.

        """
        if query_time > 3600:
            hours = time.strftime('%H:%M:%S', time.gmtime(query_time))
            results_time = f'{hours} horas'
        elif query_time > 60:
            minutes = time.strftime('%M:%S', time.gmtime(query_time))
            results_time = f'{minutes} minutos'
        else:
            results_time = f'{math.ceil(query_time)} segundos'
        return results_time

    def __get_from_iso_date(self, date: datetime | str) -> datetime:
        """Get date from an ISO date string.

        Parameters
        ----------
        date : datetime | str
            ISO date.

        Returns
        -------
        datetime
            Date.

        """
        if isinstance(date, str):
            date = datetime.fromisoformat(date)
        return date

    def __add_title1(self, text: str) -> None:
        """Add a 1-level title (similar to HTML <h1> tag).

        Parameters
        ----------
        text : str
            Title text.

        """
        self.pdf.add_title1(
            text=text, text_color=self.pdf.get_color_from_hex('#0033A0')
        )

    def __add_consolidated_front_page(
        self, consolidated: DailyConsolidated
    ) -> None:
        """Add the front page for the consolidated report.

        Parameters
        ----------
        consolidated : DailyConsolidated
            Consolidated report.

        """
        front_page_summary = FrontPage(
            title='Bot de Consulta y Retiro de Usuarios',
            subtitle='Consolidado de usuarios retirados',
            date=datetime.fromtimestamp(consolidated.startTime),
            date_label='Fecha y hora de consolidado',
            main_logo_path=str(config.PDF_MAIN_LOGO_IMG),
            secondary_logo_path=str(config.PDF_SECONDARY_LOGO_IMG),
        )
        self.pdf.add_front_page(front_page_summary)

    def __add_results_front_page(
        self, action: Literal['consult', 'remove', 'all']
    ) -> None:
        """Add the front page.

        Parameters
        ----------
        action : Literal['consult', 'remove', 'all']
            Performed action.

        """
        if action == 'all':
            ACTION_NAME = 'Consulta y Retiro'
        else:
            ACTION_NAME = 'Consulta' if action == 'consult' else 'Retiro'
        front_page_summary = FrontPage(
            title='Bot de Consulta y Retiro de Usuarios',
            subtitle=f'Resultados de {ACTION_NAME}',
            date=datetime.now(),
            date_label='Fecha y hora del reporte',
            main_logo_path=str(config.PDF_MAIN_LOGO_IMG),
            secondary_logo_path=str(config.PDF_SECONDARY_LOGO_IMG),
        )
        self.pdf.add_front_page(front_page_summary)

    def __add_summary(
        self,
        result: ResultType,
        action: Literal['consult', 'remove'],
        apps_count: int,
    ) -> None:
        """Add the consulted/removed user summary section.

        Parameters
        ----------
        result : ResultType
            The data of the consulted/removed user.
        action : Literal['consult', 'remove']
            Performed action.
        apps_count : int
            Number of applications.

        """
        self.pdf.add_title2(
            text=result.username,
            text_color=self.pdf.get_color_from_hex('#0033A0'),
        )
        self.pdf.add_title3(
            text='Resumen', text_color=self.pdf.get_color_from_hex('#0033A0')
        )

        result.createdAt = self.__get_from_iso_date(result.createdAt)
        query_time = self.__get_query_time_message(result.time)
        start_date = result.createdAt.strftime(DATES_FORMAT)
        end_date = result.createdAt + timedelta(seconds=result.time)
        end_date = end_date.strftime(DATES_FORMAT)

        USER_STATE_NAME = 'consultado' if action == 'consult' else 'retirado'
        self.pdf.add_paragraph(
            f'<b>Usuario {USER_STATE_NAME}:</b> {result.username}',
            spacer=Spacer(0, 10),
        )
        self.pdf.add_paragraph(
            f'<b>Cantidad de aplicaciones:</b> {apps_count}',
            spacer=Spacer(0, 10),
        )
        self.pdf.add_paragraph(
            f'<b>Fecha y hora de inicio:</b> {start_date}',
            spacer=Spacer(0, 10),
        )
        self.pdf.add_paragraph(
            f'<b>Fecha y hora de fin:</b> {end_date}', spacer=Spacer(0, 10)
        )
        self.pdf.add_paragraph(
            f'<b>Tiempo total:</b> {query_time}', spacer=Spacer(0, 10)
        )
        catalog_number = getattr(result, 'catalogNumber', None)
        if catalog_number:
            self.pdf.add_paragraph(
                f'<b>Número de catálogo:</b> {catalog_number}',
                spacer=Spacer(0, 10),
            )
        self.pdf.add_page_break()

    def __add_consolidated_summary(
        self, username: str, apps_count: int
    ) -> None:
        """Add the consolidated summary section for a user.

        Parameters
        ----------
        username : str
            Username.
        apps_count : int
            Number of applications.

        """
        self.pdf.add_title2(
            text=username, text_color=self.pdf.get_color_from_hex('#0033A0')
        )
        self.pdf.add_title3(
            text='Resumen', text_color=self.pdf.get_color_from_hex('#0033A0')
        )

        self.pdf.add_paragraph(
            f'<b>Usuario retirado:</b> {username}', spacer=Spacer(0, 10)
        )
        self.pdf.add_paragraph(
            f'<b>Cantidad de aplicaciones:</b> {apps_count}',
            spacer=Spacer(0, 10),
        )

    def __add_application_data(self, application_data: dict[str, Any]) -> None:
        """Add the tables that represents the application data.

        Parameters
        ----------
        application_data : dict[str, Any]
            Application data.

        """
        simple_fields = OrderedDict()
        list_fields = OrderedDict()
        for key, value in application_data.items():
            if key == 'warning':
                continue
            if value is None:
                value = 'N/A'
            if isinstance(value, list):
                list_fields.setdefault(key, value)
            else:
                simple_fields.setdefault(key, value)

        if simple_fields or list_fields:
            self.pdf.add_subtitle(
                'Datos', self.pdf.get_color_from_hex('#0033A0')
            )

        table_properties = TableProperties(
            header_background_color=self.pdf.get_color_from_hex('#cccdcc'),
            row_line_color=self.pdf.get_color_from_hex('#cccdcc'),
            row_count_background_color=self.pdf.get_color_from_hex('#e5e5e5'),
            show_row_count=True,
        )

        if simple_fields:
            self.pdf.add_table_from_dict(
                value=simple_fields,
                column_style=[
                    TableColumn(
                        width=150, font_size=10, align=TA_LEFT, bold=True
                    ),
                    TableColumn(width=300, font_size=10, align=TA_LEFT),
                ],
                table_properties=table_properties,
                translation=self.i18n.get_dictionary(),
            )
            self.pdf.add_spacer(0, 20)

        if list_fields:
            for key, value in list_fields.items():
                self.pdf.add_table_from_list(
                    value=value,
                    column_name_for_1d=key,
                    auto_width=True,
                    table_properties=table_properties,
                    translation=self.i18n.get_dictionary(),
                )

    def __add_application_time_info(self, application: dict[str, Any]) -> None:
        """Add the elements to show the start time, end time
        and time difference for each application.

        Parameters
        ----------
        application : dict[str, Any]
            Application result.

        """
        application_start_time = application.get('startTime')
        application_end_time = application.get('endTime')
        application_time_diff = application.get('timeDiff')
        if (
            application_start_time
            or application_end_time
            or application_time_diff
        ):
            self.pdf.add_subtitle(
                'Tiempo', self.pdf.get_color_from_hex('#0033A0')
            )
            if application_start_time:
                application_start_time = datetime.fromtimestamp(
                    application_start_time
                ).strftime(DATES_FORMAT)
                self.pdf.add_paragraph(
                    f'<b>Fecha y hora de inicio:</b> {application_start_time}',
                    spacer=Spacer(0, 10),
                )
            if application_end_time:
                application_end_time = datetime.fromtimestamp(
                    application_end_time
                ).strftime(DATES_FORMAT)
                self.pdf.add_paragraph(
                    f'<b>Fecha y hora de fin:</b> {application_end_time}',
                    spacer=Spacer(0, 10),
                )
            if application_time_diff:
                application_time_diff = self.__get_query_time_message(
                    round(application_time_diff, 2)
                )
                self.pdf.add_paragraph(
                    f'<b>Tiempo total:</b> {application_time_diff}',
                    spacer=Spacer(0, 10),
                )
            self.pdf.add_spacer(0, 10)

    def __add_result(
        self,
        result: ResultType,
        action: Literal['consult', 'remove'],
        has_privileges: bool,
        include_not_found: bool,
        include_errors: bool,
    ) -> bool:
        """Add the elements to represent the
        consulted/removed user results.

        Parameters
        ----------
        result : ResultType
            The data of the consulted/removed user.
        action : Literal['consult', 'remove']
            Performed action.
        has_privileges : bool
            Whether to include users with privileges only.
        include_not_found : bool
            Whether to include not found users.
        include_errors : bool
            Whether to include users with errors.

        Returns
        -------
        bool
            Whether the results were added.

        """
        if action == 'remove':
            has_privileges = False

        result_data = self.__filter_result_data(
            result.data, has_privileges, include_not_found, include_errors
        )
        applications = sorted(
            result_data, key=lambda d: d['applicationName'].lower()
        )
        if applications:
            self.__add_summary(result, action, len(applications))

        return self.__add_result_applications(applications, action)

    def __add_result_applications(
        self,
        applications: list[dict[str, Any]],
        action: Literal['consult', 'remove'],
    ) -> bool:
        """Adds the elements to represent the application results.

        Parameters
        ----------
        applications : list[dict[str, Any]]
            Application results.
        action : Literal['consult', 'remove']
            Performed action.

        Returns
        -------
        bool
            Whether the results were added.

        """
        action_name = 'consulta' if action == 'consult' else 'retiro'
        verb_name = 'consultar' if action == 'consult' else 'retirar'

        results_added = False
        for application in applications:
            self.pdf.add_title3(
                text=application['applicationName'],
                text_color=self.pdf.get_color_from_hex('#0033A0'),
            )

            application_data = application.get('data')
            application_error = application.get('error')
            application_user_not_found = application.get('userNotFound')
            self.__add_application_time_info(application)

            if application_data:
                self.__add_application_data(application_data)

            elif application_error:
                if application_user_not_found:
                    title = 'Usuario no encontrado'
                else:
                    title = 'Ha ocurrido un error'
                self.pdf.add_subtitle(
                    title, self.pdf.get_color_from_hex('#A72222')
                )
                if not application_error.endswith('.'):
                    application_error += '.'
                try:
                    self.pdf.add_paragraph(
                        html.escape(application_error, quote=False)
                    )
                except Exception:
                    self.pdf.add_paragraph(
                        f'No se pudo {verb_name} el usuario en esta aplicación.'
                    )

            else:
                self.pdf.add_subtitle(
                    'No se retornó ningún dato',
                    self.pdf.get_color_from_hex('#A72222'),
                )
                self.pdf.add_paragraph(
                    f'La operación de {action_name} del usuario fue exitosa.'
                    f' Sin embargo, la aplicación no retornó ningún dato.'
                )

            self.pdf.add_page_break()
            results_added = True

        return results_added

    def __filter_result_data(
        self,
        data: list[dict[str, Any]],
        has_privileges: bool,
        include_not_found: bool,
        include_errors: bool,
    ) -> Generator[dict[str, Any], Any, None]:
        """Filter the result data.

        Parameters
        ----------
        data : list[dict[str, Any]]
            Result data.
        has_privileges : bool
            Whether to include users with privileges only.
        include_not_found : bool
            Whether to include not found users.
        include_errors : bool
            Whether to include users with errors.

        Yields
        ------
        Generator[dict[str, Any], Any, None]
            Filtered result data.

        """
        for application in data:
            if application['application'] in EXCLUDED_APPLICATIONS:
                continue
            if application.get('ignored', False):
                continue
            if has_privileges and not application.get('hasPrivileges', False):
                continue
            if not include_not_found and application.get(
                'userNotFound', False
            ):
                continue
            if (
                not include_errors
                and not application.get('userNotFound', False)
                and application.get('error')
            ):
                continue
            yield application

    def __add_toc(self) -> None:
        """Add table of contents."""
        self.pdf.add_table_of_contents(
            TOC(text_color=self.pdf.get_color_from_hex('#0033A0'))
        )

    def __add_transaction_results(
        self,
        consulted: ResultsType,
        removed: ResultsType,
        has_privileges: bool,
        include_not_found: bool,
        include_errors: bool,
    ) -> bool:
        """Add the elements to represent the consulted/removed
        users results of a transaction.

        Parameters
        ----------
        consulted : ResultsType
            Consulted users results.
        removed : ResultsType
            Removed users results.
        has_privileges : bool
            Whether to include users with privileges only.
        include_not_found : bool
            Whether to include not found users.
        include_errors : bool
            Whether to include users with errors.

        Returns
        -------
        bool
            Whether results were added.

        """
        results_added = False

        if consulted:
            self.__add_title1('Resultados de Consulta')
            for consulted_user in consulted:
                results_added = self.__add_result(
                    consulted_user,
                    'consult',
                    has_privileges,
                    include_not_found,
                    include_errors,
                )
                self.__add_toc()

        if removed:
            self.__add_title1('Resultados de Retiro')
            for removed_user in removed:
                results_added = self.__add_result(
                    removed_user,
                    'remove',
                    False,
                    include_not_found,
                    include_errors,
                )
                self.__add_toc()

        return results_added

    def to_pdf(
        self,
        result_or_results: ResultType | ResultsType,
        action: Literal['consult', 'remove'],
        has_privileges: bool,
        include_not_found: bool,
        include_errors: bool,
    ) -> bytes | None:
        """Export a consulted/removed user to PDF.

        Parameters
        ----------
        result_or_results : ResultType | ResultsType
            The data of the consulted/removed users.
        action : Literal['consult', 'remove']
            Performed action.
        has_privileges : bool
            Whether to include users with privileges only.
        include_not_found : bool
            Whether to include not found users.
        include_errors : bool
            Whether to include users with errors.

        Returns
        -------
        bytes | None
            PDF file content.

        """
        if not isinstance(result_or_results, list):
            results = [result_or_results]
        else:
            results = result_or_results

        self.__add_results_front_page(action)
        ACTION_NAME = 'Consulta' if action == 'consult' else 'Retiro'
        self.__add_title1(f'Resultados de {ACTION_NAME}')

        results_added = False
        for result in results:
            results_added = self.__add_result(
                result=result,
                action=action,
                has_privileges=has_privileges,
                include_not_found=include_not_found,
                include_errors=include_errors,
            )
            self.__add_toc()

        if not results_added:
            return None

        return self.__build()

    def transactions_to_pdf(
        self,
        transactions: list[Transaction],
        action: Literal['consult', 'remove', 'all'],
        has_privileges: bool,
        include_not_found: bool,
        include_errors: bool,
    ) -> bytes | None:
        """Export a consulted/removed user to PDF.

        Parameters
        ----------
        transactions : list[Transaction]
            List of transactions.
        action : Literal['consult', 'remove', 'all']
            Performed action.
        has_privileges : bool
            Whether the user has privilege.
        include_not_found : bool
            Whether to include not found users.
        include_errors : bool
            Whether to include users with errors.

        Returns
        -------
        bytes | None
            PDF file content.

        """
        consulted = []
        removed = []
        for transaction in transactions:
            if action != 'all' and transaction.action != action:
                continue
            if action == 'all':
                consulted.extend(transaction.consultedUsers)
                removed.extend(transaction.removedUsers)
            elif action == 'consult':
                consulted.extend(transaction.consultedUsers)
            else:
                removed.extend(transaction.removedUsers)

        self.__add_results_front_page(action)
        results_added = self.__add_transaction_results(
            consulted=consulted,
            removed=removed,
            has_privileges=has_privileges,
            include_not_found=include_not_found,
            include_errors=include_errors,
        )

        if not results_added:
            return None

        return self.__build()

    def consolidated_to_pdf(
        self, consolidated: DailyConsolidated, include_errors: bool = False
    ) -> bytes | None:
        """Export a consolidated report to PDF.

        Parameters
        ----------
        consolidated : DailyConsolidated
            Consolidated report.
        include_errors : bool, optional
            Whether to include users with errors, by default False.

        Returns
        -------
        bytes | None
            PDF file content.

        """
        self.__add_consolidated_front_page(consolidated)
        self.__add_title1('Reporte de usuarios retirados')

        results_added = False
        for username, results in consolidated.data.items():
            result_data = self.__filter_result_data(
                results,
                has_privileges=False,
                include_not_found=True,
                include_errors=include_errors,
            )
            applications = sorted(
                result_data, key=lambda d: d['applicationName'].lower()
            )
            if applications:
                self.__add_consolidated_summary(username, len(applications))

            results_added = self.__add_result_applications(
                applications, 'remove'
            )
            self.__add_toc()

        if not results_added:
            return None

        return self.__build()

import math
import typing

from lib.base_dto import BaseResponseDto

P = typing.TypeVar('P', bound=BaseResponseDto, covariant=True)


class Pagination(typing.Generic[P]):
    """Creates an object to handle paginated data."""

    data: list[P]
    rows_per_page: int
    rows_number: int
    pages_number: int
    skip: int
    dto: type[P]

    def __init__(self, page: int, rows_per_page: int, rows_number: int) -> None:
        """Initializes the attributed of the pagination.

        Parameters
        ----------
        page : int
            Page number.
        rows_per_page : int
            Rows per page.
        rows_number : int
            Total rows number.
        """

        self.page = page
        self.rows_per_page = rows_per_page
        self.rows_number = rows_number

        self.pages_number = math.ceil(self.rows_number / self.rows_per_page) if rows_per_page else 1
        self.skip = self.rows_per_page * (self.page - 1)

    def to_response(self) -> dict[str, typing.Any]:
        """Returns the pagination data prepared to be serialized.

        Returns
        -------
        dict[str, typing.Any]
            Dict with pagination data.
        """

        return {
            'page': self.page,
            'rowsPerPage': self.rows_per_page,
            'rowsNumber': self.rows_number,
            'pagesNumber': self.pages_number,
            'data': [element.to_response() for element in self.data]
        }

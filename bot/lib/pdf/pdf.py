from copy import copy
from dataclasses import dataclass
from typing import Any, Literal, cast
from tempfile import NamedTemporaryFile

from reportlab.lib import utils
from reportlab.graphics.shapes import Line, Drawing
from reportlab.lib.colors import Color, black as BLACK
from reportlab.lib.pagesizes import LETTER, inch as INCH
from reportlab.lib.enums import TA_LEFT, TA_RIGHT, TA_CENTER
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.platypus.tableofcontents import TableOfContents
from reportlab.platypus import (
    Flowable,
    Paragraph,
    PageBreak,
    Image,
    Spacer,
    Table,
    TableStyle)

from lib.base_injectable import BaseInjectable
from lib.pdf.interfaces import (
    TextProperties,
    FrontPage,
    TOC,
    Title,
    TableColumn,
    TableProperties,
)
from lib.pdf.page_canvas import PageCanvas
from lib.pdf.document_template import DocumentTemplate


@dataclass
class TOCLevels:
    h1 = ParagraphStyle(name='Heading1', fontSize=14, leading=16)
    h2 = ParagraphStyle(name='Heading2', fontSize=12, leading=14, leftIndent=4)
    h3 = ParagraphStyle(name='Heading3', fontSize=10, leading=12, leftIndent=8)


class PDF(BaseInjectable):
    """Provides functions to build a PDF file."""

    width: float
    """Document width."""

    height: float
    """Document height."""

    __story: list[Flowable]
    """List of elements that will be rendered
    in the PDF.
    """

    __front_page_story: list[Flowable]
    """List of elements of the front page."""

    __toc_story: list[Flowable]
    """List of elements of the table of contents."""

    __front_page_added: bool = False
    """Controls whether the front page
    has been added yet.
    """

    __toc_added: bool = False
    """Controls whether the table of contents
    has been added yet.
    """

    __toc_levels = TOCLevels()

    def __init__(self) -> None:
        """Creates an injection dependency of this module."""

        super().__init__()
        self.styleSheet = getSampleStyleSheet()
        self.__story = []
        self.__front_page_story = []
        self.__toc_story = []
        self.width, self.height = LETTER

    def build(self,
                title: str,
                author: str | None = None,
                pagesize: tuple[float, float] = LETTER,
                main_logo_path: str | None = None,
                secondary_logo_path: str | None = None) -> bytes:
        """Builds the PDF file.

        Parameters
        ----------
        title : str
            Title of the PDF tab.
        author : str | None, optional
            Author, by default None.
        pagesize : tuple[float, float], optional
            Page size, by default LETTER.
        main_logo_path : str | None, optional
            Path of the main logo, by default None.
        secondary_logo_path : str | None, optional
            Path of the secondary logo, by default None.

        Returns
        -------
        bytes
            PDF binary content.
        """

        with NamedTemporaryFile('wb+') as tf:
            self.doc = DocumentTemplate(
                filename=tf,
                pagesize=pagesize,
                title=title,
                author=author)
            self.doc.main_logo_path = main_logo_path
            self.doc.secondary_logo_path = secondary_logo_path
            story = self.__front_page_story + self.__toc_story + self.__story
            self.doc.multiBuild(story, canvasmaker=PageCanvas)
            tf.seek(0)
            return tf.read()

    def get_color_from_hex(self, hex_color: str, alpha: float = 1) -> Color:
        """Gets a `Color` instance from a
        HEX color code.

        Parameters
        ----------
        hex_color : str
            HEX color code.
        alpha : float, optional
            Opacity, by default 1.

        Returns
        -------
        Color
            `Color` instance.
        """

        hex_color = hex_color.lstrip('#')
        rgb = tuple((int(hex_color[i:i+2], 16)/255) for i in (0, 2, 4))
        return Color(
            red=cast(int, rgb[0]),
            green=cast(int, rgb[1]),
            blue=cast(int, rgb[2]),
            alpha=cast(int, alpha))

    def get_color_from_rgb(self,
                            red: float,
                            green: float,
                            blue: float,
                            alpha: float = 1) -> Color:
        """Gets a `Color` instance from
        RGB color values.

        Parameters
        ----------
        red : float
            Red component value.
        green : float
            Green component value.
        blue : float
            Blue component value.
        alpha : float, optional
            Opacity, by default 1.

        Returns
        -------
        Color
            `Color` instance.
        """

        return Color(
            red=cast(int, red/255),
            green=cast(int, green/255),
            blue=cast(int, blue/255),
            alpha=cast(int, alpha))

    def __get_image(self,
                    path: str,
                    width: float,
                    align: Literal['LEFT', 'RIGHT', 'CENTER'] = 'CENTER') -> Image:
        """Gets an `Image` instance from
        an image file path.

        Parameters
        ----------
        path : str
            Image file path.
        width : float
            Image width.
        align : Literal[LEFT, RIGHT, CENTER], optional
            Alignment, by default 'CENTER'

        Returns
        -------
        Image
            `Image` instance.
        """

        img = utils.ImageReader(path)
        iw, ih = img.getSize()
        aspect = ih / float(iw)
        return Image(path, width=width, height=(width * aspect), hAlign=align)

    def __add_front_page_images(self, settings: FrontPage) -> None:
        """Adds the front page images
        if given in the passed `settings`
        settings.

        Parameters
        ----------
        settings : FrontPage
            Front page settings.
        """

        if settings.secondary_logo_path:
            img = self.__get_image(path=settings.secondary_logo_path, width=1.5 * INCH, align='LEFT')
            self.__front_page_story.append(img)
        if settings.main_logo_path:
            self.__front_page_story.append(Spacer(0, 100))
            img = self.__get_image(path=settings.main_logo_path, width=5.5 * INCH)
            self.__front_page_story.append(img)

    def __get_front_page_text(self, settings: FrontPage) -> str:
        """Gets the front page text
        from the passed `settings`
        settings.

        Parameters
        ----------
        settings : FrontPage
            Front page settings.

        Returns
        -------
        str
            Front page text.
        """

        text = ''
        if settings.title:
            text += f'<b>{settings.title.strip().upper()}</b><br/><br/>'
        if settings.subtitle:
            text += f'{settings.subtitle.strip()}<br/><br/>'
        if settings.date:
            date = settings.date.strftime('%d/%m/%Y %I:%M %p')
            text += f'{settings.date_label}: {str(date)}<br/><br/>'
        return text

    def add_spacer(self, width: int, height: int) -> None:
        """Adds a spacer.

        Parameters
        ----------
        width : int
            Width.
        height : int
            Height.
        """

        self.__story.append(Spacer(width, height))

    def add_page_break(self) -> None:
        """Adds a page break."""

        self.__story.append(PageBreak())

    def add_paragraph(self,
                        text: str,
                        style: TextProperties | None = None,
                        spacer: Spacer | None = None) -> None:
        """Adds a paragraph.

        Parameters
        ----------
        text : str
            Paragraph text.
        style : TextProperties | None, optional
            Paragraph text style, by default None.
        spacer : Spacer | None, optional
            Add spacer at bottom, by default None.
        """

        if not style:
            style = TextProperties(align=TA_LEFT)
        paragraph_style = ParagraphStyle(
            name=style.name,
            textColor=style.text_color,
            fontName=style.font_name,
            fontSize=style.font_size,
            leading=style.leading,
            alignment=style.align,
            leftIndent=style.left_indent,
            justifyBreaks=style.justify_breaks,
            justifyLastLine=style.justify_last_line)
        paragraph = Paragraph(text, paragraph_style)
        self.__story.append(paragraph)
        if spacer:
            self.__story.append(spacer)

    def add_front_page(self, settings: FrontPage | None = None) -> None:
        """Adds the front page elements.

        Parameters
        ----------
        settings : FrontPage | None, optional
            Settings, by default None.
        """

        if self.__front_page_added:
            return None
        self.__front_page_added = True

        if not settings:
            settings = FrontPage()

        self.__add_front_page_images(settings)

        text = self.__get_front_page_text(settings)
        if not text:
            self.__front_page_story.append(PageBreak())
            return None

        text_style = ParagraphStyle(
            name='FrontPageText',
            fontName=settings.font_name,
            fontSize=settings.font_size,
            leading=14,
            justifyBreaks=1,
            alignment=settings.align,
            justifyLastLine=1)
        paragraph = Paragraph(text, text_style)
        self.__front_page_story.append(Spacer(0, settings.space))
        self.__front_page_story.append(paragraph)
        self.__front_page_story.append(PageBreak())

    def add_table_of_contents(self, settings: TOC | None = None) -> None:
        """Adds the table of contents.

        Parameters
        ----------
        settings : TOC | None, optional
            Settings, by default None.
        """

        if self.__toc_added:
            return None
        self.__toc_added = True

        if not settings:
            settings = TOC()

        title_style = ParagraphStyle(
            name='TableOfContents',
            fontName=settings.font_name,
            fontSize=settings.font_size,
            alignment=settings.align,
            textColor=settings.text_color)

        toc = TableOfContents()
        toc.levelStyles = [
            self.__toc_levels.h1,
            self.__toc_levels.h2,
            self.__toc_levels.h3]

        self.__toc_story.append(Paragraph(settings.label, title_style))
        self.__toc_story.append(Spacer(0, settings.label_bottom_space))
        self.__toc_story.append(toc)
        self.__toc_story.append(PageBreak())

    def add_line(self, stroke_width: int, color: Color | None = BLACK) -> None:
        """Adds a line.

        Parameters
        ----------
        stroke_width : int
            Stroke width.
        color : Color | None, optional
            Color, by default BLACK.
        """

        drawing = Drawing(500, 1)
        line = Line(-15, 0, 470, 0)
        line.strokeColor = color
        line.strokeWidth = stroke_width
        drawing.add(line)
        self.__story.append(drawing)

    def __add_title_border_bottom(self, color: Color | None = BLACK) -> None:
        """Adds a title border at bottom.

        Parameters
        ----------
        color : Color | None, optional
            Color, by default BLACK.
        """

        self.add_line(stroke_width=2, color=color)
        self.add_spacer(10, 1)
        self.add_line(stroke_width=1, color=color)

    def add_title(self, title: Title) -> None:
        """Adds a title.

        Parameters
        ----------
        title : Title
            Title style.
        """

        if not title.name:
            if title.level == 1:
                title.name = 'Heading1'
            elif title.level == 2:
                title.name = 'Heading2'
            elif title.level == 3:
                title.name = 'Heading3'

        title_style = ParagraphStyle(
            name=title.name,
            fontName=title.font_name,
            fontSize=title.font_size,
            alignment=title.align,
            textColor=title.text_color,
            leftIndent=title.left_indent)
        paragraph = Paragraph(title.text, title_style)
        self.__story.append(paragraph)
        if title.border_space:
            self.add_spacer(0, title.border_space)
        if title.add_border_bottom:
            self.__add_title_border_bottom(title.text_color)
        if title.bottom_space:
            self.add_spacer(0, title.bottom_space)

    def add_title1(self, text: str, text_color: Color) -> None:
        """Adds a 1-level title (similar to HTML <h1> tag).

        Parameters
        ----------
        text : str
            Title text.
        text_color : Color
            Text color.
        """

        title = Title(
            text=text,
            level=1,
            text_color=text_color,
            add_border_bottom=True)
        self.add_title(title)

    def add_title2(self, text: str, text_color: Color) -> None:
        """Adds a 2-level title (similar to HTML <h2> tag).

        Parameters
        ----------
        text : str
            Title text.
        text_color : Color
            Text color.
        """

        title = Title(
            text=text,
            level=2,
            align=TA_LEFT,
            font_size=14,
            bottom_space=0,
            text_color=text_color)
        self.add_title(title)
        self.add_line(stroke_width=1, color=text_color)
        self.add_spacer(0, 16)

    def add_title3(self, text: str, text_color: Color) -> None:
        """Adds a 3-level title (similar to HTML <h3> tag).

        Parameters
        ----------
        text : str
            Title text.
        text_color : Color
            Text color.
        """

        title = Title(
            text=text,
            level=3,
            align=TA_LEFT,
            font_size=12,
            bottom_space=10,
            text_color=text_color)
        self.add_title(title)

    def add_subtitle(self, text: str, text_color: Color | None = BLACK) -> None:
        """Add subtitle.

        Parameters
        ----------
        text : str
            Text.
        text_color : Color | None, optional
            Color, by default BLACK.
        """

        self.add_paragraph(
            text=text,
            style=TextProperties(align=TA_LEFT, font_size=12, text_color=text_color))
        self.add_spacer(0, 10)

    def __get_columns_text(self, columns: list[TableColumn]) -> list[Paragraph]:
        """Gets the formatted and styled
        text of each column.

        Parameters
        ----------
        columns : list[TableColumn]
            Columns.

        Returns
        -------
        list[Paragraph]
            List of columns text.
        """

        formatted_columns = []
        for col in columns:
            col_text = f'<b>{col.text}</b>'
            col_style = ParagraphStyle(
                name='TableColumn',
                fontName=col.font_name,
                fontSize=col.font_size,
                alignment=col.align,
                textColor=col.text_color)
            formatted_columns.append(Paragraph(col_text, col_style))
        return formatted_columns

    def __get_row_count_cells(self,
                            table_properties: TableProperties,
                            table_data: list[list[Any]]) -> list[Any]:
        """Gets the row count cells.

        Parameters
        ----------
        table_properties : TableProperties
            Table properties.
        table_data : list[list[Any]]
            Table data.

        Returns
        -------
        list[Any]
            Row count cells.
        """

        row_count = len(table_data)-1
        if len(table_data[0]) == 1:
            row_count_word = 'elementos' if row_count > 1 else 'elemento'
            cell_text = f'<b>Total: {row_count} {row_count_word}</b>'
            cell_style = ParagraphStyle(
                name='TableCell',
                fontName=table_properties.row_count_font_name,
                fontSize=table_properties.row_count_font_size,
                alignment=TA_CENTER,
                textColor=table_properties.text_color)
            return [Paragraph(cell_text, cell_style)]

        footer_row = ['' for _ in table_data[0]]
        footer_row[0] = 'Cantidad de filas'
        footer_row[-1] = str(row_count)
        row_cells = []
        for i in range(len(footer_row)):
            if i != 0 and i != (len(footer_row) - 1):
                row_cells.append('')
                continue

            cell_text = f'<b>{footer_row[i]}</b>'
            cell_style = ParagraphStyle(
                name='TableCell',
                fontName=table_properties.row_count_font_name,
                fontSize=table_properties.row_count_font_size,
                alignment=TA_LEFT if i == 0 else TA_RIGHT,
                textColor=table_properties.text_color)
            row_cells.append(Paragraph(cell_text, cell_style))

        return row_cells

    def __get_table_style_options(self,
                                table_properties: TableProperties,
                                table_data: list[list[Any]]) -> list[tuple[str, tuple, tuple]]:
        """Gets the table style options.

        Parameters
        ----------
        table_properties : TableProperties
            Table properties.
        table_data : list[list[Any]]
            Table data.

        Returns
        -------
        list[tuple[str, tuple, tuple]]
            Table style options.
        """

        table_style_options = []

        if table_properties.row_line_color:
            table_style_options.append(('LINEABOVE', (0, 0), (-1, -1), 1, table_properties.row_line_color))

        if table_properties.header_background_color:
            table_style_options.append(('BACKGROUND',(0, 0), (-1, 0), table_properties.header_background_color))

        if table_properties.show_row_count:
            if table_properties.row_count_background_color:
                table_style_options.append(('BACKGROUND',(0, -1),(-1, -1), table_properties.row_count_background_color))
            row_cells = self.__get_row_count_cells(table_properties, table_data)
            table_data.append(row_cells)
            if len(row_cells) > 2:
                table_style_options.append(('SPAN', (0, -1), (1, -1)))
                table_style_options.append(('LEFTPADDING', (0, -1), (1, -1), 30))
                table_style_options.append(('RIGHTPADDING', (-1, -1), (-1, -1), 30))

        return table_style_options

    def add_table(self,
                    columns: list[TableColumn],
                    rows: list[list[Any]],
                    table_properties: TableProperties | None = None) -> None:
        """Adds a table.

        Parameters
        ----------
        columns : list[TableColumn]
            Columns.
        rows : list[list[Any]]
            Rows.
        table_properties : TableProperties | None, optional
            Table properties, by default None.
        """

        if not columns or not rows:
            return None

        formatted_columns = self.__get_columns_text(columns)
        table_data: list[Any] = [formatted_columns]
        for row in rows:
            row_cells = []
            for i in range(len(row)):
                cell_style = ParagraphStyle(
                    name='TableCell',
                    fontName=columns[i].font_name,
                    fontSize=columns[i].font_size,
                    alignment=columns[i].align,
                    textColor=columns[i].text_color)
                cell_text = f'<b>{str(row[i])}</b>' if columns[i].bold else str(row[i])
                row_cells.append(Paragraph(cell_text, cell_style))
            table_data.append(row_cells)

        table_style_options = []
        if table_properties:
            table_style_options = self.__get_table_style_options(table_properties, table_data)

        table = Table(table_data, colWidths=[col.width for col in columns])
        table_style = TableStyle(table_style_options)
        table.setStyle(table_style)
        self.__story.append(table)
        self.add_spacer(0, 10)

    def __translate_value(self, value: Any, translation: dict[str, str] | None = None) -> str:
        """Translates a value.

        Parameters
        ----------
        value : Any
            Value to be translated.
        translation : dict[str, str] | None, optional
            Translation dictionary, by default None.

        Returns
        -------
        str
            Translated value.
        """

        if isinstance(value, bool):
            return 'Sí' if value else 'No'
        if isinstance(value, str) and translation:
            return translation.get(value, value)
        return str(value)

    def __get_table_columns(self,
                            column_names: list[str],
                            column_style: TableColumn | list[TableColumn] | None = None,
                            auto_width: bool = False,
                            translation: dict[str, str] | None = None) -> list[TableColumn]:
        """Gets a `TableColumn` instance for each
        column in the passed `column_names` arg.

        Parameters
        ----------
        column_names : list[str]
            Column names.
        column_style : TableColumn | list[TableColumn] | None, optional
            Columns style, by default None.
        auto_width : bool, optional
            Auto width for columns, by default False.
        translation : dict[str, str] | None, optional
            Translation dictionary, by default None.

        Returns
        -------
        list[TableColumn]
            List of table columns.
        """

        MAX_WIDTH = 450
        columns: list[TableColumn] = []
        column_name_count = len(column_names)
        calculated_width = int(MAX_WIDTH / column_name_count)
        for i in range(column_name_count):
            column_text = self.__translate_value(column_names[i], translation) if translation else column_names[i]
            if column_style:
                if isinstance(column_style, list):
                    table_column = copy(column_style[i])
                else:
                    table_column = copy(column_style)
            else:
                table_column = TableColumn(text=column_text)
            if auto_width:
                table_column.width = calculated_width
            table_column.text = column_text
            columns.append(table_column)
        return columns

    def add_table_from_dict(self,
                            value: dict[str, Any],
                            column_style: TableColumn | list[TableColumn] | None = None,
                            auto_width: bool = False,
                            table_properties: TableProperties | None = None,
                            translation: dict[str, str] | None = None) -> None:
        """Generates and adds a table from a dictionary.

        Parameters
        ----------
        value : dict[str, Any]
            Source.
        column_style : TableColumn | list[TableColumn] | None, optional
            Columns style, by default None.
        auto_width : bool, optional
            Auto width for columns, by default False.
        table_properties : TableProperties | None, optional
            Table properties, by default None.
        translation : dict[str, str] | None, optional
            Translation dictionary, by default None.
        """

        if not value:
            return None

        rows = []
        for k, v in value.items():
            k = self.__translate_value(k, translation) if translation else k
            if isinstance(v, (list, tuple, set)):
                rows.append([k, ', '.join(v)])
            else:
                rows.append([k, str(self.__translate_value(v, translation) if translation else v)])

        COLUMN_NAMES = ['Campo', 'Valor']
        columns = self.__get_table_columns(COLUMN_NAMES, column_style, auto_width, translation)
        self.add_table(columns, rows, table_properties)

    def add_table_from_list(self,
                            value: list[Any],
                            column_name_for_1d: str | None = None,
                            column_style_for_1d: TableColumn | None = None,
                            column_style: TableColumn | list[TableColumn] | None = None,
                            auto_width: bool = False,
                            table_properties: TableProperties | None = None,
                            translation: dict[str, str] | None = None) -> None:
        """Generates and adds a table from a list.

        Parameters
        ----------
        value : list[Any]
            Source.
        column_name_for_1d : str | None, optional
            Column name for 1-dimension list, by default None.
        column_style_for_1d : TableColumn | None, optional
            Column style for 1-dimension list, by default None.
        column_style : TableColumn | list[TableColumn] | None, optional
            Columns style, by default None.
        auto_width : bool, optional
            Auto width for columns, by default False.
        table_properties : TableProperties | None, optional
            Table properties, by default None.
        translation : dict[str, str] | None, optional
            Translation dictionary, by default None.
        """

        if not value:
            return None

        keys = []
        rows = []
        is_dict_list = isinstance(value[0], dict)
        if is_dict_list:
            keys.extend(list(value[0].keys()))
            for element in value:
                values = []
                for key in keys:
                    values.append(self.__translate_value(element[key], translation))
                rows.append(values)
            columns = self.__get_table_columns(keys, column_style, auto_width, translation)
        else:
            for element in value:
                rows.append([self.__translate_value(element, translation)])
            if column_style_for_1d:
                table_column = copy(column_style_for_1d)
            else:
                table_column = TableColumn(width=450)
            table_column.text = self.__translate_value(column_name_for_1d, translation) if column_name_for_1d else 'Lista'
            columns = [table_column]
        self.add_table(columns, rows, table_properties)

from typing import Literal
from datetime import datetime
from dataclasses import dataclass

from reportlab.lib.colors import Color, black as <PERSON><PERSON><PERSON><PERSON>
from reportlab.lib.enums import TA_CENTER


@dataclass
class CommonProperties:
    align: Literal[0, 1, 2, 4] = TA_CENTER
    """
    Text alignment.
    It can be one of the constants
    imported from `reportlab.lib.enums`:

    TA_LEFT = 0

    TA_CENTER = 1

    TA_RIGHT = 2

    TA_JUSTIFY = 4
    """

    text_color: Color | None = BLACK
    """Text color."""


@dataclass
class FontProperties:
    font_size: int = 12
    """Font size."""

    font_name: str = 'Helvetica'
    """Font name."""


@dataclass
class TextProperties(CommonProperties, FontProperties):
    leading: int = 14
    """Space between adjacent lines of type."""

    name: str = 'Text'
    """Text name."""

    justify_breaks: int = 1
    """Controls whether lines deliberately
    broken with a <br/> tag should be justified.
    """

    justify_last_line: int = 1
    """Controls whether the justified alignment
    should be applied to the last line of the text.
    """

    left_indent: int = 0
    """Left indent."""


@dataclass
class FrontPage(CommonProperties, FontProperties):
    title: str | None = None
    """Title of the document."""

    subtitle: str | None = None
    """Subtitle of the document."""

    date: datetime | None = None
    """Creation date of the document."""

    date_label: str | None = 'Fecha y hora'
    """Label of the creation date."""

    space: int = 250
    """Space between logo and text."""

    main_logo_path: str | None = None
    """Path of the main logo."""

    secondary_logo_path: str | None = None
    """Path of the secondary logo."""


@dataclass
class TOC(CommonProperties, FontProperties):
    label: str | None = 'Tabla de contenidos'

    font_size: int = 16
    """Font size."""

    label_bottom_space: int = 22
    """Space between label and table."""


@dataclass
class Title(CommonProperties, FontProperties):
    text: str = 'Title'
    """Title text."""

    level: Literal[0, 1, 2, 3] = 0
    """Heading level."""

    name: str | None = None
    """Title name.

    Defaults:
    - `Heading1` for 1-level title.
    - `Heading2` for 2-level title.
    - `Heading3` for 3-level title.
    """

    font_size: int = 16
    """Font size."""

    left_indent: int = 0
    """Left indent."""

    border_space: int = 10
    """Space between text and border."""

    bottom_space: int = 22
    """Space between border and content below."""

    add_border_bottom: bool = False
    """Add bottom border."""


@dataclass
class TableColumn(CommonProperties, FontProperties):
    text: str = 'Column'
    """Column text."""

    width: int = 100
    """Column width."""

    font_size: int = 10
    """Cells font size."""

    bold: bool = False
    """Bold style."""


@dataclass
class TableProperties(CommonProperties):
    show_row_count: bool = False
    """Show row count."""

    row_line_color: Color | None = None
    """Row line color."""

    header_background_color: Color | None = None
    """Header background color."""

    row_count_background_color: Color | None = None
    """Row count row background color."""

    row_count_font_size: int = 10
    """Row count font size."""

    row_count_font_name: str = 'Helvetica'
    """Row count font name."""

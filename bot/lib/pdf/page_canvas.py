from typing import Any

from reportlab.pdfgen.canvas import Canvas
from reportlab.lib.pagesizes import LETTER, inch as INCH


class PageCanvas(Canvas):
    """Canvas of the header and footer
    of the document's pages.
    """

    width: float
    """Document width."""

    height: float
    """Document height."""

    pages: list[dict[Any, Any]]
    """List of pages of the document."""

    main_logo_path: str | None = None
    """Path of the main logo."""

    secondary_logo_path: str | None = None
    """Path of the secondary logo."""

    def __init__(self, *args, **kwargs) -> None:
        Canvas.__init__(self, *args, **kwargs)
        self.width, self.height = LETTER

    def showPage(self) -> None:
        self.draw_canvas()
        super().showPage()

    def draw_canvas(self) -> None:
        """Draws the header and footer on a page.

        The passed `self` corresponds to the current page.
        """

        if self._pageNumber < 2:
            return None

        self.saveState()

        # ----------- General settings -----------
        self.setStrokeColorRGB(0, 0, 0)
        self.setLineWidth(0.5)

        # ----------- Header canvas -----------
        SECONDARY_LOGO_X_AXIS_POSITION = self.width - INCH * 8 - 5
        MAIN_LOGO_X_AXIS_POSITION = self.width - INCH * 2
        LOGOS_Y_AXIS_POSITION = self.height - 50
        LOGOS_WIDTH = 100
        SECONDARY_LOGO_HEIGHT = 30
        MAIN_LOGO_HEIGHT = 40
        HEADER_LINE_X_AXIS_VALUE = 50
        HEADER_LINE_Y_AXIS_VALUE = 740

        if self.secondary_logo_path:
            self.drawImage(image=self.secondary_logo_path,
                x=SECONDARY_LOGO_X_AXIS_POSITION,
                y=LOGOS_Y_AXIS_POSITION,
                width=LOGOS_WIDTH,
                height=SECONDARY_LOGO_HEIGHT,
                preserveAspectRatio=True,
                mask='auto')
        if self.main_logo_path:
            self.drawImage(image=self.main_logo_path,
                x=MAIN_LOGO_X_AXIS_POSITION,
                y=LOGOS_Y_AXIS_POSITION,
                width=LOGOS_WIDTH,
                height=MAIN_LOGO_HEIGHT,
                preserveAspectRatio=True,
                mask='auto')
        if self.secondary_logo_path or self.main_logo_path:
            self.line(
                x1=HEADER_LINE_X_AXIS_VALUE,
                y1=HEADER_LINE_Y_AXIS_VALUE,
                x2=self.width - HEADER_LINE_X_AXIS_VALUE,
                y2=HEADER_LINE_Y_AXIS_VALUE)

        # ----------- Footer canvas -----------
        FOOTER_LINE_X_AXIS_VALUE = 60
        FOOTER_LINE_Y_AXIS_VALUE = 78
        PAGINATION_TEXT_X_AXIS_POSITION = 80
        PAGINATION_TEXT_Y_AXIS_POSITION = 65

        self.setFont('Helvetica', 10)
        self.line(
            x1=FOOTER_LINE_X_AXIS_VALUE,
            y1=FOOTER_LINE_Y_AXIS_VALUE,
            x2=self.width - FOOTER_LINE_X_AXIS_VALUE,
            y2=FOOTER_LINE_Y_AXIS_VALUE)
        self.drawString(
            x=self.width - PAGINATION_TEXT_X_AXIS_POSITION,
            y=PAGINATION_TEXT_Y_AXIS_POSITION,
            text=str(self._pageNumber))

        self.restoreState()

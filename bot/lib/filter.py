import datetime
import typing
import re

from beanie.odm.queries.find import Find<PERSON><PERSON>

from lib.base_model import BaseModel
from lib.base_injectable import BaseInjectable
from lib.exceptions import BadRequestError


class FilterOptions(typing.TypedDict):
    exp: str
    value: int | float | str | list[str | float | int]


FormatFilter = dict[str, FilterOptions]

T = typing.TypeVar('T', bound=BaseModel)


class Filter(typing.Generic[T], BaseInjectable):
    """Creates filtering Mongo expressions."""

    def create(self, data: FormatFilter, query: FindMany[T], fetch_link: bool = False) -> FindMany[T]:
        """Creates a list of filtering Mongo expressions.

        Parameters
        ----------
        data : FormatFilter
            Dict of expressions to build the filtering query.

            Example::

            {
                "foo": {
                    "exp": "eq",
                    "value": "bar"
                },
                "baz": {
                    "exp": "eq",
                    "value": "qux"
                }
                ...
            }
        ...

        query : FindMany[T]
            Prepared Beanie query.
        fetch_link : bool, optional
            Fetch links, by default False.

        Returns
        -------
        Find<PERSON>any[T]
            Filtered query.

        Raises
        ------
        BadRequestError
            Dates expression must be one of ["eq", "lte", "gte", "lt", "gt"].
        """

        list_filters: list[dict[str, dict[str, str | datetime.datetime | float | int | list[str | float | int]]]] = []
        for item in data:
            expression = data[item]['exp']
            value = data[item]['value']
            regex_to_validate_date = re.compile(r'^[0-9]{4}(-[0-9]{2}){2}( +[0-9]{2}(:[0-9]{2}){2})*')
            if re.fullmatch(regex_to_validate_date, str(value).strip()):
                if expression not in ['eq', 'lte', 'gte', 'lt', 'gt']:
                    raise BadRequestError('date_restricted')
                value = str(value).strip()
                try:
                    date = datetime.datetime.strptime(str(value), '%Y-%m-%d %H:%M:%S')
                    list_filters.append({f'{item}': {f'${expression}': date}})
                except Exception:
                    value += ' 00:00:00'
                    date = datetime.datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                    list_filters.append({f'{item}': {f'${expression}': date}})
            else:
                if expression in ['regex']:
                    list_filters.append({ f'{item}': { f'${expression}': f'(?i){value}(?i)' }})
                else:
                    list_filters.append({ f'{item}': { f'${expression}': value }})

        return query.find(*list_filters, fetch_links=fetch_link, collation={ 'locale': 'es', 'strength': 1 })

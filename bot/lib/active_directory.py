from typing import Any, cast

import jwt
from ldap3 import Attribute, Connection, Entry, Server
from starlette.concurrency import run_in_threadpool

from lib import config
from dtos.automation_dto import ConsultedUserDataResponseDto
from lib.base_injectable import BaseInjectable
from lib.exceptions import NotFoundError, UnauthorizedError
from lib.tools import retry

DISABLED_USER_FLAG = 514
SEARCH_FILTER = '(sAMAccountName={})'


class ADUser(ConsultedUserDataResponseDto):
    sam_account_name: str
    user_principal_name: str
    first_name: str
    last_name: str
    fullname: str
    document: str
    mail: str
    active: bool
    member_of: list[str]

    @property
    def has_privileges(self) -> bool:
        return self.active or len(self.member_of) > 0

    def create_jwt(self) -> str:
        payload = {
            'sam_account_name': self.sam_account_name,
            'user_principal_name': self.user_principal_name,
            'fullname': self.fullname,
            'member_of': self.member_of,
        }
        return jwt.encode(payload, 'session', 'HS256')


class ActiveDirectory(BaseInjectable):
    """Create a connection instance to an Active Directory server."""

    def __init__(self):
        self.server = Server(
            config.LDAP_SERVER,
            use_ssl=config.LDAP_USE_SSL,
            get_info='ALL',
            connect_timeout=10,
        )
        user = str(config.LDAP_USER)
        if config.LDAP_DOMAIN:
            self.connection_user = f'{config.LDAP_DOMAIN}\\{user}'
        else:
            self.connection_user = user

    def __get_field(
        self,
        user_entry: Entry,
        field: str,
        default: Any = None,
    ) -> Any:
        """Get a field from user entry.

        Parameters
        ----------
        user_entry : Entry
            User entry.
        field : str
            Field name.
        default : Any, optional
            Default value, by default None.

        Returns
        -------
        Any
            Field value.

        """
        if not hasattr(user_entry, field):
            return default
        attr = cast(Attribute, getattr(user_entry, field))
        if not hasattr(attr, 'value'):
            return default
        value = attr.value
        if isinstance(value, str):
            value = value.strip()
        if isinstance(value, (int, float)):
            return value
        return value if value else default

    def __extract_document(self, user_entry: Entry) -> str:
        """Extract the document from
        the `employeeID` or `cardID` AD fields.

        Parameters
        ----------
        user_entry : Entry
            User entry.

        Returns
        -------
        str
            Document number.

        """
        document = self.__get_field(user_entry, 'employeeID')
        if not document:
            document = self.__get_field(user_entry, 'cardID')
        if not document:
            return ''
        return document

    def __extract_email(self, user_entry: Entry) -> str:
        """Extract the email from
        the `mail` or `userPrincipalName` AD fields.

        Parameters
        ----------
        user_entry : Entry
            User entry.

        Returns
        -------
        str
            Email.

        """
        email = self.__get_field(user_entry, 'mail')
        if not email:
            email = self.__get_field(user_entry, 'userPrincipalName')
        if not email:
            return ''
        return str(email)

    def __check_user_state(self, user_entry: Entry) -> bool:
        """Checks the state of the user.

        Parameters
        ----------
        user_entry : Entry
            User entry.

        Returns
        -------
        bool
            Whether user is active.

        """
        user_account_control = self.__get_field(
            user_entry, 'userAccountControl'
        )
        return (
            user_account_control != DISABLED_USER_FLAG
            if user_account_control
            else False
        )

    def __extract_groups(self, user_entry: Entry) -> list:
        """Extract the groups from the `memberOf` AD field.

        Parameters
        ----------
        user_entry : Entry
            User entry.

        Returns
        -------
        list
            Groups of the user.

        """
        member_of = self.__get_field(user_entry, 'memberOf', [])
        if not isinstance(member_of, list):
            return []
        return member_of

    def __build_ad_user(self, user_entry: Entry) -> ADUser:
        """Build an ADUser instance.

        Parameters
        ----------
        user_entry : Entry
            User entry.

        Returns
        -------
        ADUser
            Data of the found AD user.

        """
        first_name = self.__get_field(user_entry, 'givenName', '')
        last_name = self.__get_field(user_entry, 'sn', '')
        fullname = f'{first_name} {last_name}'.strip()
        return ADUser(
            sam_account_name=self.__get_field(
                user_entry, 'sAMAccountName', ''
            ),
            user_principal_name=self.__get_field(
                user_entry, 'userPrincipalName', ''
            ),
            first_name=first_name,
            last_name=last_name,
            fullname=fullname,
            document=self.__extract_document(user_entry),
            mail=self.__extract_email(user_entry),
            active=self.__check_user_state(user_entry),
            member_of=self.__extract_groups(user_entry),
        )

    def _get_connection(self) -> Connection:
        """Get a connection to the Active Directory server.

        Returns
        -------
        Connection
            Connection to the Active Directory server.

        """
        return Connection(
            self.server,
            user=self.connection_user,
            password=str(config.LDAP_PASSWORD),
            auto_bind=True,
            receive_timeout=10,
        )

    @retry(times=1, raise_exceptions=(NotFoundError,))
    def search_user(
        self,
        sam_account_name: str,
        raise_if_not_found: bool = False,
    ) -> ADUser | None:
        """Search for an user by its SAM account name.

        Parameters
        ----------
        sam_account_name : str
            SAM account name.
        raise_if_not_found : bool, optional
            Raise a `NotFoundError` if user
            does not exist, by default False.

        Returns
        -------
        ADUser
            Data of the found AD user.

        Raises
        -------
        NotFoundError
            If user was not found.

        """
        with self._get_connection() as conn:
            search_filter = SEARCH_FILTER.format(sam_account_name)
            conn.search(
                search_base=config.LDAP_BASE_DN,
                search_filter=search_filter,
                attributes='*',
            )
            if len(conn.entries) == 0:
                if raise_if_not_found:
                    raise NotFoundError()
                return None

            user_entry = conn.entries[0]
            return self.__build_ad_user(user_entry)

    @retry(times=1, raise_exceptions=(UnauthorizedError,))
    def login(self, sam_account_name: str, password: str) -> ADUser:
        """Authenticate a user.

        Parameters
        ----------
        sam_account_name : str
            SAM account name.
        password : str
            Password.

        Returns
        -------
        ADUser
            Data of the found AD user.

        Raises
        ------
        UnauthorizedError
            If user credentials are not valid.

        """
        with self._get_connection() as conn:
            search_filter = SEARCH_FILTER.format(sam_account_name)
            conn.search(
                search_base=config.LDAP_BASE_DN,
                search_filter=search_filter,
                attributes='*',
            )
            if len(conn.entries) == 0:
                raise UnauthorizedError('Usuario o contraseña no válidos.')

            user_entry = conn.entries[0]
            distinguished_name = self.__get_field(
                user_entry, 'distinguishedName', ''
            )
            if not distinguished_name or not conn.rebind(
                user=distinguished_name, password=password
            ):
                raise UnauthorizedError('Usuario o contraseña no válidos.')

            return self.__build_ad_user(user_entry)

    async def async_login(
        self,
        sam_account_name: str,
        password: str,
    ) -> ADUser:
        return await run_in_threadpool(self.login, sam_account_name, password)

from i18n import I18<PERSON>
from lib.active_directory import ActiveDirectory
from lib.ariba_session import AribaSession
from lib.data_frame_tools import DataFrame
from lib.export import ExportData
from lib.filter import Filter
from lib.idm_client import IDMClient
from lib.injectable import injectable
from lib.log import Log
from lib.mail import Mail
from lib.pdf import PDF
from lib.porfin_retiros_session import PorfinRetirosSession
from lib.porfin_session import PorfinSession
from lib.query import Query
from lib.results_exporter import ResultsExporter
from lib.salesforce import Salesforce
from lib.salud_web_retiros_session import SaludWebRetirosSession
from lib.salud_web_session import SaludWebSession
from lib.saml import SAML
from lib.translate import Translate
from modules import modules
from services import services
from validators import validators


class Injectables:
    """Dependency injection module.

    There are three injection methods to inject an element::

        # Same instance for all the requests
        injectable.add_singleton(MyInjectable)

        # Same instance for the current request
        # but different for the rest of the requests
        injectable.add_scoped(MyInjectable)

        # Always creates a new instance
        injectable.add_transient(MyInjectable)

    An element must inherit from the the BaseInjectable class
    in order to inject it as a dependency::

        from lib.base_injectable import BaseInjectable

        class MyInjectable(BaseInjectable)
            ...

    Check the "dependency injection" section in the
    development documentation file (docs/DEVELOPMENT.md)
    to have more information about how the dependency injection works.
    """

    @staticmethod
    def execute():
        """Inject all dependencies."""
        Injectables.salesforce()
        Injectables.porfin()
        Injectables.ariba_session()
        Injectables.salud_web_session()
        Injectables.active_directory()
        Injectables.i18n()
        Injectables.tools()
        Injectables.modules()
        Injectables.services()
        Injectables.validators()
        injectable.end()

    @staticmethod
    def salesforce():
        """Inject the Salesforce connection module."""
        injectable.add_scoped(Salesforce)

    @staticmethod
    def porfin():
        """Inject the Porfin connection modules."""
        injectable.add_singleton(PorfinSession)
        injectable.add_singleton(PorfinRetirosSession)

    @staticmethod
    def ariba_session():
        """Inject the Ariba connection modules."""
        injectable.add_singleton(AribaSession)

    @staticmethod
    def salud_web_session():
        """Inject the Salud Web connection modules."""
        injectable.add_singleton(SaludWebSession)
        injectable.add_singleton(SaludWebRetirosSession)

    @staticmethod
    def active_directory():
        """Inject the Active Directory connection module."""
        injectable.add_scoped(ActiveDirectory)

    @staticmethod
    def i18n():
        """Inject the I18N module."""
        injectable.add_transient(I18N)

    @staticmethod
    def tools():
        """Inject the tools and libraries."""
        injectable.add_transient(ExportData)
        injectable.add_transient(Filter)
        injectable.add_transient(Translate)
        injectable.add_transient(Query)
        injectable.add_transient(Mail)
        injectable.add_transient(Log)
        injectable.add_transient(PDF)
        injectable.add_transient(SAML)
        injectable.add_transient(DataFrame)
        injectable.add_transient(ResultsExporter)
        injectable.add_transient(IDMClient)

    @staticmethod
    def modules():
        """Inject the automation modules."""
        for module in modules:
            injectable.add_scoped(module)

    @staticmethod
    def services():
        """Inject the services."""
        for service in services:
            injectable.add_scoped(service)

    @staticmethod
    def validators():
        """Inject the validators."""
        for validator in validators:
            injectable.add_scoped(validator)

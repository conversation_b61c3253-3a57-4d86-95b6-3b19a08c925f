import base64
from dataclasses import dataclass
from typing import NewType, cast

import requests

from lib import config
from lib.base_injectable import BaseInjectable
from lib.exceptions import BadRequestError
from lib.logger import logger
from lib.mail_template import mail_template

RequestFiles = NewType('RequestFiles', list[tuple[str, tuple[str, bytes]]])


@dataclass
class FileData:
    name: str
    data: str


class Mail(BaseInjectable):
    """Generate and send mails."""

    def __init__(self) -> None:
        self.__url = config.MAILER_URL
        self.__type = config.MAILER_TYPE
        self.__contract = config.MAILER_CONTRACT
        self.__from = config.MAILER_SEND_FROM

    def __get_files(self, files_data: list[FileData]) -> RequestFiles:
        """Decode the base64-encoded files and create the attachments payload.

        Parameters
        ----------
        files_data : list[FileData]
            List of objects containing the name and base64-encoded data of each file.

        Returns
        -------
        RequestFiles
            Attachments payload.

        """
        return cast(
            RequestFiles,
            [
                ('files', (file.name, base64.b64decode(file.data)))
                for file in files_data
            ],
        )

    def send(
        self,
        to: str,
        subject: str,
        message: str,
        files_data: list[FileData] | None = None,
        assert_sent: bool = True,
        ignore_errors: bool = False,
        send_from: str | None = None,
    ):
        """Send a mail.

        Parameters
        ----------
        to : str
            Recipients.
        subject : str
            Subject.
        message : str
            Message.
        files_data : list[FileData] | None, optional
            List of objects containing the name and base64-encoded
            data of each file, by default None.
        assert_sent : bool, optional
            Check if the mail was sent, by default True.
        ignore_errors : bool, optional
            Ignore errors, by default False.
        send_from : str | None, optional
            Specify a different sender, by default None (if None,
            the sender will be the value of SEND_FROM environment variable).

        Raises
        ------
        BadRequestError
            If mail could not be sent.

        """
        try:
            if not self.__from:
                raise BadRequestError(
                    'No se pudo enviar el correo.'
                    ' La variable de entorno "SEND_FROM" es requerida'
                )

            if not to:
                raise BadRequestError(
                    'No se pudo enviar el correo.'
                    ' El destinatario es requerido'
                )

            if not subject or not message:
                raise BadRequestError(
                    'No se pudo enviar el correo.'
                    ' El asunto y el mensaje son requeridos'
                )

            payload = {
                'to': to,
                'from': send_from if send_from else self.__from,
                'subject': subject,
                'type': self.__type,
                'contract': self.__contract,
                'template': message,
            }

            files = None
            if files_data:
                files = self.__get_files(files_data)

            response = requests.post(
                self.__url, data=payload, files=files, verify=False
            )
            response.raise_for_status()

            response_data = response.json()
            if assert_sent:
                assert (
                    'state' in response_data
                    and response_data['state'] == 'sent'
                )

        except Exception as e:
            logger.error(f'Error al enviar el correo: {str(e)}')
            if not ignore_errors:
                raise BadRequestError('No se pudo enviar el correo')

    def generate_data_fields(self, data: dict[str, str]) -> str:
        """Generate an html fields template.

        Parameters
        ----------
        data : dict[str, str]
            Data fields.

        Returns
        -------
        str
            Html fields template.

        """
        html_tags = ''
        for key, value in data.items():
            html_tags += (
                f'<br aria-hidden="true"><strong data-ogsc=""'
                f' style="color: rgb(106, 180, 229) !important;">'
                f'{key}:&nbsp;</strong>{value}'
            )
        return html_tags

    def generate_template(
        self, subject: str, description: str, data: dict[str, str]
    ) -> str:
        """Generate a mail template.

        Parameters
        ----------
        subject : str
            Subject.
        description : str
            Description of the mail.
        data : dict[str, str]
            Data fields.

        Returns
        -------
        str
            Mail template.

        """
        template = mail_template
        template = template.replace('{{subject}}', subject)
        template = template.replace('{{description}}', description)
        template = template.replace(
            '{{data}}', self.generate_data_fields(data)
        )
        template = template.replace('{{from}}', self.__from)
        return template

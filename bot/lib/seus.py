from dataclasses import dataclass
from datetime import datetime, timed<PERSON><PERSON>
from typing import Literal

from asyncify import asyncify
from starlette.concurrency import run_in_threadpool

from lib import config
from lib.auth import AuthRole
from lib.exceptions import BadRequestError
from lib.http.sessions import TLSSession
from lib.logger import logger
from lib.tools import sha256_from_string

HEADERS = {'Content-Type': 'application/json'}


@dataclass
class AuthenticatedUserData:
    session_token: str
    username: str
    dni: str
    email: str
    fullname: str
    repository: str
    roles: list[str]


class SEUS:
    """Provide functions to perform queries on SEUS."""

    service_conf_cache = None
    service_conf_cache_exp = None

    def __init__(self, service_provider: str) -> None:
        """Initialize the instance with a service provider.

        Parameters
        ----------
        service_provider : str
            Service provider name.

        """
        self.sp = service_provider

    @staticmethod
    def _check_sp_conf_cache_exp() -> bool:
        """Check the expiration of the service provider cache.

        Returns
        -------
        bool
            Whether is expired.

        """
        if not SEUS.service_conf_cache_exp:
            return True
        if SEUS.service_conf_cache_exp < datetime.now():
            return False
        return True

    @asyncify
    def _get_service_provider_conf(self) -> dict[str, str]:
        """Get the SEUS 4 service provider configuration.

        Returns
        -------
        dict[str, str]
            Service provider configuration.

        Raises
        ------
        BadRequestError
            If service provider configuration could not be obtained.

        """
        session = TLSSession()
        try:
            if SEUS.service_conf_cache and self._check_sp_conf_cache_exp():
                return SEUS.service_conf_cache

            params = {'serviceId': self.sp}
            response = session.get(config.SERVICE_URL, params=params)
            session.close()

            conf = response.json()
            SEUS.service_conf_cache = conf
            SEUS.service_conf_cache_exp = datetime.now() + timedelta(
                days=config.SERVICE_CONF_CACHE_TIME
            )
            return conf
        except Exception as e:
            session.close()
            message = (
                f'No fue posible obtener la configuración'
                f' del service provider {self.sp}.'
            )
            logger.error(f'MESSAGE: {message}; DETAIL: {str(e)}')
            raise BadRequestError(message)

    async def _get_url_from_sp(self, url_key: str) -> str:
        """Get a URL from the service provider configuration.

        Parameters
        ----------
        url_key : str
            URL key.

        Returns
        -------
        str
            URL.

        """
        sp_conf = await self._get_service_provider_conf()
        if url_key not in sp_conf:
            raise KeyError(f'La URL {url_key} no fue encontrada en {self.sp}')
        return sp_conf[url_key]

    async def get_authenticated_user_data(
        self, session_token: str
    ) -> AuthenticatedUserData | None:
        """Get the authenticated user data.

        Parameters
        ----------
        session_token : str
            Session token (Value of `appTag` cookie).

        Returns
        -------
        AuthenticatedUserData | None
            Authenticated user data if session is valid.

        Raises
        ------
        BadRequestError
            If user data could not be obtained.

        """
        session = TLSSession()
        url = await self._get_url_from_sp('tokenValidationServiceV2Url')
        try:
            data = {'appTag': session_token, 'service': self.sp}
            response = await run_in_threadpool(
                session.post, url=url, headers=HEADERS, json=data
            )
            session.close()
            if not response.ok:
                return None

            content = response.json()
            user_data = AuthenticatedUserData(
                session_token=content['appTag'],
                username=content['username'],
                dni=content.get('dni', ''),
                email=content.get('email', ''),
                fullname=content.get('fullname', ''),
                repository=content.get('repository', ''),
                roles=[],
            )
            await self.add_authorization_roles(user_data)
            return user_data
        except Exception as e:
            session.close()
            message = (
                'No fue posible procesar el JSON de los datos del usuario.'
            )
            logger.error(f'MESSAGE: {message}; DETAIL: {str(e)}')
            raise BadRequestError(message)

    async def get_authorization_roles(
        self, username: str, repository: str
    ) -> list[str]:
        """Get the authorization roles.

        Parameters
        ----------
        username : str
            Username.
        repository : str
            Repository name.

        Returns
        -------
        list[str]
            Roles of the user.

        """
        has_consult = await self.check_permissions_on_resource(
            username,
            repository,
            AuthRole.CONSULT.value,
        )
        has_remove = await self.check_permissions_on_resource(
            username,
            repository,
            AuthRole.REMOVE.value,
        )
        roles = []
        if has_consult:
            roles.append(AuthRole.CONSULT.value)
        if has_remove:
            roles.append(AuthRole.REMOVE.value)
        return roles

    async def add_authorization_roles(
        self, authenticated_data: AuthenticatedUserData
    ) -> None:
        """Add the authorization roles to the authenticated user data.

        Parameters
        ----------
        authenticated_data : AuthenticatedUserData
            Authenticated user data.

        """
        roles = await self.get_authorization_roles(
            authenticated_data.username,
            authenticated_data.repository,
        )
        authenticated_data.roles.extend(roles)

    async def check_permissions_on_resource(
        self,
        username: str,
        repository: str,
        resource: str,
        method: Literal['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] = 'POST',
    ) -> bool:
        """Check if an username has permissions on a resource.

        Parameters
        ----------
        username : str
            Username.
        repository : str
            Repository name.
        resource : str
            Resource (path).
        method : Literal['GET', 'POST', 'PUT', 'DELETE', 'PATCH'], optional
            Method, by default 'POST'.

        Returns
        -------
        bool
            Whether user has permissions on the resource.

        Raises
        ------
        BadRequestError
            If permissions could not be verified.

        """
        session = TLSSession()
        url = await self._get_url_from_sp('authorizationServiceV2Url')
        try:
            data = {
                'service': self.sp,
                'login': username,
                'repository': repository,
                'resource': resource,
                'action': method,
            }
            x_seus_request = f'{self.sp}|{username}|{repository}'
            headers = {'X-SEUS-REQUEST': sha256_from_string(x_seus_request)}
            headers.update(HEADERS)
            response = await run_in_threadpool(
                session.post, url=url, json=data, headers=headers
            )
            session.close()
            return response.json() if response.ok else False
        except Exception as e:
            session.close()
            message = (
                f'No fue posible procesar el JSON de la verificación'
                f' de permisos del usuario {username}.'
            )
            logger.error(
                f'MESSAGE: {message}; USERNAME: {username};'
                f' REPOSITORY: {repository}; RESOURCE: {resource};'
                f' METHOD: {method} DETAIL: {str(e)}'
            )
            raise BadRequestError(message)

    async def get_logout_url(self) -> str:
        """Get the logout URL of the service provider.

        Returns
        -------
        str
            Logout URL.

        """
        return await self._get_url_from_sp('logoutUrl')

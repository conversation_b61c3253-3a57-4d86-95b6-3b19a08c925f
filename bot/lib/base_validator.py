from abc import ABC
from i18n import I18<PERSON>

from lib.base_dto import BaseRequestDto
from lib.base_service import BaseService
from lib.validator.error_validator import <PERSON>rrorValidator
from lib.validator.data_validator import DataValidator


class BaseValidator(ABC):
    """Validates the fields of a request DTO."""

    error: ErrorValidator
    service: BaseService
    i18n: I18N

    def __init__(self, service: BaseService, i18n: I18N) -> None:
        """Creates a dependency object of this validator.

        Parameters
        ----------
        service : BaseService
            Injected dependency of the service.
        i18n : I18N
            Injected dependency of the i18n translator module.
        """

        self.service = service
        self.i18n = i18n

    def validator(self, request_dto: BaseRequestDto) -> DataValidator:
        """Adds a validator to the data validator for each field in the request data.

        Parameters
        ----------
        request_dto : BaseRequestDto
            Request data.

        Returns
        -------
        DataValidator
            Data validator.
        """

        validator = DataValidator()
        return validator

    async def validate(self, request_dto: BaseRequestDto) -> bool:
        """Validates the request data.

        Parameters
        ----------
        request_dto : BaseRequestDto
            Request data.

        Returns
        -------
        bool
            Whether data is valid.
        """

        validator = self.validator(request_dto)
        if await validator.validate():
            return True
        self.error = validator.get_error(self.i18n)
        return False

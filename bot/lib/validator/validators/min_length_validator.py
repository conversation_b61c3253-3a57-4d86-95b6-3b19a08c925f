import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class MinLengthValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, min_length_value: int, message: str | None = None) -> None:
        super().__init__(value)
        self.__min_length_value = min_length_value
        self.message = message if message else 'must_be_min_length'
        self.format_values = {'min_length_value': self.__min_length_value}

    async def validate(self) -> bool:
        value = self.value()
        if not value:
            return True

        self.is_valid = len(value) >= self.__min_length_value
        return self.is_valid

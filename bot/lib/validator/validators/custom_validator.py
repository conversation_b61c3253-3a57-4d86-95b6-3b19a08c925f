import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=object)

CallBackP1 = typing.Callable[[
    typing.Any
], typing.Awaitable[bool]]

CallBackP2 = typing.Callable[[
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP3 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP4 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP5 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP6 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP7 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP8 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP9 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBackP10 = typing.Callable[[
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any,
    typing.Any
], typing.Awaitable[bool]]

CallBack = CallBackP1 | CallBackP2 | CallBackP3 | CallBackP4 | CallBackP5 | CallBackP6 | CallBackP7 | CallBackP8 | CallBackP9 | CallBackP10


class CustomValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, callback: CallBack, params: list[object] = [], message: str | None = None, format_values: dict[str, typing.Any] | None = None) -> None:
        super().__init__(value)
        self.__callback = callback
        self.__params = params
        self.message = message if message else 'invalid_value'
        self.format_values = format_values

    async def validate(self) -> bool:
        value = self.value()
        self.is_valid = await self.__callback(value, *self.__params) if value else True
        return self.is_valid

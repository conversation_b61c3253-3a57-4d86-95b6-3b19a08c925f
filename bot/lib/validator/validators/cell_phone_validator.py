import re
import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class CellPhoneValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, include_area_code: bool, message: str | None = None) -> None:
        super().__init__(value)
        self.__include_area_code = include_area_code
        self.message = message if message else 'invalid_cell_phone'

    async def validate(self) -> bool:
        value = self.value()
        if self.__include_area_code:
            validate_fields = r'^\+[0-9](\-?[0-9]{1,3})? [0-9]{10}$'
            self.is_valid = re.search(validate_fields, typing.cast(str, value)) is not None if value else True
            return self.is_valid

        validate_fields: str = r'^[0-9]{10}$'
        self.is_valid = re.search(validate_fields, typing.cast(str, value)) is not None if value else True
        return self.is_valid

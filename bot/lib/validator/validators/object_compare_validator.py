import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=typing.Any)


class ObjectCompareValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, compare_object: T, message: str | None = None) -> None:
        super().__init__(value)
        self.__compare_object = compare_object
        self.message = message if message else ''

    async def validate(self) -> bool:
        value = self.value()
        if not value:
            return True

        for field in value:
            self.is_valid = value[field] == self.__compare_object[field]
            if not self.is_valid:
                self.message = 'incorrect_field'
                self.format_values = {'field': field}
                return self.is_valid

        return self.is_valid

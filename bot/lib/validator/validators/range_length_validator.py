import typing

from lib.validator.class_validator import ClassValidator

T = typing.TypeVar('T', bound=str)


class RangeLengthValidator(typing.Generic[T], ClassValidator[T]):

    def __init__(self, value: T, start_number: int, end_number: int, include_limits: bool = False, message: str | None = None) -> None:
        super().__init__(value)
        self.__include_limits = include_limits
        self.__start_number = start_number
        self.__end_number = end_number
        self.message = message if message else ''

    async def validate(self) -> bool:
        value = self.value()
        if not value:
            return True

        if self.__include_limits and len(value) >= self.__start_number and len(value) <= self.__end_number:
            self.is_valid = True
            return self.is_valid

        if len(value) > self.__start_number and len(value) < self.__end_number:
            self.is_valid = True
            return self.is_valid

        self.message = 'must_be_between_length'
        self.format_values = {'start_number': self.__start_number, 'end_number': self.__end_number} if self.__include_limits else {'start_number': self.__start_number+1, 'end_number': self.__end_number-1}
        self.is_valid = False
        return self.is_valid

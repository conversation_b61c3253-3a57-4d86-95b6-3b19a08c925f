import typing
from typing import Any

from lib.validator.class_validator import Class<PERSON><PERSON>da<PERSON>
from lib.validator.class_operator import ClassOperator
from lib.validator.operators import If<PERSON><PERSON><PERSON>, IfNotOperator, StopOperator
from lib.validator.validators import UrlValidator, EmailValidator, IdCardValidator, PhoneValidator, GreaterThanValidator,RequiredValidator, PasswordValidator, LowerThanValidator, GreaterEqualsThanValidator, LowerEqualsThanValidator, RangeValidator, CellPhoneValidator, CustomValidator, RegularExpressionValidator, MaxLengthValidator, ObjectCompareValidator, InValidator, MinLengthValidator, RangeLengthValidator

from lib.validator.validators.custom_validator import CallBack


T = typing.TypeVar('T', bound=typing.Any)


class Validator(typing.Generic[T]):

    def __init__(self, value: T):
        self.value: T = value
        self.is_valid: bool = True
        self.errors: list[str | dict[str, dict[str, Any]]] = []
        self.validators: list[ClassValidator[T] | ClassOperator] = []

    def required(self, message: str | None = None) -> 'Validator':
        validator = RequiredValidator[T](self.value, message)
        self.validators.append(validator)
        return self

    def email(self, message: str | None = None) -> 'Validator':
        validator = EmailValidator[T](self.value, message)
        self.validators.append(validator)
        return self

    def url(self, message: str | None = None) -> 'Validator':
        validator = UrlValidator[T](self.value, message)
        self.validators.append(validator)
        return self

    def id_card(self, message: str | None = None) -> 'Validator':
        validator = IdCardValidator[T](self.value, message)
        self.validators.append(validator)
        return self

    def phone(self, include_area_code: bool = False, message: str | None = None) -> 'Validator':
        validator = PhoneValidator[T](self.value, include_area_code, message)
        self.validators.append(validator)
        return self

    def cell_phone(self, include_area_code: bool = False, message: str | None = None) -> 'Validator':
        validator = CellPhoneValidator[T](self.value, include_area_code, message)
        self.validators.append(validator)
        return self

    def password(self, message: str | None = None, length: int = 8) -> 'Validator':
        validator = PasswordValidator[T](self.value, message, length)
        self.validators.append(validator)
        return self

    def greater_than(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        validator = GreaterThanValidator[T](self.value, compare_value, message)
        self.validators.append(validator)
        return self


    def lower_than(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        validator = LowerThanValidator[T](self.value, compare_value, message)
        self.validators.append(validator)
        return self

    def lower_equals_than(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        validator = LowerEqualsThanValidator[T](self.value, compare_value, message)
        self.validators.append(validator)
        return self

    def greater_equals_than(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        validator = GreaterEqualsThanValidator[T](self.value, compare_value, message)
        self.validators.append(validator)
        return self

    def gt(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        return self.greater_than(compare_value, message)

    def get(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        return self.greater_equals_than(compare_value, message)

    def lt(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        return self.lower_than(compare_value, message)

    def let(self, compare_value: int | float, message: str | None = None) -> 'Validator':
        return self.lower_equals_than(compare_value, message)

    def range(self, start_number: int, end_number: int, include_limits: bool = False, message: str | None = None) -> 'Validator':
        validator = RangeValidator[T](self.value, start_number, end_number, include_limits, message)
        self.validators.append(validator)
        return self

    def range_length(self, start_number: int, end_number: int, include_limits: bool = False, message: str | None = None) -> 'Validator':
        validator = RangeLengthValidator[T](self.value, start_number, end_number, include_limits, message)
        self.validators.append(validator)
        return self

    def custom(self, callback: CallBack, params: list[object] = [], message: str | None = None, format_values: dict[str, Any] | None = None) -> 'Validator':
        validator = CustomValidator[T](self.value, callback, params, message, format_values)
        self.validators.append(validator)
        return self

    def regular_expression(self, reg_expression: str, message: str | None = None) -> 'Validator':
        validator = RegularExpressionValidator[T](self.value, reg_expression, message)
        self.validators.append(validator)
        return self

    def max_length(self, max_length_value: int, message: str | None = None) -> 'Validator':
        validator = MaxLengthValidator[T](self.value, max_length_value, message)
        self.validators.append(validator)
        return self

    def min_length(self, min_length_value: int, message: str | None = None) -> 'Validator':
        validator = MinLengthValidator[T](self.value, min_length_value, message)
        self.validators.append(validator)
        return self

    def object_compare(self, compare_object: typing.Any, message: str | None = None) -> 'Validator':
        validator = ObjectCompareValidator[T](self.value, compare_object, message)
        self.validators.append(validator)
        return self

    def _in(self, data: list[object], message: str | None = None) -> 'Validator':
        validator = InValidator[T](self.value, data, message)
        self.validators.append(validator)
        return self

    def _if(self, eval_condition: bool | typing.Callable[[object], bool]) ->'Validator':
        validator = IfOperator(eval_condition)
        self.validators.append(validator)
        return self

    def if_not(self, eval_condition: bool | typing.Callable[[object], bool]) -> 'Validator':
        validator = IfNotOperator(eval_condition)
        self.validators.append(validator)
        return self

    def stop(self) -> 'Validator':
        validator = StopOperator()
        self.validators.append(validator)
        return self

    def add_validator(self, validator: ClassValidator) -> None:
        self.validators.append(validator)

    async def validate(self) -> bool:
        self.is_valid = True
        self.errors = []

        for class_validator in self.validators:
            if isinstance(class_validator, ClassOperator):
                if not self.is_valid and isinstance(class_validator, StopOperator):
                    break
                if not await class_validator.validate():
                    break
                continue

            if not await class_validator.validate():
                self.is_valid = False
                if class_validator.format_values:
                    self.errors.append({class_validator.message: class_validator.format_values})
                else:
                    self.errors.append(class_validator.message)

        return self.is_valid

from typing import Any


class ErrorValidator:
    errors: dict[str, list[str | dict[str, dict[str, Any]]]]

    def __init__(self) -> None:
        self.errors = {}

    def clear(self):
        self.errors = {}

    def add(self, key: str, error: str | dict[str, dict[str, Any]] | list[str | dict[str, dict[str, Any]]]):
        if key not in self.errors:
            self.errors[key] = []

        if isinstance(error, str) or isinstance(error, dict):
            self.errors[key].append(error)
        else:
            for item in error:
                self.errors[key].append(item)

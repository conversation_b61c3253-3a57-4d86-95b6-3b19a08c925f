import typing

from abc import ABC, abstractmethod


T = typing.TypeVar('T', bound=typing.Any)


class ClassValidator(typing.Generic[T], ABC):
    def __init__(self, value: T):
        self.__value: T | typing.Callable[[], T] = value
        self.is_valid: bool = True
        self.message: str = ''
        self.format_values: dict[str, typing.Any] = {}

    def value(self) -> T:
        return self.__value() if callable(self.__value) else self.__value

    @abstractmethod
    async def validate(self) -> bool:
        pass

import os
import json

from typing import Literal
from logging import getLogger
from logging.config import dictConfig
from uvicorn.logging import DefaultFormatter


class LoggerFormatter(DefaultFormatter):
    """Custom formatter for logger."""

    def __init__(
        self,
        fmt: str | None = None,
        datefmt: str | None = None,
        style: Literal['%'] | Literal['{'] | Literal['$'] = '%',
        use_colors: bool | None = None,
    ) -> None:
        fmt = '[%(asctime)s] %(levelprefix)s %(message)s'
        datefmt = '%Y-%m-%d %H:%M:%S'
        super().__init__(fmt, datefmt, style, use_colors)


class LoggerConfig:
    """Provides a `set_conf` function
    to set a logger configuration
    from a specific file.
    """

    @staticmethod
    def set_conf(logs_file_name: str, logs_folder_path: str = '') -> None:
        """Sets the logger configuration
        from the specified conf file in
        the server config.

        Parameters
        ----------
        logs_file_name : str
            Log file name.
        logs_folder_path : str, optional
            Log file folder path, by default ''.

        Raises
        ------
        FileNotFoundError
            Conf file not found.
        Exception
            Generic error.
        """

        conf_file = 'logger_conf.json'
        if not os.path.exists(conf_file):
            raise FileNotFoundError('Logger conf file "{}" not found.'.format(conf_file))

        if not os.path.exists(logs_folder_path):
            os.makedirs(logs_folder_path)
        logs_path = os.path.join(logs_folder_path, logs_file_name)

        try:
            with open(conf_file, 'r') as f:
                config = json.loads(f.read())
                config['handlers']['fileHandler']['filename'] = logs_path
                dictConfig(config)
        except Exception as e:
            raise Exception('Logger conf file could not be read: {}'.format(str(e)))


logger = getLogger('uvicorn')

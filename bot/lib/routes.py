import functools
import inspect
from collections.abc import Callable
from inspect import signature
from uuid import uuid4

from pydantic import ValidationError
from starlette._utils import is_async_callable
from starlette.concurrency import run_in_threadpool
from starlette.requests import ClientDisconnect
from starlette.responses import Response
from starlette.routing import Route as StarletteRoute
from starlette.routing import compile_path, get_name
from starlette.types import <PERSON><PERSON>App, Receive, Scope, Send

from lib.base_controller import BaseController, Request
from lib.base_error import BaseError
from lib.injectable import injectable
from lib.logger import logger
from lib.responses import ErrorResponse
from lib.validator.error_validator import ErrorValidator


async def get_request_params(request: Request, func: Callable) -> list:
    func_params = signature(func).parameters
    request_params = []

    for param in func_params.values():
        if param.name == 'self':
            continue
        if param.name in request.path_params:
            request_params.append(request.path_params[param.name])
        elif injectable.has(param.annotation):
            instance = injectable.get(param.annotation, request.state.id)
            if instance:
                request_params.append(instance)
            else:
                logger.error(
                    f'No se encontró la instancia de {param.annotation}.'
                )
        else:
            content = await request.json()
            data = param.annotation(**content)
            request_params.append(data)

    return request_params


async def call_endpoint(
    request: Request, class_: type[BaseController], func: Callable
) -> Response:
    controller = class_(request, injectable)
    controller.prev()
    endpoint = getattr(controller, func.__name__)
    request_params = await get_request_params(request, func)
    if is_async_callable(func):
        return await endpoint(*request_params)
    return await run_in_threadpool(endpoint, *request_params)


def request_response(cls: type[BaseController], func: Callable) -> ASGIApp:
    async def app(scope: Scope, receive: Receive, send: Send) -> None:
        request = Request(scope, receive=receive, send=send)
        request.state.id = str(uuid4())
        error_validator = ErrorValidator()
        try:
            response = await call_endpoint(request, cls, func)
        except ValidationError as e:
            for error in e.errors():
                error_validator.add(str(error['loc'][0]), error['msg'])
            response = ErrorResponse(error_validator)
        except BaseError as e:
            error_validator.add('common', str(e))
            response = ErrorResponse(error_validator, e.code)
        except Exception as e:
            logger.critical(e, exc_info=True)
            error_validator.add('common', 'Ha ocurrido un error desconocido.')
            response = ErrorResponse(error_validator)
        finally:
            injectable.clear_scoped_objects(request.state.id)
        await response(scope, receive, send)

    return app


class Route(StarletteRoute):
    def __init__(
        self,
        path: str,
        cls: type[BaseController],
        endpoint: Callable,
        *,
        methods: list[str] | None = None,
        name: str | None = None,
        include_in_schema: bool = True,
    ) -> None:
        assert path.startswith('/'), "Routed paths must start with '/'"
        self.path = path
        self.endpoint = endpoint
        self.name = get_name(endpoint) if name is None else name
        self.include_in_schema = include_in_schema

        try:
            endpoint_handler = endpoint
            while isinstance(endpoint_handler, functools.partial):
                endpoint_handler = endpoint_handler.func
            if inspect.isfunction(endpoint_handler) or inspect.ismethod(
                endpoint_handler
            ):
                # Endpoint is function or method. Treat it as `func(request) -> response`.
                self.app = request_response(cls, endpoint)
                if methods is None:
                    methods = ['GET']
            else:
                # Endpoint is a class. Treat it as ASGI.
                self.app = endpoint

            if methods is None:
                self.methods = None
            else:
                self.methods = {method.upper() for method in methods}
                if 'GET' in self.methods:
                    self.methods.add('HEAD')

            self.path_regex, self.path_format, self.param_convertors = (
                compile_path(path)
            )

        except ClientDisconnect:
            logger.error('Client disconnected.')


routes: list[Route] = []

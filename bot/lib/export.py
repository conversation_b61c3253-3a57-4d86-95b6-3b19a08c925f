import json
import typing
import csv
from csv import DictWriter
from openpyxl import Workbook
from openpyxl.worksheet.worksheet import Worksheet
from tempfile import NamedTemporaryFile

from i18n import I18N
from lib.base_dto import BaseResponseDto
from lib.base_export import File, BaseExport
from lib.exceptions import BadRequestError, NotFoundError


T = typing.TypeVar('T', bound=BaseResponseDto, covariant=True)


class ExportData(typing.Generic[T], BaseExport):
    """Exports a list of DTOs to CSV, JSON and XLSX."""

    __data: list[T]
    __csv_writer: DictWriter
    __excel_workbook: Workbook
    __excel_worksheet: Worksheet
    __translation: dict[str, str] | None = None

    def __init__(self, i18n: I18N) -> None:
        """Creates a dependency object of this module.

        Parameters
        ----------
        i18n : I18N
            Injected dependency of the i18n translator module.
        """

        self.i18n = i18n

    def __get_columns(self) -> list[str]:
        """Gets columns names from the DTOs structure.

        Returns
        -------
        list[str]
            Columns.
        """

        return [column_name for column_name in self.__data[0].to_response().keys()]

    def __get_dto_value(self, value: object) -> object:
        """Serializes value to JSON if its type is a primitive datatype.

        Parameters
        ----------
        value : object
            DTO field value.

        Returns
        -------
        object
            DTO field value serialized.
        """

        if isinstance(value, (dict, list, tuple, set)):
            return json.dumps(value, ensure_ascii=False)
        return value

    def __write_csv_data(self) -> None:
        """Writes the DTOs to a CSV writer."""

        self.__csv_writer.writeheader()
        for dto in self.__data:
            self.__csv_writer.writerow(dto.to_response())

    def __write_xlsx_data(self) -> None:
        """Appends the DTOs to a Excel worksheet."""

        self.__excel_workbook = Workbook()
        self.__excel_worksheet = typing.cast(Worksheet, self.__excel_workbook.active)
        columns = self.__get_columns()
        self.__excel_worksheet.append(columns)
        for dto in self.__data:
            dto_dict = dto.to_response()
            self.__excel_worksheet.append([self.__get_dto_value(dto_dict[col]) for col in columns])

        # Translate column names
        if self.__translation:
            for col in self.__excel_worksheet.iter_cols():
                col[0].value = self.i18n(self.__translation[str(col[0].value)])

    def __to_json(self) -> bytes:
        """Writes the DTOs to a JSON file.

        Returns
        -------
        bytes
            JSON file content stream
        """

        stream = b''
        with NamedTemporaryFile(mode='w+') as json_file:
            dtos = [dto.to_response() for dto in self.__data]
            json.dump(dtos, json_file, indent=4, ensure_ascii=False)
            json_file.seek(0)
            stream = json_file.read().encode('utf-8')
        return stream

    def __to_csv(self) -> bytes:
        """Writes the DTOs to a CSV file.

        Returns
        -------
        bytes
            CSV file content stream
        """

        stream = b''
        with NamedTemporaryFile(mode='w+') as csv_file:
            self.__csv_writer = csv.DictWriter(csv_file, fieldnames=self.__get_columns())
            self.__write_csv_data()
            csv_file.seek(0)
            stream = csv_file.read().encode('utf-8')
        return stream

    def __to_xlsx(self) -> bytes:
        """Writes the DTOs to a XLSX file.

        Returns
        -------
        bytes
            XLSX file content stream.
        """

        stream = b''
        with NamedTemporaryFile() as xlsx_file:
            self.__write_xlsx_data()
            self.__excel_workbook.save(xlsx_file.name)
            xlsx_file.seek(0)
            stream = xlsx_file.read()
        return stream

    def __create_file(self) -> File:
        """Creates a file with the DTOs to export
        and returns a file content stream with the data.

        Returns
        -------
        File
            File descriptor with the exported data and its metadata.

        Raises
        ------
        BadRequestError
            File could not be written.
        """

        stream = b''
        if self._extension == 'xlsx':
            stream = self.__to_xlsx()
        elif self._extension == 'json':
            stream = self.__to_json()
        else:
            stream = self.__to_csv()

        if not stream:
            raise BadRequestError(self.i18n('could_not_write_file'))

        return File(
            stream=stream,
            filename=self._get_full_filename(),
            media_type=self._get_media_type_from_extension(),
            headers=self._get_headers(full_filename=True)
        )

    def to_file(self, data: list[T], filename: str, extension: str = 'csv', translation: dict[str, str] | None = None) -> File:
        """Exports DTOs to a CSV, JSON or XLSX file.

        Parameters
        ----------
        data : list[T]
            DTOs to export.
        filename : str
            Output file name.
        extension : str, optional
            Output file extension, by default 'csv'.
        translation : dict[str, str] | None, optional
            Columns translations dict, by default None.

        Returns
        -------
        File
            File descriptor with the exported data and its metadata.

        Raises
        ------
        NotFoundError
            No data available to export.
        BadRequestError
            File could not be written.
        BadRequestError
            An unknown error has occurred.
        """

        try:
            self.__data = data
            self._filename = filename
            self._extension = extension
            self.__translation = translation
            return self.__create_file()

        except IndexError:
            raise NotFoundError(self.i18n('no_data_available'))

        except ValueError:
            raise BadRequestError(self.i18n('could_not_write_data'))

        except Exception as error:
            raise BadRequestError(self.i18n('an_error_has_occurred', format_values={'error': str(error)}))

    def stream_to_file(self, stream: bytes, filename: str, extension: str) -> File:
        """Generates a file from a stream.

        Parameters
        ----------
        stream : bytes
            Stream.
        filename : str
            Output file name.
        extension : str
            Output file extension.

        Returns
        -------
        File
            File descriptor with the stream and its metadata.
        """

        self._filename = filename
        self._extension = extension
        return File(
            stream=stream,
            filename=self._get_full_filename(),
            media_type=self._get_media_type_from_extension(),
            headers=self._get_headers(full_filename=True))

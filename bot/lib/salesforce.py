from typing import Any, Callable, cast

from simple_salesforce.api import Salesforce as SF
from simple_salesforce.exceptions import (
    SalesforceAuthenticationFailed,
    SalesforceError,
    SalesforceMalformedRequest,
)
from simple_salesforce.format import format_soql

from lib.base_injectable import BaseInjectable
from lib.exceptions import AutomationError, BadQueryError
from lib.http.sessions import TLSSession


class Salesforce(BaseInjectable):
    """Salesforce API client."""

    def connect(
        self,
        domain: str = 'login',
        username: str | None = None,
        password: str | None = None,
        security_token: str | None = None,
        client_id: str | None = None,
        client_secret: str | None = None,
    ) -> None:
        """Create a connection instance to Salesforce.

        Parameters
        ----------
        domain : str, optional
            "login" for prod environment and "test"
            for testing environment, by default 'login'.
        username : str | None, optional
            Connection username, by default None.
        password : str | None, optional
            Connection password, by default None.
        security_token : str | None, optional
            Security token of the connection user, by default None.
        client_id : str | None, optional
            OAuth application client ID, by default None.
        client_secret : str | None, optional
            OAuth application client secret, by default None.

        """
        try:
            self.conn = SF(
                domain=domain,
                username=username,
                password=password,
                security_token=security_token,
                consumer_key=client_id,
                consumer_secret=client_secret,
                session=TLSSession(),
            )
        except SalesforceAuthenticationFailed as e:
            raise AutomationError(
                message='No se pudo iniciar sesión en la API',
                detail=str(e),
            ) from e

    def run_query(self, query: str, *params: Any) -> Any:
        """Run a query.

        Parameters
        ----------
        query : str
            SOQL query.

        Returns
        -------
        Any
            Result of the query.

        Raises
        ------
        BadQueryError
            If a query error occurred.

        """
        try:
            query = format_soql(query, *params)
            return self.conn.query(query).get('records')
        except SalesforceMalformedRequest as e:
            error_message = self.get_error_message(e)
            raise BadQueryError(error_message)

    def update_user(self, user_id: str, fields: dict[str, Any]) -> Any:
        """Update an user.

        Parameters
        ----------
        user_id : str
            User ID.
        fields : dict[str, Any]
            Fields to be updated.

        Returns
        -------
        Any
            Result of the update.

        Raises
        ------
        BadQueryError
            If a query error occurred.

        """
        try:
            return cast(Callable, self.conn.User.update)(user_id, fields)
        except SalesforceMalformedRequest as e:
            error_message = self.get_error_message(e)
            raise BadQueryError(error_message)

    def get_user_login_by_user_id(self, user_id: str) -> Any:
        """Get the user login by a user ID.

        Parameters
        ----------
        user_id : str
            User ID.

        Returns
        -------
        Any
            User login.

        """
        query = 'SELECT Id, IsFrozen FROM UserLogin WHERE UserId = {} LIMIT 2'
        records = self.run_query(query, user_id)
        return records[0] if records else None

    def freeze_user(self, user_id: str) -> Any:
        """Freeze a user.

        Parameters
        ----------
        user_id : str
            User ID.

        Returns
        -------
        Any
            Operation result.

        Raises
        ------
        BadQueryError
            If a query error occurred.

        """
        try:
            user_login = self.get_user_login_by_user_id(user_id)
            if not user_login:
                return None

            return cast(Callable, self.conn.UserLogin.update)(
                user_login['Id'],
                {'IsFrozen': True},
            )
        except SalesforceMalformedRequest as e:
            error_message = self.get_error_message(e)
            raise BadQueryError(error_message)

    def get_error_message(self, exception: SalesforceError) -> str:
        """Get the error message from a Salesforce exception.

        Parameters
        ----------
        exception : SalesforceError
            Thrown exception.

        Returns
        -------
        str
            Error message.

        """
        try:
            return cast(dict, exception.content[0]).get(
                'message', str(exception)
            )
        except Exception:
            return str(exception)

    def close(self) -> None:
        """Close the session if opened."""
        try:
            if hasattr(self, 'conn'):
                self.conn.session.close()
        except Exception:
            return None

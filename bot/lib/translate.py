import typing

from lib.base_model import BaseModel
from lib.base_injectable import BaseInjectable
from i18n import I18N


TranslateData = dict[str, dict[str, str]]


T = typing.TypeVar('T', bound=BaseModel)


class Translate(typing.Generic[T], BaseInjectable):
    """Creates aggregation expressions to translate
    fields with many possible values."""

    def __init__(self, i18n: I18N) -> None:
        """Creates a dependency object of this module.

        Parameters
        ----------
        i18n : I18N
            Injected dependency of the i18n translator module.
        """

        self.i18n = i18n

    def create(self, data: TranslateData) -> dict[str, dict[str, typing.Any]]:
        """Creates translation expressions.

        Parameters
        ----------
        data : TranslateData
            Dict of translations.

            Example::

            {
                "foo": {
                    "<value>": "<i18n message>",
                    "<value>": "<i18n message>",
                    ...
                },
                "bar": {
                    "<value>": "<i18n message>",
                    "<value>": "<i18n message>",
                    ...
                }
                ...
            }

        Returns
        -------
        dict[str, dict[str, typing.Any]]
            _description_
        """

        translation_query = {'$addFields': {}}

        for field, translation in data.items():
            translation_query['$addFields'][field] = {'$switch': {'branches': [], 'default': f'${field}'}}

            for current_value, new_value in translation.items():
                translation_query['$addFields'][field]['$switch']['branches'].append({'case': {'$eq': [ f'${field}', current_value ]}, 'then': self.i18n(new_value)})

        return translation_query

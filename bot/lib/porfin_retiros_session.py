from lib import config
from lib.http.sessions import TLSSession
from lib.porfin_session import PorfinCredentials, PorfinSession


class PorfinRetirosSession(PorfinSession):
    """Create a connection instance to <PERSON>rf<PERSON>
    using the Bot Retiros user.
    """

    def create_session(self) -> None:
        self.session = TLSSession(timeout=(20, 180))

    def get_credentials(self):
        return PorfinCredentials(
            username=str(config.PORFIN_RETIROS_USER),
            password=str(config.PORFIN_RETIROS_PASSWORD),
        )

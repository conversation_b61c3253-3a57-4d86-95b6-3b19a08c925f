from typing import Literal, cast
from beanie import PydanticObjectId

from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService
from lib.filter import Filter, FormatFilter
from lib.query import Query
from lib.pagination import Pagination
from lib.export import ExportData, File

from models.log import Log
from dtos.log_dto import LogResponseDto, LogRequestDto, ExportLogResponseDto


class LogService(BaseInjectable, BaseService):
    """Service to create, list and export logs."""

    __filter: Filter[Log]
    __query: Query[Log]
    __export_data: ExportData[ExportLogResponseDto]

    def __init__(self, export_data: ExportData, _filter: Filter) -> None:
        """Creates a dependency object of this service.

        Parameters
        ----------
        export_data : ExportData
            Injected dependency of the exportation module.
        logs_exporter : LogsExporter
            Injected dependency of the logs exportation module.
        _filter : Filter
            Injected dependency of the mongo filters module.
        """

        super().__init__()
        self.__export_data = export_data
        self.__filter = _filter

    async def export(self, extension: Literal['csv', 'xlsx', 'json'], filename: str, start_index: int, limit: int, filter_data: FormatFilter | None = None) -> File:
        """Exports the logs.

        Parameters
        ----------
        extension : Literal['xlsx', 'json']
            File's extension.
        filename : str
            Filename.
        start_index : int
            Index of the log from which to export.
        limit : int
            Number of logs to export.
        filter_data : FormatFilter | None, optional
            Dict of mongo search expressions to filter the logs before exporting them, by default None.

        Returns
        -------
        File
            File object with the exported logs.
        """

        query = self.__query.create(Log, Log.isDeleted == False, start_index=start_index, limit=limit, format_filter=filter_data, projection_model=ExportLogResponseDto, sort='-createdAt')  # noqa: E712
        return self.__export_data.to_file(cast(list[ExportLogResponseDto], await query.to_list()), filename, extension, Log.TranslationConfig.columns)

    async def list(self, page: int, rows_per_page: int, filter_data: FormatFilter | None = None) -> Pagination[LogResponseDto]:
        """Gets the list of logs.

        Parameters
        ----------
        page : int
            Current page.
        rows_per_page : int
            Rows per page.
        filter_data : FormatFilter | None, optional
            Dict of mongo search expressions to filter the logs, by default None.

        Returns
        -------
        Pagination[LogResponseDto]
            Paginated list of logs.
        """

        query = Log.find(Log.isDeleted == False, fetch_links=True)  # noqa: E712

        if filter_data is not None:
            query = self.__filter.create(filter_data, query, fetch_link=True)

        pagination = Pagination[LogResponseDto](page, rows_per_page, await query.count())
        pagination.data = LogResponseDto.from_orm_many(
            await query.skip(pagination.skip).limit(pagination.rows_per_page).sort('-createdAt').to_list()
        )
        return pagination

    async def create(self, log_dto: LogRequestDto) -> LogResponseDto:
        """Creates a log.

        Parameters
        ----------
        log_dto : LogRequestDto
            Log's data

        Returns
        -------
        LogResponseDto
            Created log.
        """

        new_log: Log = Log(**log_dto.to_dict())
        return LogResponseDto.from_orm(await new_log.create())

    async def retrieve(self, id: str) -> LogResponseDto | None:
        """Retrieves a log.

        Parameters
        ----------
        id : str
            Log id.

        Returns
        -------
        LogResponseDto | None
            Log data if found.
        """

        log = await Log.get(PydanticObjectId(id))
        if log is None or log.isDeleted:
            return None
        return LogResponseDto.from_orm(log)

import asyncio
import json
import time
from dataclasses import dataclass
from datetime import datetime
from inspect import iscoroutinefunction, ismethod, signature
from typing import Callable, Literal, cast

from beanie import PydanticObjectId
from requests.exceptions import ConnectionError, Timeout
from starlette.concurrency import run_in_threadpool

from dtos.automation_dto import (
    ApplicationDataResponseDto,
    ApplicationResultResponseDto,
    ConsultedUserDataResponseDto,
    RemoveUserDataRequestDto,
    RemoveUserRequestDto,
    RemoveUsersRequestDto,
)
from dtos.log_dto import LogRequestDto
from dtos.idm_log_dto import IDMLogRequestDto
from i18n import I18N
from ldap3.core.exceptions import LDAPSocketOpenError
from lib import config
from lib.active_directory import ActiveDirectory, ADUser
from lib.base_injectable import BaseInjectable
from lib.base_module import BaseModule
from lib.base_service import BaseService
from lib.exceptions import AutomationError, NotFoundError
from lib.idm_client import IDMClient
from lib.logger import logger
from lib.results_exporter import ResultsExporter
from models import ConsultedUser, DailyConsolidated, RemovedUser, Transaction
from modules import (
    AgendaWebModule,
    AribaModule,
    BandejaEscalamientoModule,
    BeyondHealthModule,
    BillingCenterModule,
    CAServiceDeskModule,
    CaseTrackingModule,
    ClaimCenterModule,
    ConfluenceModule,
    ContactManagerModule,
    ConveniosModule,
    EventosAdversosModule,
    HealthCloudModule,
    IDMModule,
    IntegradorModule,
    IPSAModule,
    OfficeModule,
    OHIModule,
    OIPAModule,
    PolicyCenterModule,
    PorfinModule,
    SalesforceModule,
    SaludWebModule,
    SEEEModule,
    SEUSModule,
    SOATModule,
    STARModule,
    TablaTercerosModule,
    ViafirmaModule,
)
from services.consulted_user_service import ConsultedUserService
from services.idm_log_service import IDMLogService
from services.log_service import LogService
from services.removed_user_service import RemovedUserService
from services.transaction_service import TransactionService


@dataclass
class ADResult:
    time: float
    ad_user: ADUser | None
    data: ApplicationResultResponseDto


@dataclass
class Module:
    name: str
    func: Callable[..., ApplicationDataResponseDto]


@dataclass
class AutomationFunction:
    name: str
    func: Callable
    args: list[object]
    ignored: bool = False
    warning: str | None = None


@dataclass
class AutomationFunctionResultsStore:
    results: list[dict[str, object]]
    total_time: float
    error_count: int
    warning_count: int


class AutomationService(BaseInjectable, BaseService):
    """Service to consult users on multiple applications."""

    def __init__(
        self,
        i18n: I18N,
        idm_client: IDMClient,
        results_exporter: ResultsExporter,
        consulted_user_service: ConsultedUserService,
        removed_user_service: RemovedUserService,
        transaction_service: TransactionService,
        log_service: LogService,
        idm_log_service: IDMLogService,
        active_directory: ActiveDirectory,
        agenda_web_module: AgendaWebModule,
        ariba_module: AribaModule,
        bandeja_escalamiento_module: BandejaEscalamientoModule,
        beyond_health_module: BeyondHealthModule,
        billing_center_module: BillingCenterModule,
        ca_service_desk_module: CAServiceDeskModule,
        case_tracking_module: CaseTrackingModule,
        claim_center_module: ClaimCenterModule,
        confluence_module: ConfluenceModule,
        contact_manager_module: ContactManagerModule,
        convenios_module: ConveniosModule,
        eventos_adversos_module: EventosAdversosModule,
        health_cloud_module: HealthCloudModule,
        idm_module: IDMModule,
        integrador_module: IntegradorModule,
        ipsa_module: IPSAModule,
        office_module: OfficeModule,
        ohi_module: OHIModule,
        oipa_module: OIPAModule,
        policy_center_module: PolicyCenterModule,
        porfin_module: PorfinModule,
        salesforce_module: SalesforceModule,
        salud_web_module: SaludWebModule,
        seee_module: SEEEModule,
        seus_module: SEUSModule,
        soat_module: SOATModule,
        star_module: STARModule,
        tabla_terceros_module: TablaTercerosModule,
        viafirma_module: ViafirmaModule,
    ) -> None:
        super().__init__()
        self.i18n = i18n
        self.idm_client = idm_client
        self.results_exporter = results_exporter
        self.consulted_user_service = consulted_user_service
        self.removed_user_service = removed_user_service
        self.transaction_service = transaction_service
        self.log_service = log_service
        self.idm_log_service = idm_log_service
        self.active_directory = active_directory

        self._consult_user_modules = [
            agenda_web_module,
            ariba_module,
            bandeja_escalamiento_module,
            beyond_health_module,
            billing_center_module,
            ca_service_desk_module,
            case_tracking_module,
            claim_center_module,
            confluence_module,
            contact_manager_module,
            convenios_module,
            eventos_adversos_module,
            health_cloud_module,
            idm_module,
            integrador_module,
            ipsa_module,
            office_module,
            ohi_module,
            oipa_module,
            policy_center_module,
            porfin_module,
            salesforce_module,
            salud_web_module,
            seee_module,
            seus_module,
            soat_module,
            star_module,
            tabla_terceros_module,
            viafirma_module,
        ]

        self._remove_user_modules = [
            agenda_web_module,
            beyond_health_module,
            contact_manager_module,
            convenios_module,
            eventos_adversos_module,
            health_cloud_module,
            integrador_module,
            ipsa_module,
            ohi_module,
            oipa_module,
            porfin_module,
            salesforce_module,
            salud_web_module,
            seus_module,
            tabla_terceros_module,
        ]

        self._remove_user_module_names = self._get_module_names_in_list(
            self._remove_user_modules
        )

        self._lab_modules = [
            health_cloud_module,
            salesforce_module,
        ]

        self._lab_module_names = self._get_module_names_in_list(
            self._lab_modules
        )

    def _get_module_names_in_list(
        self, list_of_modules: list[BaseModule | Module]
    ) -> list[str]:
        """Gets the list of names of the automation modules
        in the passed list.
        """
        modules = []
        for module in list_of_modules:
            if isinstance(module, BaseModule):
                modules.append(str(module))
            else:
                modules.append(module.name)
        return modules

    def _build_user_data_dict(
        self, username: str, ad_user: ADUser | None
    ) -> dict[str, str]:
        """Builds a dict with the username and
        Active Directory user data.

        Parameters
        ----------
        username : str
            Username.
        ad_user : ADUser | None
            Active Directory user data.

        Returns
        -------
        dict[str, str]
            User data dict.
        """

        return {
            'username': username,
            'document': ad_user.document if ad_user else '',
            'fullname': ad_user.fullname if ad_user else '',
            'first_name': ad_user.first_name if ad_user else '',
            'last_name': ad_user.last_name if ad_user else '',
            'user_principal_name': ad_user.user_principal_name
            if ad_user
            else '',
            'email': ad_user.mail if ad_user else '',
        }

    def _build_missing_param_warning_message(
        self,
        action: Literal['consult', 'remove'],
        param_name: str,
        ad_user: ADUser | None,
    ) -> str:
        """Builds the missing param warning message.

        Parameters
        ----------
        action: Literal['consult', 'remove']
            Action to be performed.
        param_name : str
            Param name.
        ad_user : ADUser | None
            Active Directory user data.

        Returns
        -------
        str
            Message.
        """
        if not ad_user:
            message = (
                'El usuario no fue {} en esta aplicación debido a que no se'
                ' encontró en Directorio Activo y no se pudo extraer el dato'
                ' "{}" requerido por esta aplicación.'
            )
        else:
            message = (
                'El usuario no fue {} en esta aplicación debido a que el dato'
                ' "{}" del usuario en Directorio Activo se encuentra vacío y'
                ' es requerido por esta aplicación.'
            )
            if not ad_user.active:
                message += (
                    ' Tenga en cuenta que este usuario se encuentra'
                    ' deshabilitado en el Directorio Activo.'
                )
        return message.format(
            'consultado' if action == 'consult' else 'retirado',
            self.i18n(param_name),
        )

    def _get_automation_function(
        self,
        action: Literal['consult', 'remove'],
        user_data: dict[str, str],
        name: str,
        func: Callable,
        ad_user: ADUser | None,
    ) -> AutomationFunction:
        """Gets an automation function.

        Parameters
        ----------
        action: Literal['consult', 'remove']
            Action to be performed.
        user_data : dict[str, str]
            User data.
        name : str
            Name of the module or the function.
        func : Callable
            Function.
        ad_user : ADUser | None
            Active Directory data found for the user.

        Returns
        -------
        AutomationFunction
            Automation function.
        """

        params = signature(func).parameters
        args: list[object] = []
        is_valid = True
        warning = None
        for param in params.values():
            if param.name == 'self':
                continue
            if param.name == 'catalog_number':
                args.append(user_data.get('catalog_number', ''))
                continue
            param_value = user_data.get(param.name)
            if not param_value:
                is_valid = False
                warning = self._build_missing_param_warning_message(
                    action, param.name, ad_user
                )
                break
            args.append(param_value)
        ignored = not is_valid
        return AutomationFunction(
            name=name, func=func, args=args, ignored=ignored, warning=warning
        )

    def _get_automation_function_list(
        self,
        username: str,
        action: Literal['consult', 'remove'],
        ad_user: ADUser | None,
        catalog_number: str = '',
        selected_functions_or_modules: list[str] = [],
    ) -> list[AutomationFunction]:
        """Gets the automation functions to remove users
        and the `args` required by each one.

        Parameters
        ----------
        username : str
            Username of the user to consult.
        action : Literal['consult', 'remove']
            Action to be performed.
        ad_user : ADUser | None
            Active Directory data found for the user.
        catalog_number : str, optional
            Catalog number, by default '.
        selected_functions_or_modules : list[str], optional
            Selected functions or modules, by default [].

        Returns
        -------
        list[AutomationFunction]
            Automation functions.
        """
        user_data = self._build_user_data_dict(username, ad_user)
        if action == 'remove':
            user_data.setdefault('catalog_number', catalog_number)

        func_name = 'consult_user' if action == 'consult' else 'remove_user'
        modules = (
            self._consult_user_modules
            if action == 'consult'
            else self._remove_user_modules
        )

        automation_functions = []
        for module in modules:
            name = str(module)
            if config.LAB_ENV and name not in self._lab_module_names:
                continue

            if (
                selected_functions_or_modules
                and name not in selected_functions_or_modules
            ):
                continue

            func = getattr(module, func_name, None)
            if not func or not ismethod(func):
                continue

            automation_function = self._get_automation_function(
                action=action,
                user_data=user_data,
                name=name,
                func=func,
                ad_user=ad_user,
            )
            automation_functions.append(automation_function)

        return automation_functions

    def _get_ad_result(
        self,
        data: ADUser | None = None,
        error: str | None = None,
        error_detail: str | None = None,
        user_not_found: bool = False,
    ) -> ApplicationResultResponseDto:
        """Converts `ADUser` instance to
        an automation result.

        Parameters
        ----------
        data : ADUser | None, optional
            ADUser if found, by default None.
        error : str | None, optional
            Error message, by default None.
        error_detail : str | None, optional
            Error detail, by default None.
        user_not_found : bool | None, optional
            Specifies if user was not found, by default False.

        Returns
        -------
        ApplicationResultResponseDto
            AD result.
        """

        return ApplicationResultResponseDto(
            application='activeDirectory',
            applicationName=self.i18n('activeDirectory'),
            data=data,
            error=error,
            errorDetail=error_detail,
            userNotFound=user_not_found,
            hasPrivileges=data.has_privileges if data else False,
        )

    def _build_logger_message(
        self, transaction: Transaction, username: str | None = None
    ) -> str:
        """Builds the logger message.

        Parameters
        ----------
        transaction : Transaction
            Transaction.
        username : str | None, optional
            Consulted or removed username, by default None.

        Returns
        -------
        str
            Message.
        """

        data = {
            'transaction': str(transaction.id) if transaction.id else None,
            'createdBy': transaction.createdBy,
            'progress': transaction.progress,
            'state': transaction.state,
            'action': transaction.action,
            'username': username,
            'message': transaction.message,
        }
        return json.dumps(data)

    async def _update_transaction(
        self,
        transaction: Transaction,
        message: str | None = None,
        state: Literal['progress', 'done', 'error'] | None = None,
        progress: float | None = None,
        consulted_user: ConsultedUser | None = None,
        removed_user: RemovedUser | None = None,
    ) -> None:
        """Updates a transaction

        Parameters
        ----------
        transaction : Transaction
            Transaction.
        message : str | None, optional
            Transaction message, by default None.
        state : Literal['progress', 'done', 'error'] | None, optional
            State of the transaction, by default None.
        progress : float | None, optional
            Operations counter to update the `progress` field
            of the transaction, by default None.
        consulted_user : ConsultedUser | None, optional
            Consulted user to be added to the transaction, by default None.
        removed_user : RemovedUser | None, optional
            Removed user to be added to the transaction, by default None.
        """

        await self.transaction_service.update_transaction(
            transaction=transaction,
            message=message,
            state=state,
            progress=progress,
            consulted_user=consulted_user,
            removed_user=removed_user,
        )
        if message:
            if consulted_user:
                username = consulted_user.username
            elif removed_user:
                username = removed_user.username
            else:
                username = None
            logger_message = self._build_logger_message(transaction, username)
            logger.error(logger_message) if state == 'error' else logger.info(
                logger_message
            )

    async def _update_transaction_found_consulted_user(
        self,
        transaction: Transaction,
        consulted_user: ConsultedUser,
        progress: float,
        username: str,
    ) -> None:
        """Updates the transaction with
        a found consulted user.

        Parameters
        ----------
        transaction : Transaction
            Transaction.
        consulted_user : ConsultedUser
            Consulted user.
        progress : float
            Operations counter to update the `progress` field
            of the transaction.
        username : str
            Username of the consulted user.
        """

        message = (
            'Se retorna la información del usuario {} obtenida el {}.'.format(
                username, str(consulted_user.exp)
            )
        )
        await self._update_transaction(
            transaction=transaction,
            message=message,
            state='done',
            progress=progress,
            consulted_user=consulted_user,
        )

    async def _consult_user_on_active_directory(
        self, username: str, transaction: Transaction
    ) -> ADResult:
        """Consults the user on Active Directory
        by its username (samAccountName).

        Parameters
        ----------
        username : str
            Username of the user to consult.
        transaction : Transaction
            Transaction.

        Returns
        -------
        ADResult
            Active Directory result.
        """

        start_time = time.time()
        ad_user = None
        try:
            message = f'Consultando usuario {username} en Directorio Activo...'
            if transaction is not None:
                await self._update_transaction(
                    transaction=transaction, message=message
                )
            ad_user = await run_in_threadpool(
                self.active_directory.search_user,
                username,
                raise_if_not_found=True,
            )
            ad_result = self._get_ad_result(data=ad_user)
        except NotFoundError as e:
            ad_result = self._get_ad_result(
                error='El usuario no fue encontrado en Directorio Activo. {}'.format(
                    self.i18n('ad_data_required_advise')
                ),
                user_not_found=True,
                error_detail=str(e),
            )
        except LDAPSocketOpenError as e:
            ad_result = self._get_ad_result(
                error='No se pudo establecer una conexión al Directorio Activo. {}'.format(
                    self.i18n('ad_data_required_advise')
                ),
                error_detail=str(e),
            )
        except Exception as e:
            ad_result = self._get_ad_result(
                error='No se pudo consultar el usuario en Directorio Activo. {}'.format(
                    self.i18n('ad_data_required_advise')
                ),
                error_detail=str(e),
            )

        # Get time diff
        end_time = time.time()
        ad_result.startTime = start_time
        ad_result.endTime = end_time
        ad_result.timeDiff = end_time - start_time
        return ADResult(
            time=ad_result.timeDiff, ad_user=ad_user, data=ad_result
        )

    async def _run_function(
        self,
        username: str,
        action: Literal['consult', 'remove'],
        application: str,
        applications_progress_step: float,
        func: Callable,
        args: list[object],
        transaction: Transaction,
    ) -> ApplicationResultResponseDto:
        """Call the passed func passing the args.

        Parameters
        ----------
        username : str
            Username of the user to consult.
        action : Literal['consult', 'remove']
            Action to be performed.
        application : str
            Module application name.
        applications_progress_step : int
            Progress step for each application.
        func : Callable
            Automation function callable.
        args : list[object]
            Automation function args.
        transaction : Transaction
            Transaction.

        Returns
        -------
        ApplicationResultResponseDto
            Result of the automation function.

        """
        data = None
        error = None
        error_detail = None
        user_not_found = False
        start_time = time.time()
        try:
            suffix = 'Consultando' if action == 'consult' else 'Retirando'
            msg = f'{suffix} usuario {username} en {self.i18n(application)}...'
            if transaction is not None:
                await self._update_transaction(
                    transaction=transaction, message=msg
                )
            if iscoroutinefunction(func):
                data = await func(*args)
            else:
                data = await run_in_threadpool(func, *args)
        except NotFoundError as e:
            error = 'El usuario no fue encontrado en esta aplicación.'
            error_detail = str(e)
            user_not_found = True
        except Timeout as e:
            error = 'La aplicación ha tardado mucho tiempo en responder.'
            error_detail = str(e)
        except ConnectionError as e:
            error = 'No se pudo establecer una conexión con esta aplicación.'
            error_detail = str(e)
        except Exception as e:
            error = str(e)
            error_detail = (
                e.detail if isinstance(e, AutomationError) else str(e)
            )

        # Update transaction progress
        if transaction is not None:
            transaction.progress += applications_progress_step
            await self._update_transaction(
                transaction=transaction, progress=transaction.progress
            )

        end_time = time.time()
        return ApplicationResultResponseDto(
            application=application,
            applicationName=self.i18n(application),
            data=data,
            error=error,
            errorDetail=error_detail,
            startTime=start_time,
            endTime=end_time,
            timeDiff=end_time - start_time,
            userNotFound=user_not_found,
            hasPrivileges=data.has_privileges
            if isinstance(data, ConsultedUserDataResponseDto)
            else False,
        )

    def _get_log_state(
        self, result: ApplicationResultResponseDto
    ) -> Literal['done', 'error', 'ignored', 'user_not_found']:
        """Get the state of the log from the automation result.

        Returns
        -------
        Literal['done', 'error', 'ignored', 'user_not_found']
            State.

        """
        if result.ignored:
            return 'ignored'
        elif result.userNotFound:
            return 'user_not_found'
        elif result.error:
            return 'error'
        else:
            return 'done'

    async def _create_log(
        self,
        transaction: Transaction,
        username: str,
        moduleName: str,
        moduleFuncArgsSpec: str,
        moduleFuncArgs: list[object],
        result: ApplicationResultResponseDto,
    ):
        """Create a log.

        Parameters
        ----------
        transaction : Transaction
            Transaction.
        username : str
            Consulted username.
        moduleName : str
            Application module name.
        moduleFuncArgsSpec : str
            Specifications of ``main`` of
            the application module name.
        moduleFuncArgs : list[object]
            List of args passed to the ``main`` function
            of the application module name.
        result : ApplicationResultResponseDto
            Result of the execution.

        """
        try:
            created_by = None
            removed_by = None
            if transaction.action == 'consult':
                created_by = transaction.createdBy
            else:
                removed_by = transaction.createdBy
            log_dto = LogRequestDto(
                transactionId=cast(PydanticObjectId, transaction.id),
                username=username,
                consultedBy=created_by,
                removedBy=removed_by,
                moduleName=moduleName,
                moduleFuncArgsSpec=moduleFuncArgsSpec,
                moduleFuncArgs=moduleFuncArgs,
                application=result.application,
                applicationName=result.applicationName,
                status=self._get_log_state(result),
                action=transaction.action,
                data=result.data.to_response() if result.data else None,
                message=result.error,
                detail=result.errorDetail,
                startTime=result.startTime,
                endTime=result.endTime,
                timeDiff=result.timeDiff,
                userNotFound=result.userNotFound,
                ignored=result.ignored,
                warning=result.warning,
            )
            await self.log_service.create(log_dto)
        except Exception as e:
            logger.error(
                f'No se pudo crear el log para el módulo {moduleName}: {str(e)}'
            )

    async def _create_ad_log(
        self,
        username: str,
        transaction: Transaction,
        result: ApplicationResultResponseDto,
    ) -> None:
        """Create a log for AD automation.

        Parameters
        ----------
        username : str
            Consulted username.
        transaction : Transaction
            Transaction.
        result : ApplicationResultResponseDto
            Result.

        """
        await self._create_log(
            transaction=transaction,
            username=username,
            moduleName=self.active_directory.__module__,
            moduleFuncArgsSpec=str(
                signature(self.active_directory.search_user)
            ),
            moduleFuncArgs=[username, True],
            result=result,
        )

    async def _create_result_log(
        self,
        username: str,
        transaction: Transaction,
        function: AutomationFunction,
        result: ApplicationResultResponseDto,
    ) -> None:
        """Create a log for an automation result.

        Parameters
        ----------
        username : str
            Consulted username.
        transaction : Transaction
            Transaction.
        function : AutomationFunction
            Automation function name, callable and args.
        result : ApplicationResultResponseDto
            Result.

        """
        await self._create_log(
            transaction=transaction,
            username=username,
            moduleName=function.name,
            moduleFuncArgsSpec=str(signature(function.func)),
            moduleFuncArgs=function.args,
            result=result,
        )

    async def _run_automation_function(
        self,
        results_store: AutomationFunctionResultsStore,
        action: Literal['consult', 'remove'],
        username: str,
        function: AutomationFunction,
        transaction: Transaction,
        applications_progress_step: float = 0,
        consolidated_date: datetime | None = None,
    ) -> None:
        """Run an automation function.

        Parameters
        ----------
        results_store : AutomationFunctionResultsStore
            Results store.
        action : Literal['consult', 'remove']
            Action to be performed.
        username : str
            Username to consult/remove.
        function : AutomationFunction
            Automation function.
        transaction : Transaction
            Transaction.
        applications_progress_step : float, optional
            Progress step, by default 0.
        consolidated_date : datetime | None, optional
            Consolidated date, by default None.

        """
        app_name = self.i18n(function.name)
        if function.ignored:
            if consolidated_date:
                logger.warning(
                    f'[Consolidado {consolidated_date}] Aplicación {app_name}'
                    f' ignorada para {username}'
                )
            result = ApplicationResultResponseDto(
                application=function.name,
                applicationName=app_name,
                ignored=True,
                warning=function.warning,
            )
        else:
            if consolidated_date:
                logger.info(
                    f'[Consolidado {consolidated_date}] Consultando {username}'
                    f' en {app_name}...'
                )
            result = await self._run_function(
                username=username,
                action=action,
                application=function.name,
                applications_progress_step=applications_progress_step,
                func=function.func,
                args=function.args,
                transaction=transaction,
            )

        result.hasRemove = function.name in self._remove_user_module_names

        await self._create_result_log(
            username=username,
            transaction=transaction,
            function=function,
            result=result,
        )

        results_store.results.append(result.to_response())
        results_store.total_time += result.timeDiff
        if result.warning or result.userNotFound:
            results_store.warning_count += 1
        elif result.error:
            results_store.error_count += 1

    async def _run_automation_functions(
        self,
        action: Literal['consult', 'remove'],
        username: str,
        ad_result: ADResult,
        transaction: Transaction,
        users_progress_step: float | None = None,
        catalog_number: str = '',
        selected_functions_or_modules: list[str] = [],
        consolidated_date: datetime | None = None,
    ) -> AutomationFunctionResultsStore:
        """Run the automation functions according to the action.

        Parameters
        ----------
        action : Literal['consult', 'remove']
            Action to be performed.
        username : str
            Username to consult/remove.
        ad_result : ADResult
            Result of the AD query.
        transaction : Transaction
            Transaction.
        users_progress_step : float, optional
            Progress step, by default None.
        catalog_number : str, optional
            Catalog number.
        selected_functions_or_modules : list[str], optional
            Selected functions or modules, by default [].

        Returns
        -------
        AutomationFunctionResults
            Results.

        """
        automation_functions = self._get_automation_function_list(
            username=username,
            action=action,
            ad_user=ad_result.ad_user,
            catalog_number=catalog_number,
            selected_functions_or_modules=selected_functions_or_modules,
        )

        PLUS_ACTIVE_DIRECTORY = 1
        applications_progress_step = 0
        if users_progress_step is not None and transaction is not None:
            applications_progress_step = users_progress_step / (
                len(automation_functions) + PLUS_ACTIVE_DIRECTORY
            )
            transaction.progress = (
                transaction.progress + applications_progress_step
            )
            await self._update_transaction(
                transaction=transaction, progress=transaction.progress
            )

        results_store = AutomationFunctionResultsStore(
            results=[],
            total_time=0.0,
            error_count=0,
            warning_count=0,
        )

        tasks = [
            self._run_automation_function(
                results_store,
                action,
                username,
                automation_function,
                transaction,
                applications_progress_step,
                consolidated_date,
            )
            for automation_function in automation_functions
        ]
        await asyncio.gather(*tasks)

        return results_store

    async def consult_users(
        self, usernames: str | list[str], transaction: Transaction
    ) -> None:
        """Consults the user by its username on
        multiple applications and save the results.

        Parameters
        ----------
        username : str | list[str]
            Username or usernames to consult.
        transaction : Transaction
            Transaction object to save the state of
            the operation.
        """

        if isinstance(usernames, str):
            usernames = [usernames]

        if not usernames:
            return None

        users_count = len(usernames)
        users_progress_step = 1 / users_count

        for i in range(users_count):
            username = usernames[i].strip().upper()
            consulted_user = None
            results: list[dict[str, object]] = []
            total_time = 0.0

            try:
                consulted_user = await ConsultedUser.find_one(
                    ConsultedUser.username == username,
                    ConsultedUser.isDeleted == False,  # noqa: E712
                )
                if consulted_user and datetime.now() < consulted_user.exp:
                    transaction.progress = (
                        transaction.progress + users_progress_step
                    )
                    await self._update_transaction_found_consulted_user(
                        transaction=transaction,
                        consulted_user=consulted_user,
                        progress=transaction.progress,
                        username=username,
                    )
                    continue

                # Consult user on Active Directory
                ad_result = await self._consult_user_on_active_directory(
                    username=username, transaction=transaction
                )
                total_time += ad_result.time
                results.append(ad_result.data.to_response())
                await self._create_ad_log(
                    username=username,
                    transaction=transaction,
                    result=ad_result.data,
                )

                # Consult user on applications
                automation_functions_results = (
                    await self._run_automation_functions(
                        action='consult',
                        username=username,
                        ad_result=ad_result,
                        transaction=transaction,
                        users_progress_step=users_progress_step,
                    )
                )
                total_time += automation_functions_results.total_time
                results.extend(automation_functions_results.results)

                consulted_user = await self.consulted_user_service.create_or_update(
                    consulted_user=consulted_user,
                    username=username,
                    ad_user=ad_result.ad_user,
                    results=results,
                    error_count=automation_functions_results.error_count,
                    warning_count=automation_functions_results.warning_count,
                    total_time=total_time,
                )

                message = f'Resultados del usuario {username} obtenidos correctamente.'
                transaction.progress = (i + 1) * users_progress_step
                await self._update_transaction(
                    transaction=transaction,
                    message=message,
                    state='progress' if i != (users_count - 1) else 'done',
                    progress=transaction.progress,
                    consulted_user=consulted_user,
                )

            except Exception as e:
                transaction.progress = (i + 1) * users_progress_step
                await self._update_transaction(
                    transaction=transaction,
                    message=f'Ha ocurrido un error durante la consulta del usuario {username}: {str(e)}',
                    state='progress' if i != (users_count - 1) else 'error',
                    progress=transaction.progress,
                    consulted_user=consulted_user,
                )

    async def remove_users(
        self,
        data: RemoveUsersRequestDto,
        transaction: Transaction,
        send_to_idm: bool = False,
    ) -> None:
        """Removes the user by its username on
        the selected applications and save the results.

        Parameters
        ----------
        data : RemoveUsersRequestDto
            List of users to be removed and
            the target applications.
        transaction : Transaction
            Transaction object to save the state of
            the operation.
        send_to_idm : bool, optional
            Whether to send the results to IDM, by default False.
        """

        usernames = list(data.users.keys())
        if not usernames:
            return None

        users_count = len(usernames)
        users_progress_step = 1 / users_count

        for i in range(users_count):
            username = usernames[i].strip().upper()
            removed_user = None
            results: list[dict[str, object]] = []
            total_time = 0.0

            try:
                removed_user = await RemovedUser.find_one(
                    RemovedUser.username == username,
                    RemovedUser.isDeleted == False,  # noqa: E712
                )

                # Consult user on Active Directory
                ad_result = await self._consult_user_on_active_directory(
                    username=username, transaction=transaction
                )
                total_time += ad_result.time
                await self._create_ad_log(
                    username=username,
                    transaction=transaction,
                    result=ad_result.data,
                )

                # Remove user from applications
                selected_apps = data.users[username].applications
                automation_functions_results = (
                    await self._run_automation_functions(
                        action='remove',
                        username=username,
                        ad_result=ad_result,
                        transaction=transaction,
                        users_progress_step=users_progress_step,
                        catalog_number=data.users[username].catalogNumber,
                        selected_functions_or_modules=selected_apps,
                    )
                )
                total_time += automation_functions_results.total_time
                results.extend(automation_functions_results.results)

                removed_user = await self.removed_user_service.create_or_update(
                    removed_user=removed_user,
                    username=username,
                    ad_user=ad_result.ad_user,
                    results=results,
                    catalog_number=data.users[username].catalogNumber,
                    error_count=automation_functions_results.error_count,
                    warning_count=automation_functions_results.warning_count,
                    total_time=total_time,
                )

                if send_to_idm:
                    response = await self.idm_client.send_removed_user_result(
                        str(transaction.id),
                        removed_user.username,
                        removed_user.data,
                    )
                    await self.idm_log_service.create(
                        IDMLogRequestDto(
                            transactionId=cast(
                                PydanticObjectId, transaction.id
                            ),
                            username=removed_user.username,
                            content=response.content,
                            is_success=response.is_success,
                            status_code=response.status_code,
                            request_data=response.request_data,
                        )
                    )

                message = f'Resultados del usuario {username} obtenidos correctamente.'
                transaction.progress = (i + 1) * users_progress_step
                await self._update_transaction(
                    transaction=transaction,
                    message=message,
                    state='progress' if i != (users_count - 1) else 'done',
                    progress=transaction.progress,
                    removed_user=removed_user,
                )

            except Exception as e:
                transaction.progress = (i + 1) * users_progress_step
                await self._update_transaction(
                    transaction=transaction,
                    message=f'Ha ocurrido un error durante el retiro del usuario {username}: {str(e)}',
                    state='progress' if i != (users_count - 1) else 'error',
                    progress=transaction.progress,
                    removed_user=removed_user,
                )

    async def remove_user_and_send_to_idm(
        self,
        data: RemoveUserRequestDto,
        transaction: Transaction,
    ) -> None:
        await self.remove_users(
            data=RemoveUsersRequestDto(
                users={
                    data.username: RemoveUserDataRequestDto(
                        applications=data.applications,
                        catalogNumber=data.catalogNumber,
                    )
                }
            ),
            transaction=transaction,
            send_to_idm=True,
        )

    async def save_daily_consolidated_results(self, date: datetime) -> None:
        """Consults the previous removed users
        and save a consolidated of the results.

        Parameters
        ----------
        date : datetime
            Consolidated date.
        """

        start_time = time.time()

        data = {}
        try:
            removed_users = (
                await self.transaction_service.get_daily_deletions()
            )
        except Exception as e:
            logger.error(
                f'[Consolidado {date}] Error al obtener los usuarios retirados del día actual: {e}'
            )
            return None

        if removed_users:
            logger.info(
                f'[Consolidado {date}] Iniciando generación de consolidado...'
            )
        else:
            logger.info(
                f'[Consolidado {date}] No se encontraron retiros en el día actual.'
            )
            return None

        usernames = []
        for removed_user in removed_users:
            usernames.append(removed_user.username)
            try:
                if removed_user.username not in data:
                    data[removed_user.username] = [
                        result['application'] for result in removed_user.data
                    ]
                else:
                    for result in removed_user.data:
                        if (
                            result.get('ignored', False)
                            or result.get('userNotFound', False)
                            or result.get('error')
                        ):
                            continue
                        if (
                            result['application']
                            not in data[removed_user.username]
                        ):
                            data[removed_user.username].append(
                                result['application']
                            )

            except Exception as e:
                logger.error(
                    f'[Consolidado {date}] Error al obtener las aplicaciones de retiro del usuario {removed_user.username}: {e}'
                )

        transaction = await self.transaction_service.create(
            usernames=usernames,
            message='Consolidado de usuarios retirados iniciado',
            creator='System',
            action='consult',
        )

        results: dict[str, list[dict[str, object]]] = {}
        users_count = len(data)
        counter = 0
        for username, applications in data.items():
            username = username.strip().upper()

            counter += 1
            logger.info(
                f'[Consolidado {date}] Consultando el usuario {username} ({counter}/{users_count})...'
            )

            try:
                # Consult user on Active Directory
                ad_result = await self._consult_user_on_active_directory(
                    username=username, transaction=transaction
                )

                # Consult user on applications
                automation_functions_results = (
                    await self._run_automation_functions(
                        action='consult',
                        username=username,
                        ad_result=ad_result,
                        transaction=transaction,
                        selected_functions_or_modules=applications,
                        consolidated_date=date,
                    )
                )
                results[username] = automation_functions_results.results

                logger.info(
                    f'[Consolidado {date}] Resultados del usuario {username} obtenidos correctamente.'
                )
            except Exception as e:
                logger.error(
                    f'[Consolidado {date}] Error durante la consulta del usuario {username}: {e}'
                )

        end_time = time.time()

        try:
            consolidated = DailyConsolidated(
                data=results,
                startTime=start_time,
                endTime=end_time,
                timeDiff=(end_time - start_time),
            )
            await consolidated.save()
            logger.info(
                f'[Consolidado {date}] Resultados guardados correctamente en la colección de consolidados.'
            )
        except Exception as e:
            logger.error(
                f'[Consolidado {date}] Error al guardar los resultados en la colección de consolidados: {e}'
            )

        await self._update_transaction(
            transaction,
            'Consolidado finalizado',
            'done',
            1,
        )

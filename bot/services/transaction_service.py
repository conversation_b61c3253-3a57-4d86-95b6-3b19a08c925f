import json
import asyncio

from datetime import datetime, timezone
from asyncify import asyncify
from typing import AsyncGenerator, List, Literal
from beanie import PydanticObjectId

from lib.base_injectable import BaseInjectable
from lib.base_service import BaseService
from lib.export import ExportData, File
from lib.filter import Filter, FormatFilter
from lib.pagination import Pagination
from lib.results_exporter import ResultsExporter

from dtos.consulted_user_dto import ConsultedUserResponseDto
from dtos.removed_user_dto import RemovedUserResponseDto
from dtos.transaction_dto import (
    TransactionResponseDto,
    TransactionProgressResponseDto,
)
from models import Transaction, ConsultedUser, RemovedUser, DailyConsolidated


class TransactionService(BaseInjectable, BaseService):
    """Service to create and get transactions."""

    __exporter: ResultsExporter
    __export_data: ExportData
    __filter: Filter[Transaction]

    def __init__(
        self,
        exporter: ResultsExporter,
        export_data: ExportData,
        filter_: Filter,
    ) -> None:
        super().__init__()
        self.__exporter = exporter
        self.__export_data = export_data
        self.__filter = filter_

    @asyncify
    def generate_pdf_file(self, transaction: Transaction) -> File | None:
        """Generate a PDF file from the data of
        the consulted/removed users.

        Parameters
        ----------
        transaction : Transaction
            Transaction instance.

        Returns
        -------
        File | None
            Generated PDF file.

        """
        action_name = (
            'consulta' if transaction.action == 'consult' else 'retiro'
        )
        results = (
            transaction.consultedUsers
            if transaction.action == 'consult'
            else transaction.removedUsers
        )
        pdf_content = self.__exporter.to_pdf(
            result_or_results=results,
            action=transaction.action,
            has_privileges=False,
            include_not_found=True,
            include_errors=True,
        )
        if not pdf_content:
            return None
        date = datetime.now().strftime('%d-%m-%Y_%H-%M-%S')
        filename = f'reporte_{action_name}_{date}'
        return self.__export_data.stream_to_file(
            stream=pdf_content, filename=filename, extension='pdf'
        )

    @asyncify
    def generate_daily_results_pdf_file(
        self,
        transactions: list[Transaction],
        action: Literal['consult', 'remove', 'all'],
        has_privileges: bool,
        include_not_found: bool,
        include_errors: bool,
    ) -> File | None:
        """Generate a PDF file from the transactions of a specific day.

        Parameters
        ----------
        transaction : Transaction
            Transaction instance.
        action : Literal['consult', 'remove', 'all']
            Action of the transaction.
        has_privileges : bool
            Whether to include users with privileges only.
        include_not_found : bool
            Whether to include not found users.
        include_errors : bool
            Whether to include users with errors.

        Returns
        -------
        File
            Generated PDF file.

        """
        pdf_content = self.__exporter.transactions_to_pdf(
            transactions=transactions,
            action=action,
            has_privileges=has_privileges,
            include_not_found=include_not_found,
            include_errors=include_errors,
        )
        if not pdf_content:
            return None
        date = datetime.now().strftime('%d-%m-%Y_%H-%M-%S')
        filename = f'reporte_diario_{date}'
        return self.__export_data.stream_to_file(
            stream=pdf_content, filename=filename, extension='pdf'
        )

    @asyncify
    def generate_consolidated_pdf_file(
        self,
        consolidated: DailyConsolidated,
        include_errors: bool = False,
    ) -> File | None:
        """Generate a PDF file from the transactions of a specific day.

        Parameters
        ----------
        consolidated : DailyConsolidated
            Consolidated instance.
        include_errors : bool, optional
            Whether to include users with errors, by default False.

        Returns
        -------
        File
            Generated PDF file.

        """
        pdf_content = self.__exporter.consolidated_to_pdf(
            consolidated=consolidated,
            include_errors=include_errors,
        )
        if not pdf_content:
            return None
        date = datetime.fromtimestamp(consolidated.startTime).strftime(
            '%d-%m-%Y_%H-%M-%S'
        )
        filename = f'consolidado_retiros_{date}'
        return self.__export_data.stream_to_file(
            stream=pdf_content, filename=filename, extension='pdf'
        )

    async def export_results(self, id: str) -> File | None:
        """Find a transaction by its id and export its
        consulted/removed users.

        Parameters
        ----------
        id : str
            Id of the transaction.#

        Returns
        -------
        File | None
            Exported consulted/removed users to PDF if found.

        """
        transaction = await Transaction.find_one(
            Transaction.id == PydanticObjectId(id),
            Transaction.isDeleted == False,  # noqa: E712
        )
        if not transaction:
            return None
        return await self.generate_pdf_file(transaction)

    async def export_daily_results(
        self,
        action: Literal['consult', 'remove', 'all'],
        has_privileges: bool,
        include_not_found: bool,
        include_errors: bool,
    ) -> File | None:
        """Export the transactions of a specific day.

        Parameters
        ----------
        action : Literal['consult', 'remove', 'all']
            Action of the transaction.
        has_privileges : bool
            Whether the user has privilege.
        include_not_found : bool
            Whether to include not found users.
        include_errors : bool
            Whether to include users with errors.

        Returns
        -------
        File | None
            Exported transactions to PDF if found.

        """
        today = datetime.now().date()
        start_date = datetime(today.year, today.month, today.day)

        criteria = [
            Transaction.state == 'done',
            Transaction.createdAt >= start_date,
            Transaction.isDeleted == False,  # noqa: E712
        ]

        if action != 'all':
            criteria.append(Transaction.action == action)

        transactions = await Transaction.find_many(*criteria).to_list()
        if not transactions:
            return None

        return await self.generate_daily_results_pdf_file(
            transactions=transactions,
            action=action,
            has_privileges=has_privileges,
            include_not_found=include_not_found,
            include_errors=include_errors,
        )

    async def export_last_consolidated(
        self, include_errors: bool = False
    ) -> File | None:
        """Export the last consolidated report.

        Parameters
        ----------
        include_errors : bool, optional
            Whether to include users with errors, by default False.

        Returns
        -------
        File | None
            Exported last consolidated to PDF if found.

        """
        consolidated = await DailyConsolidated.find(
            sort='-startTime'
        ).first_or_none()
        if consolidated is None:
            return None
        return await self.generate_consolidated_pdf_file(
            consolidated, include_errors
        )

    async def get_daily_deletions(self) -> list[RemovedUserResponseDto]:
        """Get the daily deletions.

        Returns
        -------
        list[RemovedUserResponseDto]
            List of daily deletions.

        """
        today = datetime.now().date()
        start_date = datetime(today.year, today.month, today.day)

        criteria = [
            Transaction.action == 'remove',
            Transaction.state == 'done',
            Transaction.createdAt >= start_date,
            Transaction.isDeleted == False,  # noqa: E712
        ]

        transactions = await Transaction.find_many(*criteria).to_list()
        if not transactions:
            return []

        removed = []
        for transaction in transactions:
            removed.extend(transaction.removedUsers)
        return removed

    async def list(
        self,
        page: int,
        rows_per_page: int,
        filter_data: FormatFilter | None = None,
    ) -> Pagination[TransactionResponseDto]:
        """Get the list of Transactions.

        Parameters
        ----------
        page : int
            Current page.
        rows_per_page : int
            Rows per page.
        filter_data : FormatFilter | None, optional
            Dict of mongo search expressions to filter the Transactions, by default None.

        Returns
        -------
        Pagination[TransactionResponseDto]
            Paginated list of Transactions.

        """
        query = Transaction.find(Transaction.isDeleted == False)  # noqa: E712

        if filter_data is not None:
            query = self.__filter.create(filter_data, query)

        pagination = Pagination[TransactionResponseDto](
            page, rows_per_page, await query.count()
        )
        pagination.data = TransactionResponseDto.from_orm_many(
            await query.skip(pagination.skip)
            .limit(pagination.rows_per_page)
            .sort('-createdAt')
            .to_list()
        )
        return pagination

    async def create(
        self,
        usernames: List[str],
        message: str,
        creator: str,
        action: Literal['consult', 'remove'] = 'consult',
    ) -> Transaction:
        """Create a transaction.

        Parameters
        ----------
        usernames : str
            Usernames to consult.
        message : str
            Transaction message.
        creator : str
            Creator name or username.
        action : Literal['consult', 'remove'], optional
            Action to be performed, by default 'consult'.
        catalog_number : str, optional
            Catalog or ticket number, by default None.

        Returns
        -------
        Transaction
            Created transaction.

        """
        transaction = Transaction(
            usernames=usernames,
            action=action,
            message=message,
            createdBy=creator,
        )
        return await transaction.create()

    async def retrieve(self, id: str) -> TransactionResponseDto | None:
        """Find a transaction by id.

        Parameters
        ----------
        id : str
            Transaction id.

        Returns
        -------
        TransactionResponseDto | None
            Transaction if found.

        """
        transaction = await Transaction.find_one(
            Transaction.id == PydanticObjectId(id),
            Transaction.isDeleted == False,  # noqa: E712
        )
        if not transaction:
            return None
        return TransactionResponseDto.from_orm(transaction)

    async def find_in_progress_by_creator(
        self, creator: str
    ) -> TransactionResponseDto | None:
        """Find a transaction in progress by creator.

        Parameters
        ----------
        creator : str
            Creator name or username.

        Returns
        -------
        TransactionResponseDto | None
            Transaction if found.

        """
        transaction = await Transaction.find_one(
            Transaction.createdBy == creator,
            Transaction.isDeleted == False,  # noqa: E712
            Transaction.state == 'progress',
        )
        if not transaction:
            return None
        return TransactionResponseDto.from_orm(transaction)

    async def get_progress(self, id: str) -> AsyncGenerator[str, None]:
        """Stream the transaction current progress.

        Parameters
        ----------
        id : str
            Transaction id.

        Yields
        ------
        Iterator[AsyncGenerator[str, None]]
            JSON-formatted `Transaction` current progress.

        """
        try:
            transaction_in_progress = True
            while transaction_in_progress:
                transaction = await self.retrieve(id)
                if not transaction:
                    progress = TransactionProgressResponseDto(
                        state='error', message='Transacción no encontrada.'
                    )
                    yield json.dumps(
                        progress.to_response(), ensure_ascii=False
                    )
                    return

                await asyncio.sleep(1)
                transaction_in_progress = transaction.state == 'progress'
                progress = TransactionProgressResponseDto.from_orm(transaction)
                yield json.dumps(progress.to_response(), ensure_ascii=False)
        except asyncio.CancelledError:
            progress = TransactionProgressResponseDto(
                state='error',
                message='La conexión fue cerrada sin haber finalizado la transmisión.',
            )
            yield json.dumps(progress.to_response(), ensure_ascii=False)
            return

    async def update_transaction(
        self,
        transaction: Transaction,
        message: str | None = None,
        state: Literal['progress', 'done', 'error'] | None = None,
        progress: float | None = None,
        consulted_user: ConsultedUser | None = None,
        removed_user: RemovedUser | None = None,
    ) -> None:
        """Update a transaction.

        Parameters
        ----------
        transaction : Transaction
            Transaction.
        message : str | None, optional
            Transaction message, by default None.
        state : Literal['progress', 'done', 'error'] | None, optional
            State of the transaction, by default None.
        progress : float | None, optional
            Operations counter to update the `progress` field
            of the transaction, by default None.
        consulted_user : ConsultedUser | None, optional
            Consulted user to be added to the transaction, by default None.
        removed_user : RemovedUser | None, optional
            Removed user to be added to the transaction, by default None.
        """
        if consulted_user:
            transaction.consultedUsers.append(
                ConsultedUserResponseDto.from_orm(consulted_user)
            )
        if removed_user:
            transaction.removedUsers.append(
                RemovedUserResponseDto.from_orm(removed_user)
            )

        transaction.state = state if state else transaction.state
        transaction.message = message if message else transaction.message
        transaction.progress = progress if progress else transaction.progress
        transaction.updatedAt = datetime.now()
        transaction.updatedAtUTC = datetime.now(timezone.utc)
        await transaction.save()

    async def discard_transaction(self, transaction: Transaction) -> None:
        """Discards a transaction.

        Parameters
        ----------
        transaction : Transaction
            Transaction.

        """
        try:
            await transaction.set({str(Transaction.isDeleted): True})
        except Exception:
            return None

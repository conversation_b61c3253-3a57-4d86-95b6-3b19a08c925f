<template>
  <div class="login-container bg-grey-10">
    <div class="column q-col-gutter-sm q-pa-xl bg-grey-3 rounded-borders">
      <div class="text-center">
        <q-img class="logo" src="images/logo.png" alt="Logo SURA" />
      </div>
      <spinner-component v-if="loading" />
      <template v-else>
        <div class="text-body1 text-center text-bold">
          {{ t('couldNotSignIn') }}
        </div>
        <div class="text-body2 text-center q-mb-md">
          {{ t('contactToAdmin') }}
        </div>
        <div class="text-center">
          <q-btn color="primary" :label="t('signInAgain')" size="md" @click="sendSSOForm" />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';

import { loginSSO } from 'src/services/authService';

import SpinnerComponent from 'src/components/common/SpinnerComponent.vue';
//#endregion

const $q = useQuasar();

const route = useRoute();

const { t } = useI18n();

const loading = ref<boolean>(true);

const sendSSOForm = (): void => {
  loginSSO();
}

onMounted(() => {
  if (route.query.auth_failed === 'true') {
    loading.value = false;
    return;
  }

  const loadedPage = $q.sessionStorage.getItem<boolean>('loadedPage');
  if (!loadedPage) {
    $q.sessionStorage.setItem('loadedPage', true);
    sendSSOForm();
  } else {
    loading.value = false;
  }
});
</script>

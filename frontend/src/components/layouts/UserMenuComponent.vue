<template>
  <q-btn-dropdown
    dropdown-icon="person"
    no-icon-animation
    unelevated>
    <div class="column items-center q-pa-md" style="min-width: 200px">
      <q-avatar v-if="initials" size="72px" class="q-mb-sm bg-grey-4 text-dark">
        {{ initials }}
      </q-avatar>
      <div v-if="username" class="text-subtitle1">
        {{ username }}
      </div>
      <div v-if="fullName" class="text-subtitle2">
        {{ fullName }}
      </div>
      <q-separator class="q-my-sm full-width" />
      <q-btn
        :label="t('logout')"
        v-close-popup
        @click="() => logout()"
        color="primary"
        dense />
    </div>
  </q-btn-dropdown>
</template>

<script setup lang="ts">
//#region imports
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import UserSession from 'src/lib/userSession';
//#endregion

const { t } = useI18n({ useScope: 'global' });

const username = computed<string | undefined>(() => UserSession.username);

const fullName = computed<string | undefined>(() => UserSession.fullName);

const initials = computed<string | undefined>(() => UserSession.getFullNameInitials());

const logout = async (): Promise<void> => {
  await UserSession.logout();
}
</script>

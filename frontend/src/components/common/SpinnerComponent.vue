<template>
  <div class="q-pa-xl text-center">
    <q-spinner-gears color="primary" size="60px" />
    <p class="q-mt-md" style="font-size: 12px">{{text}}</p>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';
//#endregion

//#region types
interface SpinnerProps {
  text?: string;
  labelSize?: string;
}
//#endregion

const { t } = useI18n({ useScope: 'global' });

const props =  defineProps<SpinnerProps>();

const text = computed<string>(() => props.text ? props.text : t('loading'));
</script>

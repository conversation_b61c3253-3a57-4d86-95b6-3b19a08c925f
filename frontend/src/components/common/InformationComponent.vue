<template>
  <div v-if="props.type === 'card'">
    <q-card flat :class="`bg-${props.color ?? informationTypes[props.informationType]['color']} ${cardClass}`" :style="{width: props.width }">
      <q-card-section horizontal class="row">
        <div class="row items-center q-pl-xs q-mr-sm">
          <q-icon :color="props.textColor ?? textColor" :name="props.icon ?? informationTypes[props.informationType]['icon']" :size="props.iconSize"></q-icon>
        </div>
        <div  class="row items-center col-11">
          <q-input type="textarea" class="col-12" :input-style="{ color: props.textColor ?? textColor }" borderless dense autogrow readonly  v-model="instructions" />
        </div>
      </q-card-section>
    </q-card>
  </div>
  <div v-if="props.type === 'badge'" :style="{width: props.width}">
    <q-badge :color="props.color ?? informationTypes[props.informationType]['color']" :multi-line="props.multiLine" :class="props.badgeClass">
      <q-icon :color="props.textColor ?? textColor" :name="props.icon ?? informationTypes[props.informationType]['icon']" :size="props.iconSize" class="q-mr-xs"/>
      <div :class="`text-${props.textColor} ${textClass}`"><p class="badge-text q-pa-none q-ma-none">{{ props.text }}</p></div>
    </q-badge>
  </div>
</template>

<script setup lang="ts">
//#region imports
import { computed } from 'vue';
//#endregion

//#region types
interface InformationProps {
  type: 'badge' | 'card';
  width?: string;
  text: string;
  informationType?: 'warning' | 'info' | 'error';
  icon?: string;
  textClass?: string;
  badgeClass?: string;
  cardClass?: string;
  color?: string;
  textColor?: string;
  textSize?: string;
  iconSize?: string;
  multiLine?: boolean;
}

interface Properties {
  color: string;
  icon: string;
}

interface InformationType {
  [key: string]: Properties;
}
//#endregion

const props = withDefaults(defineProps<InformationProps>(), {
  informationType: 'info',
  iconSize: '18px',
  badgeClass: 'q-pa-sm',
  textColor: 'black'
});

const instructions = computed(() => props.text);

const informationTypes: InformationType = {
  'info': {
    color: 'cyan-2',
    icon: 'info'
  },
  'warning': {
    color: 'warning',
    icon: 'warning'
  },
  'error': {
    color: 'negative',
    icon: 'error'
  }
}
</script>

<style scoped>
.badge-text {
  hyphens: auto !important;
  white-space: normal;
}
</style>

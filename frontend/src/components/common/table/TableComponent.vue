<template>
  <q-table
    flat
    dense
    :rows="rows"
    :columns="(columns as QTableProps['columns'])"
    :no-data-label="props.noDataLabel ? props.noDataLabel : t('noDataLabel')"
    :pagination="{rowsPerPage: 0}"
    hide-pagination>
    <template v-slot:header="headerProps">
      <q-tr :props="headerProps" v-if="hasSearch">
        <q-th class="vertical-center q-gutter-xs" auto-width>
          <q-btn flat :icon-right="!expandedConditionsFilter ? 'expand_more' : 'expand_less'" size="sm" no-wrap align="center" padding="xs" dense :label="t('advancedSearch')" @click="expandedConditionsFilter = !expandedConditionsFilter">
            <q-tooltip class="text-body2" :offset="[10, -60]">
              {{ t('useFiltersToSearch') }}
            </q-tooltip>
          </q-btn>
          <div class="items-center column q-gutter-sm">
            <q-btn-group>
              <q-btn class="text-caption q-px-sm" color="secondary" icon="clear" @click="onClearAll" dense size="sm">
                <q-tooltip class="text-body2" :offset="[10, 10]">
                  {{ t('clear') }}
                </q-tooltip>
              </q-btn>
              <q-btn class="text-caption q-px-sm" color="primary" icon="search" @click="onSearch" dense size="sm">
                <q-tooltip class="text-body2" :offset="[10, 10]">
                  {{ t('search') }}
                </q-tooltip>
              </q-btn>
            </q-btn-group>
          </div>
        </q-th>
        <template v-for="(col, index) in (headerProps.cols as TableColumns)" :key="index">
          <q-th v-if="col.searchInput || col.searchSelect" class="vertical-center">
            <search-component
              :clear-trigger="clearTrigger"
              :col-name="col.name"
              :search-object="searchObject"
              :hide-select-condition="col.hideSelectCondition"
              :type="col.searchType"
              :expanded-conditions-filter="expandedConditionsFilter"
              :label="col.label"
              :select="col.searchSelect"
              :options-select="col.optionsSelect"
              :options-conditions="col.optionsConditions"
              @on-search="(() => emits('onSearch', searchObject))"
              @on-value="((filter: string, condition: string) => addToSearch(filter, col.name, condition))"
              @on-clear="(() => onClear(col.name))" />
          </q-th>
          <q-th v-else />
        </template>
      </q-tr>
      <q-tr>
        <q-th v-if="hasExport">
          <export-data-component :export-action="(props.exportAction as ExportAction)" :records-number="props.pagination?.rowsNumber" :filter="searchObject" icon="file_download" />
        </q-th>
        <q-th v-else auto-width />
        <q-th v-for="(col, index) in (headerProps.cols as TableColumns)" :key="index">
          {{col.label}}
        </q-th>
      </q-tr>
    </template>

    <template v-slot:body="bodyProps">
      <q-tr :props="bodyProps">
        <q-td class="text-center" auto-width>
          <q-btn v-if="props.crudOptions || props.seeRowButton" class="q-mr-xs" color="primary" :label="t('see')" @click="emits('onRetrieve', bodyProps.row)" dense size="sm" />
          <q-btn v-if="props.crudOptions || props.editRowButton" class="q-mr-xs" color="primary" :label="t('edit')" @click="emits('onEdit', bodyProps.row)" dense size="sm" />
          <q-btn v-if="props.crudOptions || props.deleteRowButton" class="q-mr-xs" color="primary" :label="t('delete')" @click="emits('onRemove', bodyProps.row)" dense size="sm" />
          <q-btn v-if="props.downloadRowButton" class="q-mr-xs" color="primary" :label="props.downloadRowButtonLabel ?? t('download')" @click="emits('onDownload', bodyProps.row)" dense size="sm" :disable="props.lockDownloadButtons" />
        </q-td>
        <q-td :props="bodyProps" v-for="col in (bodyProps.cols as TableColumns)" :key="col.name" :class="{'text-ellipsis': col.useEllipsis,'cursor-pointer': col.useCursorPointer}" :style="{maxWidth: `${col.maxWidth}px`}">
          {{ col.value }}
          <q-popup-edit v-if="col.usePopUp" class="bg-blue-grey-2" v-model="col.value" :offset="[10, 10]" v-slot="scope" max-width="600px" max-height="400px">
            <div v-html="scope.value"></div>
          </q-popup-edit>
        </q-td>
      </q-tr>
    </template>
  </q-table>
  <q-separator />
  <div v-if="props.pagination" class="q-pa-md">
    <pagination-component v-bind="props.pagination" @on-change-page="onChangePage" />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
//#region imports
import { onBeforeMount, computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import type { QTableProps } from 'quasar';
import { useQuasar } from 'quasar';

import type { Pagination, Search, ExportAction } from 'src/lib/interfaces';

import PaginationComponent from 'src/components/common/table/PaginationComponent.vue';
import ExportDataComponent from 'src/components/common/table/ExportDataComponent.vue';
import SearchComponent from 'src/components/common/table/SearchComponent.vue';
//#endregion

//#region types
export type TableColumns = {
  name: string;
  label: string;
  value: any;
  field: string | ((row: any) => any);
  required?: boolean;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  sort?: (a: any, b: any, rowA: any, rowB: any) => number;
  sortOrder?: 'ad' | 'da';
  format?: (val: any, row: any) => any;
  style?: string | ((row: any) => string);
  classes?: string | ((row: any) => string);
  headerStyle?: string;
  headerClasses?: string;
  searchInput?: boolean;
  searchSelect?: boolean;
  optionsSelect?: any[];
  searchType?: 'number' | 'time' | 'text' | 'date';
  optionsConditions?: string[] | boolean;
  maxWidth?: number;
  useEllipsis?: boolean;
  usePopUp?: boolean;
  hideSelectCondition?: boolean;
  useCursorPointer?: boolean;
}[];

interface FilterObject {
  label?: string;
  value: string;
}

interface TableEmits {
  (e: 'onRetrieve', row: unknown): void;
  (e: 'onEdit', row: unknown): void;
  (e: 'onRemove', row: unknown): void;
  (e: 'onDownload', row: unknown): void;
  (e: 'onChangePage', newPage: number, filter?: Search): void;
  (e: 'onSearch', filter?: Search): void;
}

interface TableProps {
  id?: string;
  rows?: any[];
  columns: TableColumns;
  crudOptions?: boolean;
  seeRowButton?: boolean;
  editRowButton?: boolean;
  deleteRowButton?: boolean;
  downloadRowButton?: boolean;
  downloadRowButtonLabel?: string;
  lockDownloadButtons?: boolean;
  pagination?: Pagination<unknown>;
  exportAction?: ExportAction;
  noDataLabel?: string;
}
//#endregion

const $q = useQuasar();

const { t } = useI18n();

const props = withDefaults(defineProps<TableProps>(), {});

const columns = ref<TableColumns>(props.columns);

const rows = computed<any[]>(() => {
  return props.pagination ? props.pagination.data : (props.rows ? props.rows : []);
});

const emits = defineEmits<TableEmits>();

const searchObject: Search = {};

const clearTrigger = ref<number>(0);

const expandedConditionsFilter = ref<boolean>(false);

const onChangePage = (page: number): void => {
  if(props.id) {
    $q.localStorage.set(`pagination-for-${props.id}`, page);
  }
  emits('onChangePage', page, searchObject);
}

const isNumber = (value: string): any => {
  return /^-?\d+$/.test(value) ? Number(value) : value;
}

const addToSearch = (filter: string | FilterObject, columnName: string, condition: string):void => {
  searchObject[columnName] = {
    exp: condition,
    value: typeof filter == 'object' && filter.hasOwnProperty('value')
      ? (typeof filter.value == 'string' ? filter.value.trim().replace('&', '%26') : isNumber(filter.value))
      : isNumber(String(filter).replace('&', '%26'))
  };
}

const onSearch = (): void => {
  if(props.id) {
    $q.localStorage.set(props.id, searchObject);
  }
  emits('onSearch', searchObject);
}

const onClear = (columnName: string): void => {
  delete searchObject[columnName];
  onSearch();
}

const onClearAll = (): void => {
  if(props.id) {
    $q.localStorage.remove(props.id);
  }
  Object.keys(searchObject).forEach(key => delete searchObject[key]);
  clearTrigger.value = clearTrigger.value + 1;
  emits('onSearch', searchObject);
}

const hasSearch = computed<boolean>(() => {
  if(props.columns)
    for(let col of props.columns) {
      if((col as any).searchInput === true || (col as any).searchSelect === true) return true;
    }
    return false;
});

const hasExport = computed<boolean>(() => props.exportAction != undefined);

onBeforeMount((): void => {
  if(columns.value) {
    for(const column in columns.value) {
      if(!columns.value[column].align) {
        columns.value[column].align = 'center';
      }
    }
  }

  if(props.id){
    const search = $q.localStorage.getItem(props.id);
    let currentPage = $q.localStorage.getItem(`pagination-for-${props.id}`);
    if(search) {
      Object.assign(searchObject, search);
    }
    if(!currentPage || typeof currentPage != 'number') {
      currentPage = 1;
    }
    emits('onChangePage', currentPage, searchObject);
  }
});
</script>

<style scoped>
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

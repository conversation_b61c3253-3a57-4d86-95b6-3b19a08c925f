<template>
  <div :class="props.class">
    <slot :errors="getErrors()" :has-errors="hasErrors()"></slot>
    <slot name="hint">
      <p v-if="!hasErrors() && props.hint" class="q-mb-none q-pt-xs q-px-md text-caption text-primary">{{ props.hint }}</p>
    </slot>
    <slot name="errors">
      <error-list-component v-if="hasErrors()" class="q-pl-sm" :errors="props.errors" :bullet="props.bullet" />
    </slot>
  </div>
</template>

<script setup lang="ts">
//#region imports
import ErrorListComponent from 'src/components/common/ErrorListComponent.vue';
//#endregion

//#region types
export interface ClassFieldComponent {
  [key: string]: boolean;
}

interface FieldProps {
  errors?: string[];
  bullet?: string;
  class?: ClassFieldComponent | string;
  hint?: string;
}
//#endregion

const props = withDefaults(defineProps<FieldProps>(), {
  class: () => { return {'col col-md-6 col-sm-12 col-xs-12': true} }
});

const hasErrors = (): boolean => {
  if(!props.errors) return false;
  return props.errors.length > 0;
}

const getErrors = (): string[] => {
  if(!props.errors) return [];
  return props.errors;
}
</script>

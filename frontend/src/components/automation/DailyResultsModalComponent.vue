<template>
  <q-dialog v-model="modelValue" persistent @before-show="resetForm">
    <q-card style="width: 100%; max-width: 40vw;">
      <q-card-section :class="{ 'bg-primary text-white': !$q.dark.isActive }">
        <div class="row justify-between">
          <div class="text-h6 text-bold">{{ t('exportDailyReport') }}</div>
          <q-icon name="cancel" class="cursor-pointer" size="sm" @click="modelValue = false" />
        </div>
      </q-card-section>
      <q-card-section>
        <div :class="{ 'text-subtitle1 text-bold': true, 'text-primary': !$q.dark.isActive }">
          {{ t('exportDailyReportTooltip') }}
        </div>
        <div class="q-pa-md">
          <div class="column">
            <span class="text-body1 text-left">{{ t('selectWhatToExport') }}</span>
            <q-radio v-model="form.exportType" val="consult" :label="t('consultedResults')" />
            <q-radio v-model="form.exportType" val="remove" :label="t('removedResults')" />
            <q-radio v-model="form.exportType" val="all" :label="t('allResults')" />
            <q-radio v-model="form.exportType" val="previous_deletions"
              :label="t('lastDayDeletions')" />
          </div>
          <div class="column q-mt-sm">
            <template v-if="form.exportType == 'all' || form.exportType == 'consult'">
              <span class="text-body1 text-left">{{ t('forConsultResults') }}</span>
              <q-checkbox v-model="form.hasPrivileges" color="primary"
                :label="t('onlyAppsWhichUserHasPrivilegesOn')" />
              <q-checkbox v-if="!form.hasPrivileges" v-model="form.includeNotFound" color="primary"
                :label="t('includeAppsWhichUserWasNotFoundOn')" />
            </template>
            <q-checkbox v-model="form.includeErrors" color="primary"
              :label="t('includeErrorResults')" />
          </div>
        </div>
        <information-component badge-class="q-pa-xs" text-color="black" information-type="warning"
          type="badge" :text="t('exportDailyReportTimeWarning')" />
        <div class="text-center q-mt-md">
          <q-btn :label="t('export')" color="primary" @click="onSubmit" />
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
//#region imports
import { useQuasar } from 'quasar';
import { computed, reactive } from 'vue';
import { useI18n } from 'vue-i18n';

import type { DailyResultsForm } from 'src/lib/interfaces';

import InformationComponent from 'src/components/common/InformationComponent.vue';
//#endregion

//#region types
interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: 'update:modelValue', showModal: boolean): void;
  (e: 'submit', form: DailyResultsForm): void;
}
//#endregion

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const $q = useQuasar();

const { t } = useI18n();

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(value: boolean) {
    emit('update:modelValue', value);
  }
});

const form = reactive<DailyResultsForm>({
  exportType: 'previous_deletions',
  hasPrivileges: false,
  includeNotFound: false,
  includeErrors: false,
});

function onSubmit(): void {
  emit('submit', {...form});
}

function resetForm(): void {
  form.exportType = 'previous_deletions';
  form.hasPrivileges = false;
  form.includeNotFound = false;
  form.includeErrors = false;
}
</script>

<template>
  <div
    :class="`col column items-center full-width q-mt-md q-pa-lg border-radius ${$q.dark.isActive ? 'bg-grey-10' : 'bg-blue-1'}`">
    <p class="text-h6 text-weight-bold q-mb-sm">{{ t('inThisSpaceYouCanSeeTheResult') }}</p>
    <div class="row justify-center q-col-gutter-md">
      <div class="col-md-4 col-sm-6 col-xs-12">
        <p class="text-subtitle2 text-weight-bold text-center q-ma-none">
          {{ t('clickOnTheExportDailyReportButton') }}
          <span class="text-positive"> "{{ t('exportDailyReport') }}" </span>
          {{ t('locatedInTheTopLeftCorner') }}
        </p>
      </div>
      <div class="col-md-4 col-sm-6 col-xs-12">
        <p class="text-subtitle2 text-weight-bold text-center q-ma-none">
          {{ t('clickOnTheConsultUsersButton') }}
          <span class="text-primary"> "{{ t('consultUsers') }}" </span>
          {{ t('locatedInTheTopRightCorner') }}
          <br>
          {{ t('youCanRemoveUsersAfterConsultingThem') }}
        </p>
      </div>
    </div>
    <q-img class="q-mt-md" src="icons/robot.png" alt="Robot" width="180px" />
  </div>
</template>

<script setup lang="ts">
//#region imports
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
//#endregion

const $q = useQuasar();

const { t } = useI18n();
</script>

/* eslint-disable @typescript-eslint/no-explicit-any */
import { boot } from 'quasar/wrappers';
import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

import UserSession from 'src/lib/userSession';
import { i18n } from 'src/boot/i18n';

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $axios: AxiosInstance;
    $api: AxiosInstance;
  }
}

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)
const api = axios.create({ baseURL: process.env.API_SERVER });

api.interceptors.response.use((response: AxiosResponse<any>): AxiosResponse<any> => {
  return response;
}, async (error: AxiosError<any>) => {
  if(error.response?.status == 401 && !UserSession.onLoginRoute()) {
    await UserSession.logout(Array.isArray(error.response?.data?.common) ? error.response?.data?.common[0] : i18n.global.t('signInAgainPlease'));
  } else {
    throw error;
  }
});

api.interceptors.request.use((request: InternalAxiosRequestConfig<any>): InternalAxiosRequestConfig<any> => {
  request.headers.Authorization = UserSession.getSessionToken();
  return request;
});

export default boot(({ app }) => {
  // for use inside Vue files (Options API) through this.$axios and this.$api

  app.config.globalProperties.$axios = axios;
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options API form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$api = api;
  // ^ ^ ^ this will allow you to use this.$api (for Vue Options API form)
  //       so you can easily perform requests against your app's API
});

export { api };

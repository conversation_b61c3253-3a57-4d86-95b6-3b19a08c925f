//#region imports
import { RouteRecordRaw } from 'vue-router';

import UserSession from 'src/lib/userSession';
//#endregion

const routes: RouteRecordRaw[] = [{
  path: '/',
  component: () => import('layouts/MainLayout.vue'),
  children: [{
    path: '', component: () => import('pages/IndexPage.vue'),
  }],
}, {
  path: '/login',
  component: () => import('layouts/EmptyLayout.vue'),
  beforeEnter: [() => !UserSession.isAnonymous() ? '/' : true],
  children: [{
    path: '', component: () => import('pages/LoginPage.vue')
  }],
}, {
  path: '/health',
  component: () => import('pages/HealthCheckPage.vue'),
}, {
  path: '/forbidden',
  component: () => import('pages/ErrorForbidden.vue'),
}, {
  path: '/:catchAll(.*)*',
  component: () => import('pages/ErrorNotFound.vue'),
}];

export default routes;

import { route } from 'quasar/wrappers';
import { Cookies } from 'quasar';
import {
  createMemoryHistory,
  createRouter,
  createWebHashHistory,
  createWebHistory,
  RouteLocationNormalized,
} from 'vue-router';

import UserSession from 'src/lib/userSession';

import routes from './routes';

/*
 * If not building with SSR mode, you can
 * directly export the Router instantiation;
 *
 * The function below can be async too; either use
 * async/await or return a Promise which resolves
 * with the Router instance.
 */

const createHistory = process.env.SERVER
  ? createMemoryHistory
  : (process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory);

export const router = createRouter({
  scrollBehavior: () => ({ left: 0, top: 0 }),
  routes,

  // Leave this as is and make changes in quasar.conf.js instead!
  // quasar.conf.js -> build -> vueRouterMode
  // quasar.conf.js -> build -> publicPath
  history: createHistory(process.env.VUE_ROUTER_BASE),
});

export default route(function (/* { store, ssrContext } */) {
  router.beforeEach((to: RouteLocationNormalized): string | void => {
    const publicRoutes = [
      '/login',
      '/health'
    ];

    if(UserSession.isAnonymous()) {
      const session = Cookies.get('session');
      if(UserSession.loadFromSessionToken(session)) {
        Cookies.remove('session');
      }
    }

    if(UserSession.isAnonymous() && !publicRoutes.includes(to.path)) {
      return '/login';
    }
  });

  return router;
});

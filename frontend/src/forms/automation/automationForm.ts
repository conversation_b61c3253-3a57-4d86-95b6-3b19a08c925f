//#region imports
import { reactive } from 'vue';

import Form from 'src/lib/form'
import  Validator from 'src/lib/validator/validator';
//#endregion

//#region types
export interface AutomationFormData {
  runByFile: boolean;
  usernames?: string;
  usersFile?: File;
}

export interface AutomationFormErrors {
  common: string[];
  usernames: string[];
  usersFile: string[];
}
//#endregion


export default (): Form<AutomationFormData, AutomationFormErrors> => {
  const form = new Form<AutomationFormData, AutomationFormErrors>(
    reactive({
      runByFile: false,
      usernames: undefined,
      usersFile: undefined
    }),
    reactive({
      common: [],
      usernames: [],
      usersFile: [],
    })
  );
  form.addValidator('usernames', new Validator(() => form.data.usernames).if(() => !form.data.runByFile).required());
  form.addValidator('usersFile', new Validator(() => form.data.usersFile).if(() => form.data.runByFile).required());

  return form;
}

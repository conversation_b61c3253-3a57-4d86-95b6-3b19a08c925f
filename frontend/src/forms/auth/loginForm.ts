//#region imports
import { reactive } from 'vue';

import Form from 'src/lib/form'
import  Validator from 'src/lib/validator/validator';
//#endregion

//#region types
export interface LoginFormData {
  username?: string;
  password?: string;
}

interface LoginFormErrors {
  common: string[];
  username: string[];
  password: string[];
}
//#endregion


export default (): Form<LoginFormData, LoginFormErrors> => {
  const form = new Form<LoginFormData, LoginFormErrors>(
    reactive({
      username: undefined,
      password: undefined
    }),
    reactive({
      common: [],
      username: [],
      password: [],
    })
  );
  form.addValidator('username', new Validator(() => form.data.username).required());
  form.addValidator('password', new Validator(() => form.data.password).required());

  return form;
}

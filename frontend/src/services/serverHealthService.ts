//#region imports
import { api } from 'src/boot/axios';
import { AxiosResponse } from 'axios';

import type { ServerHealth } from 'src/lib/interfaces';
//#endregion

export const getServerHealth = async (): Promise<ServerHealth> => {
  try {
    const response: AxiosResponse<ServerHealth> = await api.get('health');
    return response.data;
  } catch {
    return {status: false, authenticated: false, enableBotRetiros: false};
  }
}

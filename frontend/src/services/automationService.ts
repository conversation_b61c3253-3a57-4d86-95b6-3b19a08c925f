//#region imports
import { AxiosResponse } from 'axios';
import { api } from 'src/boot/axios';
import { SessionStorage } from 'quasar';

import { AutomationFormData } from 'src/forms/automation/automationForm';
import { TransactionTools } from 'src/models/transaction';
import { RemoveUsersData } from 'src/lib/interfaces';
import type { Transaction, TransactionResponse } from 'src/models/transaction';
//#endregion

export const createTransaction = async (path: string, data: FormData | RemoveUsersData): Promise<Transaction> => {
  SessionStorage.removeItem('lastTransactionId');
  const response: AxiosResponse<TransactionResponse> = await api.post(path, data);
  const transaction = TransactionTools.load(response.data);
  SessionStorage.setItem('lastTransactionId', transaction.id);
  return transaction;
}

export const consultUsers = async (data: AutomationFormData): Promise<Transaction> => {
  const form = new FormData();
  form.append('runByFile', JSON.stringify(data.runByFile));
  if (data.usernames) form.append('usernames', data.usernames);
  if (data.usersFile) form.append('usersFile', data.usersFile, data.usersFile.name);
  return await createTransaction('automation/consult_users', form);
}

export const removeUsers = async (data: RemoveUsersData): Promise<Transaction> => {
  return await createTransaction('automation/remove_users', data);
}

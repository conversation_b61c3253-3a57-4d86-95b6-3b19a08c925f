//#region imports
import { api } from 'src/boot/axios';
import { Cookies, LocalStorage, SessionStorage } from 'quasar';
import { jwtDecode, JwtPayload } from 'jwt-decode';
import { router } from 'src/router';
import { ServerHealth } from 'src/lib/interfaces';
import { getServerHealth } from 'src/services/serverHealthService';
import { logout } from 'src/services/authService';
//#endregion

//#region types
interface TokenPayload extends JwtPayload {
  session: string;
  username: string;
  fullname?: string;
  roles: string[];
}
//#endregion

class UserSession {
  public static username?: string;
  public static fullName?: string;
  public static roles?: string[];

  public static serverHealth?: ServerHealth;

  public static load(data: TokenPayload): void {
    UserSession.username = data.username;
    UserSession.fullName = data.fullname;
    UserSession.roles = data.roles;
  }

  public static clear(): void {
    UserSession.username = undefined;
    UserSession.fullName = undefined;
    UserSession.roles = undefined;
    UserSession.clearCookies();
    LocalStorage.clear();
    api.defaults.headers.common['Authorization'] = undefined;
  }

  public static clearCookies(): void {
    Cookies.remove('XSRF-TOKEN');
    Cookies.remove('appTag');
    Cookies.remove('session');
  }

  public static setAuthorizationHeader(token: string): void {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  public static loadFromSessionToken(sessionToken: string | null = null): boolean {
    try {
      let token;
      if(sessionToken) {
        token = sessionToken;
      } else {
        token = LocalStorage.getItem('sessionToken');
      }
      if(!token) {
        UserSession.clear();
        return false;
      }
      const payload = jwtDecode<TokenPayload>(token as string);
      UserSession.load(payload);
      LocalStorage.setItem('sessionToken', token);
      UserSession.setAuthorizationHeader(payload.session);
      return true;
    } catch {
      UserSession.clear();
      return false;
    }
  }

  public static getSessionToken(): string | null {
    if(api.defaults.headers.common['Authorization'])
      return api.defaults.headers.common['Authorization'] as string;
    const token = LocalStorage.getItem('sessionToken');
    if(!token) {
      return null;
    }
    const payload = jwtDecode<TokenPayload>(token as string);
    return `Bearer ${payload.session}`;
  }

  public static getFullNameInitials(): string | undefined {
    if(!UserSession.fullName) return;
    const initials = UserSession.fullName.split(' ').map(word => word.charAt(0));
    if(initials.length == 1) return initials[0].toUpperCase();
    return initials ? `${initials[0]}${initials.pop()}`.toUpperCase() : '';
  }

  public static isAnonymous(): boolean {
    const token: string | null = LocalStorage.getItem('sessionToken');
    if(!token) return true;
    return UserSession.username == undefined;
  }

  public static hasRole(role: string): boolean {
    return UserSession.roles?.includes(role) ?? false;
  }

  public static hasConsult(): boolean {
    return UserSession.hasRole('consult');
  }

  public static hasRemove(): boolean {
    const enabled = UserSession.serverHealth?.enableBotRetiros ?? false;
    return enabled && UserSession.hasRole('remove');
  }

  public static async getServerHealth(): Promise<ServerHealth> {
    const serverHealth = await getServerHealth();
    UserSession.serverHealth = serverHealth;
    return UserSession.serverHealth;
  }

  public static onLoginRoute(): boolean {
    return router.currentRoute.value.path == '/login';
  }

  public static async logout(message?: string): Promise<void> {
    UserSession.clear();
    SessionStorage.clear();
    if(message) LocalStorage.setItem('loginMessage', message);
    await logout();
  }
}

export default UserSession;

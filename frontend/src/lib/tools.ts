/* eslint-disable @typescript-eslint/no-explicit-any */
//#region imports
import <PERSON><PERSON> from 'dexie';
import { AxiosError } from 'axios';
import { Notify, copyToClipboard } from 'quasar';
import { i18n } from 'src/boot/i18n';

import type { Pagination } from 'src/lib//interfaces';
//#endregion

export const downloadFromBlob = (blobPart: BlobPart, contentDisposition: string): void => {
  const blobURL = URL.createObjectURL(new Blob([blobPart]));
  const matches = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
  if(matches != null && matches[1]) {
    const link = document.createElement('a');
    const filename = matches[1].replace('filename=', '').replace(/['"]/g, '');
    link.setAttribute('download', filename);
    link.setAttribute('href', blobURL);
    link.click();
    link.remove();
  }
}

export const getErrorFromBlob = async (error: AxiosError): Promise<string> => {
  if(error.response?.data instanceof Blob) {
    const data: Blob = error.response?.data;
    return JSON.parse(await data.text()).common[0];
  }
  return (error.response?.data as any).common[0] ?? i18n.global.t('dataExportNotAvailable');
}

export const dateParse = (date: string | Date, timeZone = 'America/Bogota', zone = 'es-CO'): string => {
  try {
    if(typeof date == 'string') date = new Date(date);
    const dateTimeZone = date.toLocaleString(zone, { timeZone, hour12: false});
    const newDate = dateTimeZone.split(',');
    const [day, month, year] = newDate[0].split('/');
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}${newDate[1]}`;
  } catch {
    if(typeof date == 'string') return date;
    return date.toString();
  }
}

export const getAxiosErrorMessage = (error: AxiosError): string | undefined => {
  if(!error.response || !error.response.data) {
    return undefined;
  }
  if(!Object.hasOwn(error.response.data, 'common')) {
    return undefined;
  }
  const data = (error.response.data as any);
  return Array.isArray(data.common) ? data.common[0] : data.common;
}

export const generateRandomPassword = (length = 8, characters = '*'): string => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const digits = '0123456789';
  let randomPassword = '';
  for (let i = 0; i < length - 5; i++) {
    randomPassword += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  for (let i = 0; i < 4; i++) {
    randomPassword += digits.charAt(Math.floor(Math.random() * digits.length));
  }
  randomPassword = randomPassword.charAt(0).toUpperCase() + randomPassword.charAt(1).toLowerCase() + randomPassword.slice(2);
  const randomCharacterIndex = Math.floor(Math.random() * characters.length);
  return randomPassword + characters.charAt(randomCharacterIndex);
}

export const paginate = <T>(list: T[], page = 1, rowsPerPage = 20): Pagination<T> => {
  const rowsNumber = list.length;
  const pagesNumber = Math.ceil(rowsNumber / rowsPerPage);
  const skip = rowsPerPage * (page - 1);
  const end = skip + rowsPerPage;
  const data = Dexie.deepClone(list).slice(skip, end < rowsNumber ? end : rowsNumber);

  return {
    data: data,
    page: page,
    pagesNumber: pagesNumber,
    rowsNumber: rowsNumber,
    rowsPerPage: rowsPerPage
  }
}

export const copyValueToClipboard = async (value: string): Promise<void> => {
  try {
    await copyToClipboard(value);
    Notify.create({
      message: i18n.global.t('copiedToClipboard'),
      color: 'positive',
      position: 'top',
      timeout: 3000,
    });
  } catch {
    Notify.create({
      message: i18n.global.t('couldNotCopyToClipboard'),
      color: 'negative',
      position: 'top',
      timeout: 3000,
    });
  }
}

export const extractJSONFromString = (value: string): any => {
  const curlyBracesInclusive = /\{([^}]+)\}/;
  const matches = value.trim().match(curlyBracesInclusive);
  if(!matches) return null;
  try {
    return JSON.parse(matches[0].trim());
  } catch {
    return null;
  }
}

export const bytesToMegas = (value: number | string): number => {
  const megaBytes = 1000000;
  return Number(value) * megaBytes;
}

export const secondsToSchedule = (value: number): string => {
  if(value > 3600) {
    const hours = new Date(value * 1000).toISOString().slice(11, 19);
    return `${hours} ${i18n.global.t('hours')}`;
  } else if(value > 60) {
    const minutes = new Date(value * 1000).toISOString().slice(14, 19);
    return `${minutes} ${i18n.global.t('minutes')}`;
  } else {
    const rounded = Math.round((value + Number.EPSILON) * 100) / 100;
    return `${rounded} ${i18n.global.t('seconds')}`;
  }
}

export const getPercentage = (value: number): string => {
  return (value * 100).toFixed(2) + '%';
}

export function cleanObject(source: object): void {
  for (const prop in source) {
    if (source.hasOwnProperty(prop)) {
      delete source[prop as keyof object];
    }
  }
}

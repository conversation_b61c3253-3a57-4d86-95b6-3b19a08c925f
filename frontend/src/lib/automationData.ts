export const consultedUsersRawData = `
{
    "id": "6723b32a409f9757d3e2858b",
    "usernames": [
        "FERNECOR",
        "ANDECAMO"
    ],
    "action": "consult",
    "state": "done",
    "message": "Resultados del usuario ANDECAMO obtenidos correctamente.",
    "progress": 1,
    "consultedUsers": [
        {
            "id": "6723b1eae02f9ed1270801ad",
            "username": "FERNECOR",
            "exp": "2024-10-31 11:46:26.378000",
            "ad_user": {
                "sam_account_name": "fernecor",
                "user_principal_name": "<EMAIL>",
                "first_name": "<PERSON><PERSON><PERSON>",
                "last_name": "<PERSON><PERSON><PERSON><PERSON>",
                "document": "C71363555",
                "mail": "<EMAIL>",
                "active": true,
                "member_of": [
                    "CN=GS SR VPN_Piloto_Security Client,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=RT SC V Accesos Catalogo,OU=CA CATALOGO,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Autorizados_VPN_Cisco_Aliados,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR PSO Tecnologia Colombia,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=RT AZDEV F GESTIONS,OU=AZDEV,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Usuarios_PAM,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_SPLUNK_SEUS_GESTION_ACCESOS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN ALIADOS,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OCI-PDN,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OCI_PDN_WEB,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OU_Externos,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR RADIUS-PAM,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GD SG CloudGuardProtect,OU=GD Correo,OU=Distribucion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GRUPO SAR SAP DATASERVICES,OU=Citrix,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Proveedores,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Basico,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Lectura_Global,OU=CA CATALOGO,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=IMP SR IMPRESIONGENERAL,OU=Impresion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Bloqueo_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_Infraestructura,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_SEUS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Negocio,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_TI,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR SCCM REPORTS,OU=SCCM,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=AutenticacionVIP,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS IN Somos Sura,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=IMP IN 114 P,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co"
                ]
            },
            "data": [
                {
                    "application": "activeDirectory",
                    "applicationName": "Active Directory",
                    "data": {
                        "sam_account_name": "fernecor",
                        "user_principal_name": "<EMAIL>",
                        "first_name": "Ferney Andres",
                        "last_name": "Echeverri Ortiz",
                        "document": "C71363555",
                        "mail": "<EMAIL>",
                        "active": true,
                        "member_of": [
                            "CN=GS SR VPN_Piloto_Security Client,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=RT SC V Accesos Catalogo,OU=CA CATALOGO,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_Autorizados_VPN_Cisco_Aliados,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR PSO Tecnologia Colombia,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=RT AZDEV F GESTIONS,OU=AZDEV,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_Usuarios_PAM,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS_SR_SPLUNK_SEUS_GESTION_ACCESOS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN ALIADOS,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_OCI-PDN,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_OCI_PDN_WEB,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_OU_Externos,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR RADIUS-PAM,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GD SG CloudGuardProtect,OU=GD Correo,OU=Distribucion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GRUPO SAR SAP DATASERVICES,OU=Citrix,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=SC_Proveedores,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=SC_Basico,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=SC_Lectura_Global,OU=CA CATALOGO,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=IMP SR IMPRESIONGENERAL,OU=Impresion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_Bloqueo_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS_SR_Splunk_Infraestructura,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS_SR_Splunk_SEUS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=SC_Negocio,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=SC_TI,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR SCCM REPORTS,OU=SCCM,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=AutenticacionVIP,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS IN Somos Sura,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=IMP IN 114 P,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.9165735,
                    "endTime": **********.6238081,
                    "timeDiff": 1.7072346210479736,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": false,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "BeyondHealthModule",
                    "applicationName": "Beyond Health",
                    "data": {
                        "active": true,
                        "locked": false,
                        "roles": [
                            "RT_BH_F_CREACION USUARIOS"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.6310494,
                    "endTime": **********.6265237,
                    "timeDiff": 3.995474338531494,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "ConveniosModule",
                    "applicationName": "Convenios",
                    "data": {
                        "active": true,
                        "role": "454 - RT_CVEPS_P_IMPLANTACIONES"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.6284506,
                    "endTime": 1730392882.5312402,
                    "timeDiff": 1.90278959274292,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "FooModule",
                    "applicationName": "Foo Application",
                    "data": {
                        "active": false,
                        "role": "0 - GUEST"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.6284506,
                    "endTime": 1730392882.5312402,
                    "timeDiff": 1.90278959274292,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                },
                {
                    "application": "BarModule",
                    "applicationName": "Bar Application",
                    "data": {
                        "active": true,
                        "role": "454 - RT_CVEPS_P_IMPLANTACIONES"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.6284506,
                    "endTime": 1730392882.5312402,
                    "timeDiff": 1.90278959274292,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "BazModule",
                    "applicationName": "Baz Application",
                    "data": {
                        "active": false,
                        "role": "0 - GUEST"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.6284506,
                    "endTime": 1730392882.5312402,
                    "timeDiff": 1.90278959274292,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                },
                {
                    "application": "QuxModule",
                    "applicationName": "Qux Application",
                    "data": {
                        "active": true,
                        "role": "454 - RT_CVEPS_P_IMPLANTACIONES"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.6284506,
                    "endTime": 1730392882.5312402,
                    "timeDiff": 1.90278959274292,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "QuuxModule",
                    "applicationName": "Quux Application",
                    "data": {
                        "active": true,
                        "role": "454 - RT_CVEPS_P_IMPLANTACIONES"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.6284506,
                    "endTime": 1730392882.5312402,
                    "timeDiff": 1.90278959274292,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "IPSAModule",
                    "applicationName": "IPSA",
                    "data": {
                        "roles": [
                            {
                                "branch": "(100) PUNTO DE VISTA CARACAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1008) IPS CAÑAVERAL COOMULTRASAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1009) FAMILIA IPS SALUD INTEGRAL SAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1013) COOPSANA CALASANZ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(101300) HELPHARMA BOGOTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1030) IPS VIVIR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1031) PROSALCO SAN JUAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1034) SALUD DEL CARIBE ARMENIA SEDE SUR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(106582) RTS SUCURSAL BUCARAMANGA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(107209) AYUDAS DIAGNOSTICAS SALUD SURA SAO PAULO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(11) DARSALUD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(110212) HELPHARMA BARRANQUILLA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(112) CIS COMFAMA APARTADO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(113878) CARDIOPREVENT SAS-ACTIVIDAD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(115393) COOPSANA AVENIDA ORIENTAL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(119950) VIVA 1A IPS SANTA MARTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(12) ASISTIR SALUD SAS SOACHA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(120077) AMI OCCIDENTE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(120289) NEUMOMED - IPS ESPECIALIZADA EPOC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(120292) CEMDE - IPS ESPECIALIZADA CARDIOLOGIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(120361) IPS SURA CENTRAL DE ESPECIALISTAS MEDELLIN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(120818) CENTRO DE CIRUGIA AMBULATORIA-PAF",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(121258) FAN - FUNDACION DE ATENCION A LA NIÑEZ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(122354) AMIGOS DE LA SALUD AMISALUD JAMUNDI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(122810) CLINICA OFTALMOLOGICA SAN DIEGO PGP OFTALMOLOGIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(122814) ORGANIZA SANTA LUCIA PGP ESPECIALIZADA POBLADO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(123409) CONSULTORIO MÉDICO COLSUBSIDIO UNICENTRO OCCIDENTE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(123410) CONSULTORIO MÉDICO COLSUBSIDIO CALLE 116",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(124441) INSTITUTO DE REHABILITACION ISSA ABUCHAIBE-PIP",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(124499) FUNDACION CARDIOINFANTIL - PAF CARDIOLOGIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(125705) RIETE CLINICAS ODONTOLOGICAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(125939) CLINICA LOS ANDES - PAF",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(126162) MEDICARTE IPS ESPECIALIZADA EHC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(126170) CONSULTORIO MÉDICO COLSUBSIDIO PLAZA DE LAS AMERIC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(126310) UROCLIN S.A.S PGP- UROLOGIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(131243) ESTIMA S.A.S IPS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(131946) SALUD VISUAL MAYORCA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(131972) CLINICA CASTELLANA S.A.S.",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(132658) CARDIOPREVENT SAS - IPS ESPECIALIZADA DM",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(133050) PAC ACCESO DIRECTO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(133179) HELPHARMA MANIZALES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(134189) COOPSANA PROGRAMA AUTOINMUNIDAD IPS ESPECIALIZADA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(134641) COMITE DE ESTUDIOS MEDICOS - RH POSTCOVID MEDELLIN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(135291) IPS ATEL MEDISALUD ARMENIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(135718) HELPHARMA FARMANORTE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(137277) FUND HOSPITAL SAN VICENTE RED RESPONSABLE CUIDADO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(137361) HOLA DOCTOR S.A.S.",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(137947) PIEL JOVEN S.A.S",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(138078) PIEL JOVEN S.A.S SEDE RIONEGRO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(138721) IPS FISIO S.A.S - IPS ESPECIALIZADA OBESIDAD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(138826) IPS PAC SALUD DEL CARIBE SAN FELIPE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(138945) CENTRO ESPECIALISTAS DEL EJE S.A.S PAC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(139143) HELPHARMA CAPITA SANTA MARTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(139714) OPTICA SANTA LUCIA OPTOMETRIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(139978) MEDICARTE FARMAAPARTADO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140111) IPS SURA VIRTUAL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140246) CLINICA OTORRINOLARINGOLOGIA - RIONEGRO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140439) SIES IPS ESPECIALIZADA PEREIRA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140440) SIES IPS ESPECIALIZADA ARMENIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140650) CLINICA NEUMOLOGICA DEL PACIFICO - ACTIVIDAD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(14072) ESE HOSPITAL LA MISERICORDIA DE CALARCA -CAPITA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140784) ASORSALUD SM LTDA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140882) INST COLOMBIANO MEDICINA INTEGRA -IPS ESPECIALIZAD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(141197) CAF LA 80",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(141470) MEDICARTE MALAMBO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(142125) CLINICA SOMA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(142152) CEMDE - RIONEGRO ICC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(142192) CEMDE - RIONEGRO OBESIDAD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(143238) CENTRO MEDICO SURA PAC SANTA BARBARA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1439) CLINICA COMFAMILIAR RISARALDA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(145766) HELPHARMA TELEMEDICINA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(145880) CUIDARTE EN CASA S.A.S - DC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(147935) NEUROMEDICA ARMENIA SEDE DOLOR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(148106) CORPORACION IPS UNIVERSITARIA DE CALD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(150192) NEUROMEDICA-MAYORCA S.A.S",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(150508) AYUDAS DIAGNOSTICATIEMPO PARA TI-PATOLOGIA MAMARIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(153299) CENTRO DE ESPECIALISTAS VIVA 1A",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(153664) REFORMAR IPS FUNDACIÓN - CUIDADOS PALIATIVOS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(153694) COMFAMA BUENOS AIRES \u0096 PARQUEADERO SOTANO 4",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(157266) SIES IPS ESPECIALIZADA APARTADO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(158874) VIVA 1A IPS CALLE 85 - SALA PAC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(159372) H & G MEDICA INTEGRAL SAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(159683) CLINICA ANI - H & G SAS-PGP",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(160098) MIRED BARRANQUILLA IPS S.A.S. - IPS BASICA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(161399) INSTITUTO DE CANCEROLOGIA PIP MAMA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(161444) GESENCRO BUGA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(161446) CENTRO DE ATENCION FAMILIAR INTEGRAL LA TEBAIDA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(161447) SINERGIA GLOBAL EN SALUD S.A.S EL BOSQUE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(161454) RTS AGENCIA SOACHA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(161476) CHRISTUS SINERGIA SALUD CENTRO SUR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1622) AYUDAS DIAGNOSTICAS SURA ITAGÜI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(162480) NEUROMEDICA PGP OFTALMOLOGIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(163265) COMFAMILIAR DOSQUEBRADAS SEDE ODONTOLOGIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(164449) CENTRO MEDICO IMBANACO-PIP MAMA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(165) ASISTIR SALUD SAS FONTIBON",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(166467) SALUD SURA PORTAL DEL GENOVÉS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(167736) SYSO CONSULTORES S.A.S",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1695) JAVESALUD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(17) PROSALCO SANTA ROSA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1712) INTERCONSULTAS SAS SEDE 1",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1714) SALUD DEL CARIBE PIE LA POPA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1721) RADIOLOGOS ASOCIADOS S.A.S",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1761) A Y G NIZA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1827) SALUD VISUAL OLAYA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(20) IPS SURA ROBLEDO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(203) CLINICA DE OTORRINOLARINGOLOGIA DE ANTIOQUIA S.A",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2161) SALUDSURA INDUSTRIALES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2205) PROSALCO BARBOSA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2264) SALUD DEL CARIBE SANTA LUCIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2278) SALUDSURA BOGOTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2325) IPS COBERSALUD INTEGRAL EN SALUD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2424) PROSALCO MARINILLA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2431) PROSALCO GUARNE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2433) IPS UNIDAD MEDICA SANTA FE AMÉRICAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(24427) HOSPITAL SAGRADO CORAZÓN DE JESÚS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2448) IPS CALLE 30",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(24827) HOSPITAL SAN VICENTE ESE MONTENEGRO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(26) CIS COMFAMA ITAGUI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2642) SALUD DEL CARIBE ARMENIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2669) URGENCIAS IPS SURA PASO ANCHO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2671) IPS COLSUBSIDIO PEREIRA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2672) SALUD EN CASA INDUSTRIALES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2676) CENTRO DE EXCELENCIA CLINICA SANTA HELENA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2679) CIS COMFAMA COPACABANA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2681) CIS COMFAMA CHIGORODO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2683) IPS CLINICA CHIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2685) PUNTO DE ATENCION EN SALUD SUBA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2687) PUNTO DE ATENCIÓN EN SALUD SURA CASTELLANA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2695) PUNTO DE ATENCIÓN EN SALUD SURA SUR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2696) VIVA 1A SAN JOSE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2698) VIVA 1A CALLE 85",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(27) CIS COMFAMA ENVIGADO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2701) COOMSOCIAL ESTADIO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2702) COOMSOCIAL BELLO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2704) CIS COMFAMA LA ESTRELLA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2705) PROSALCO CARMEN DE VIBORAL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2708) IPS CAJA COMPENSACIÓN FAMILIAR CALDAS - CONFA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2709) CIS COMFAMA LOPEZ DE MESA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2711) IPS BASICA COMFANDI BUGA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2712) CENTRO MEDICO COLSUBSIDIO CALI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2715) IPS PAC COOPSANA SURAMERICANA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2716) IPS COOMULTRASAN CARRERA 27",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2717) COMFAMILIAR RISARALDA SEDE DOSQUEBRADAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2718) CIS COMFAMA SALUD PLAZA APARTADO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2719) CIS COMFAMA CALASANZ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2721) CIS COMFAMA CURRULAO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2725) CIS COMFAMA BUENOS AIRES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2726) IPS COLSUBSIDIO SURA PAC SANTA BARBARA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2727) CIS COMFAMA CITY PLAZA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2739) CIS COMFAMA SAN ANTONIO DE PRADO - ITAGUI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2740) CIS COMFAMA SANTA MARIA - ITAGUI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2741) CIS COMFAMA SANTUARIO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2743) CIS COMFAMA EL PORVENIR - RIONEGRO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2744) IPS VIVIR NORTE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2746) IPS SURA GARPER MEDICA TUNJA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2748) CENTRO MEDICO SURA PLAZA CENTRAL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2749) VIVA 1A MALAMBO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2750) CENTRO MÉDICO SURA NUESTRO BOGOTÁ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2757) COLSUBSIDIO PEREIRA SEDE PINARES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2758) GESENCRO IPS BASICA POPAYAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2759) GESENCRO IPS BASICA PALMIRA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2762) CIS LA ESTRELLA CENTRAL DE SERVICIOS SUR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2763) JAVESALUD CALI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2765) CIS COMFAMA SAN CRISTOBAL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2766) E.S.E. HOSPITAL SAN JOSE DE AGUADAS - IPS BASICA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2767) ESE HOSPITAL SAN VICENTE DE PAUL ANSERMA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2768) ESE HOSPITAL DEPARTAMENTAL SAN VICENTE DE PAUL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2769) ESE HOSPITAL SAN CAMILO - BUENAVISTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2770) HOSPITAL SAN ROQUE DE CORDOBA QUINDIO ESE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2771) IPS ESE HOSPITAL SAN VICENTE DE PAUL FILANDIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2772) ESE HOSPITAL SAN VICENTE DE PAUL GENOVA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2773) IPS REDSALUD ARMENIA ESE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2774) IPS HOSPITAL SANTA ANA DE PIJAO QUINDIO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2775) IPS ESE HOSPITAL SAN VICENTE DE PAUL SALENTO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2776) IPS ESE HOSPITAL SAN VICENTE DE PAUL CIRCASIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2780) CENTRO MÉDICO SURA AVENTURA PLAZA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2781) HOSPITAL PIO X - IPS BASICA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2782) IPS PAC PROSALCO LAS VEGAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2789) IPS COOMULTRASAN PIEDECUESTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2791) CIS COMFAMA SABANETA NORTE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2795) IPS SUMEDICA YUMBO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2798) IPS ESPECIALIZADA SURA - DIABETES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2807) IPS HOSPITAL SANTANDER DE CAICEDONIA VALLE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(29) CIS COMFAMA SAN IGNACIO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(30) CIS COMFAMA MANRIQUE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(31) CIS COMFAMA ARANJUEZ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(32) CIS COMFAMA GIRARDOTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(33) CIS COMFAMA LA CEJA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(34) CIS COMFAMA CALDAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(35) COOPSANA - CENTRO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(37) CIS COMFAMA BELLO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(39439) INSTITUTO DE REHABILITACION ISSA ABUCHAIBE MURILLO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(43) IPS SURA ITAGUI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(443) CLINICA FARALLONES S.A.",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(50111) NEUMOMED S.A.S",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(50173) INCODOL ORTOPEDIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(50176) INSTITUTO DE COLOPROCTOLOGIA MEDELLIN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(50188) ART MEDICA S.A.S. IPS ESPECIALIZADA-REUMATOLOGIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(53) CIS COMFAMA RIONEGRO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(54) IPS SURA ALTOS BARRANQUILLA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(569) CLINICA CES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(59) A Y G SERVICIOS DE SALUD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(6) HUMANITAS - ITAGUI",
                                "position": "(3) Auxiliar Punto de Servicio",
                                "role": "(40) PUNTO DE SERVICIO",
                                "status": "(0) INACTIVO"
                            },
                            {
                                "branch": "(60) INSTITUTO DEL TORAX - LA PAZ",
                                "position": "(9) Médico General",
                                "role": "(18) MEDICO GENERAL CONSULTA POS",
                                "status": "(0) INACTIVO"
                            },
                            {
                                "branch": "(620) INSTITUTO DE REHABILITACION",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(78278) HELPHARMA ALMACENTRO",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(79) IPS SURA OLAYA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(826) CLINICA LOS ANDES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(828) CLINICA DE OCCIDENTE - CALI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(83) SALUD EN CASA CALI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(86079) MEDICARTE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(87799) MEDICARTE BOGOTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(87819) MEDICARTE BARRANQUILLA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(87859) MEDICARTE MANIZALES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(87899) MEDICARTE ARMENIA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(902) AYUDAS DIAGNOSTICAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(91) CIS COMFAMA SABANETA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(93) IPS SURA BOSTON",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(935) REGIONAL CALI",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(99998) CENTRO DE ESPECIALISTAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(999999) CENTRAL PAC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            }
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": 1730392882.533218,
                    "endTime": 1730392885.399117,
                    "timeDiff": 2.865899085998535,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": false,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "OHIModule",
                    "applicationName": "OHI",
                    "data": {
                        "active": true,
                        "roles": [
                            "RT_OHI_A_GIGA"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": 1730392885.4101489,
                    "endTime": **********.3765748,
                    "timeDiff": 0.966425895690918,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": false,
                    "hasPrivileges": true,
                    "warning": null
                }
            ],
            "time": 11.**************,
            "updatedAt": "2024-10-31 11:41:26.378000",
            "createdAt": "2024-10-31 11:35:54.396000"
        },
        {
            "id": "6723b2e5e02f9ed1270801b5",
            "username": "ANDECAMO",
            "exp": "2024-10-31 11:46:36.669000",
            "ad_user": {
                "sam_account_name": "andecamo",
                "user_principal_name": "<EMAIL>",
                "first_name": "Anderson",
                "last_name": "Casallas Molina",
                "document": "C1020485629",
                "mail": "<EMAIL>",
                "active": true,
                "member_of": [
                    "CN=GS SR VPN_Piloto_Security Client,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Autorizados_VPN_Cisco_Aliados,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR PSO Tecnologia Colombia,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Bloqueo_VPN_Cliente_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Usuarios_PAM,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_SPLUNK_SEUS_GESTION_ACCESOS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN ALIADOS,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OU_Externos,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR RADIUS-PAM,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GD SG CloudGuardProtect,OU=GD Correo,OU=Distribucion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Proveedores,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Bloqueo_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_Infraestructura,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_SEUS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS IN Usuarios 802.1X Terceros PEAP,OU=802.1X,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GRUPO CITRIX XAPP DIOGENES,OU=Citrix,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR SCCM REPORTS,OU=SCCM,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=AutenticacionVIP,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS IN Somos Sura,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co"
                ]
            },
            "data": [
                {
                    "application": "activeDirectory",
                    "applicationName": "Active Directory",
                    "data": {
                        "sam_account_name": "andecamo",
                        "user_principal_name": "<EMAIL>",
                        "first_name": "Anderson",
                        "last_name": "Casallas Molina",
                        "document": "C1020485629",
                        "mail": "<EMAIL>",
                        "active": true,
                        "member_of": [
                            "CN=GS SR VPN_Piloto_Security Client,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_Autorizados_VPN_Cisco_Aliados,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR PSO Tecnologia Colombia,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_Bloqueo_VPN_Cliente_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_Usuarios_PAM,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS_SR_SPLUNK_SEUS_GESTION_ACCESOS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN ALIADOS,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_OU_Externos,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR RADIUS-PAM,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GD SG CloudGuardProtect,OU=GD Correo,OU=Distribucion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=SC_Proveedores,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR VPN_Bloqueo_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS_SR_Splunk_Infraestructura,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS_SR_Splunk_SEUS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS IN Usuarios 802.1X Terceros PEAP,OU=802.1X,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GRUPO CITRIX XAPP DIOGENES,OU=Citrix,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS SR SCCM REPORTS,OU=SCCM,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=AutenticacionVIP,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                            "CN=GS IN Somos Sura,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.390123,
                    "endTime": **********.0634785,
                    "timeDiff": 0.6733555793762207,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": false,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "BeyondHealthModule",
                    "applicationName": "Beyond Health",
                    "data": {
                        "active": true,
                        "locked": false,
                        "roles": [
                            "RT_BH_F_CREACION USUARIOS"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.0730305,
                    "endTime": **********.1073148,
                    "timeDiff": 4.034284353256226,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "ConveniosModule",
                    "applicationName": "Convenios",
                    "data": {
                        "active": true,
                        "role": "454 - RT_CVEPS_P_IMPLANTACIONES"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.1096838,
                    "endTime": 1730392893.0546331,
                    "timeDiff": 1.9449493885040283,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "IPSAModule",
                    "applicationName": "IPSA",
                    "data": {
                        "roles": [
                            {
                                "branch": "(1008) IPS CAÑAVERAL COOMULTRASAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1013) COOPSANA CALASANZ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1030) IPS VIVIR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1031) PROSALCO SAN JUAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(120287) HELPHARMA - IPS ESPECIALIZADA NEUMOLOGIA-EPOC",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(121627) AYUDAS DIAGNOSTICAS SALUD SURA CHIPICHAPE",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(137792) ART MEDICA MANIZALES IPS ESPECIALIZADA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(140111) IPS SURA VIRTUAL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1695) JAVESALUD",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2448) IPS CALLE 30",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2671) IPS COLSUBSIDIO PEREIRA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2672) SALUD EN CASA INDUSTRIALES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(2748) CENTRO MEDICO SURA PLAZA CENTRAL",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(50105) INCODOL LA 33 INSTITUTO COLOMBIANO DEL DOLOR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(826) CLINICA LOS ANDES",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(99998) CENTRO DE ESPECIALISTAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            }
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": 1730392893.0564206,
                    "endTime": 1730392895.8123531,
                    "timeDiff": 2.755932569503784,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": false,
                    "hasPrivileges": true,
                    "warning": null
                },
                {
                    "application": "OHIModule",
                    "applicationName": "OHI",
                    "data": {
                        "active": true,
                        "roles": [
                            "RT_OHI_A_GIGA"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": 1730392895.815377,
                    "endTime": 1730392896.667189,
                    "timeDiff": 0.8518118858337402,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": false,
                    "hasPrivileges": true,
                    "warning": null
                }
            ],
            "time": 10.260333776473999,
            "updatedAt": "2024-10-31 11:41:36.669000",
            "createdAt": "2024-10-31 11:40:05.553000"
        }
    ],
    "updatedAt": "2024-10-31 11:41:36.671000",
    "createdAt": "2024-10-31 11:41:14.905000",
    "createdBy": "test"
}
`;

export const removedUsersRawData = `
{
    "id": "6723bf282983d69bebf81380",
    "usernames": [
        "FERNECOR",
        "ANDECAMO"
    ],
    "action": "remove",
    "state": "done",
    "message": "Resultados del usuario ANDECAMO obtenidos correctamente.",
    "progress": 1,
    "consultedUsers": [],
    "removedUsers": [
        {
            "id": "6723b9da7f4792dd2ace0ccc",
            "username": "FERNECOR",
            "ad_user": {
                "sam_account_name": "fernecor",
                "user_principal_name": "<EMAIL>",
                "first_name": "Ferney Andres",
                "last_name": "Echeverri Ortiz",
                "document": "C71363555",
                "mail": "<EMAIL>",
                "active": true,
                "member_of": [
                    "CN=GS SR VPN_Piloto_Security Client,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=RT SC V Accesos Catalogo,OU=CA CATALOGO,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Autorizados_VPN_Cisco_Aliados,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR PSO Tecnologia Colombia,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=RT AZDEV F GESTIONS,OU=AZDEV,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Usuarios_PAM,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_SPLUNK_SEUS_GESTION_ACCESOS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN ALIADOS,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OCI-PDN,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OCI_PDN_WEB,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OU_Externos,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR RADIUS-PAM,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GD SG CloudGuardProtect,OU=GD Correo,OU=Distribucion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GRUPO SAR SAP DATASERVICES,OU=Citrix,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Proveedores,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Basico,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Lectura_Global,OU=CA CATALOGO,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=IMP SR IMPRESIONGENERAL,OU=Impresion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Bloqueo_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_Infraestructura,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_SEUS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Negocio,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_TI,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR SCCM REPORTS,OU=SCCM,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=AutenticacionVIP,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS IN Somos Sura,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=IMP IN 114 P,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co"
                ]
            },
            "data": [
                {
                    "application": "BeyondHealthModule",
                    "applicationName": "Beyond Health",
                    "data": {
                        "message": "Roles eliminados correctamente.",
                        "warning": false,
                        "removed_roles": [
                            "RT_BH_F_CREACION USUARIOS"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.2911518,
                    "endTime": **********.295977,
                    "timeDiff": 0.0048253536224365234,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                },
                {
                    "application": "ConveniosModule",
                    "applicationName": "Convenios",
                    "data": {
                        "message": "Usuario retirado correctamente.",
                        "warning": false,
                        "active": false,
                        "role": "0 - GUEST"
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.2984202,
                    "endTime": **********.3032336,
                    "timeDiff": 0.004813432693481445,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                },
                {
                    "application": "IPSAModule",
                    "applicationName": "IPSA",
                    "data": {
                        "message": "Roles eliminados correctamente.",
                        "warning": false,
                        "removed_roles": [
                            {
                                "branch": "(100) PUNTO DE VISTA CARACAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1008) IPS CAÑAVERAL COOMULTRASAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1009) FAMILIA IPS SALUD INTEGRAL SAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1013) COOPSANA CALASANZ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(101300) HELPHARMA BOGOTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1030) IPS VIVIR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1031) PROSALCO SAN JUAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            }
                        ],
                        "unremoved_roles": []
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.3071167,
                    "endTime": **********.3127182,
                    "timeDiff": 0.005601406097412109,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                },
                {
                    "application": "OHIModule",
                    "applicationName": "OHI",
                    "data": {
                        "message": "Usuario retirado correctamente.",
                        "warning": false,
                        "active": false,
                        "removed_roles": [
                            "RT_OHI_A_GIGA"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.3157418,
                    "endTime": **********.320642,
                    "timeDiff": 0.004900217056274414,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                }
            ],
            "time": 1.****************,
            "updatedAt": "2024-10-31 12:32:26.324000",
            "createdAt": "2024-10-31 12:09:46.140000"
        },
        {
            "id": "6723b9db7f4792dd2ace0cce",
            "username": "ANDECAMO",
            "ad_user": {
                "sam_account_name": "andecamo",
                "user_principal_name": "<EMAIL>",
                "first_name": "Anderson",
                "last_name": "Casallas Molina",
                "document": "C1020485629",
                "mail": "<EMAIL>",
                "active": true,
                "member_of": [
                    "CN=GS SR VPN_Piloto_Security Client,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Autorizados_VPN_Cisco_Aliados,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR PSO Tecnologia Colombia,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Bloqueo_VPN_Cliente_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Usuarios_PAM,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_SPLUNK_SEUS_GESTION_ACCESOS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN ALIADOS,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_OU_Externos,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR RADIUS-PAM,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GD SG CloudGuardProtect,OU=GD Correo,OU=Distribucion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=SC_Proveedores,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR VPN_Bloqueo_CP,OU=VPN,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_Infraestructura,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS_SR_Splunk_SEUS,OU=Suramericana,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS IN Usuarios 802.1X Terceros PEAP,OU=802.1X,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GRUPO CITRIX XAPP DIOGENES,OU=Citrix,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS SR SCCM REPORTS,OU=SCCM,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=AutenticacionVIP,OU=Administracion,OU=GRUPOS,DC=suramericana,DC=com,DC=co",
                    "CN=GS IN Somos Sura,OU=Inversura,OU=GRUPOS,DC=suramericana,DC=com,DC=co"
                ]
            },
            "data": [
                {
                    "application": "BeyondHealthModule",
                    "applicationName": "Beyond Health",
                    "data": {
                        "message": "Roles eliminados correctamente.",
                        "warning": false,
                        "removed_roles": [
                            "RT_BH_F_CREACION USUARIOS"
                        ]
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.162851,
                    "endTime": **********.1707318,
                    "timeDiff": 0.007880687713623047,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                },
                {
                    "application": "IPSAModule",
                    "applicationName": "IPSA",
                    "data": {
                        "message": "Roles eliminados correctamente.",
                        "warning": false,
                        "removed_roles": [
                            {
                                "branch": "(100) PUNTO DE VISTA CARACAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1008) IPS CAÑAVERAL COOMULTRASAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1009) FAMILIA IPS SALUD INTEGRAL SAS",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1013) COOPSANA CALASANZ",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(101300) HELPHARMA BOGOTA",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1030) IPS VIVIR",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            },
                            {
                                "branch": "(1031) PROSALCO SAN JUAN",
                                "position": "(15) Analista de Soporte",
                                "role": "(17) CREACION DE USUARIOS",
                                "status": "(1) ACTIVO"
                            }
                        ],
                        "unremoved_roles": []
                    },
                    "error": null,
                    "errorDetail": null,
                    "startTime": **********.1739125,
                    "endTime": **********.1826532,
                    "timeDiff": 0.008740663528442383,
                    "userNotFound": false,
                    "ignored": false,
                    "hasRemove": true,
                    "hasPrivileges": false,
                    "warning": null
                }
            ],
            "time": 0.8371255397796631,
            "updatedAt": "2024-10-31 12:32:27.185000",
            "createdAt": "2024-10-31 12:09:47.312000"
        }
    ],
    "catalogNumber": "C-123456",
    "updatedAt": "2024-10-31 12:32:27.188000",
    "createdAt": "2024-10-31 12:32:24.769000",
    "createdBy": "test"
}
`;

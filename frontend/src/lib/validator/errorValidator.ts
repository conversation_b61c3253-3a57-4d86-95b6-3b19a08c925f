//#region imports
import type { DataValidatorError } from 'src/lib/validator/dataValidator';
//#endregion

class ErrorValidator{
  public errors: DataValidatorError;

  public constructor(){
    this.errors = {};
  }

  public clear(){
    this.errors = {};
  }

  public add(key: string, error: string | string[]): void{
    if(!(key in this.errors)){
      this.errors[key] = [];
    }

    if (typeof error == 'string'){
      this.errors[key].push(error);
    }else{
      for(const item of error){
        this.errors[key].push(item);
      }
    }
  }
}

export default ErrorValidator;
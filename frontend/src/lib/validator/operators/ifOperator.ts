//#region imports
import ClassOperator from 'src/lib/validator/classOperator';
//#endregion

class IfOperator extends ClassOperator {

  public constructor(evalCondition: boolean | (() => boolean)) {
    super(evalCondition);
  }

  public async validate(): Promise<boolean> {
    const evalCondition = this.evalCondition();
    return (evalCondition === true) ? this.isValid = true : this.isValid = false;
  }
}

export default IfOperator;
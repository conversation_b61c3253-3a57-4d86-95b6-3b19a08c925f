//#region imports
import ClassValidator from 'src/lib/validator/classValidator'
import ClassOperator from 'src/lib/validator/classOperator';
import {IfOperator, IfNotOperator, StopOperator} from 'src/lib/validator/operators/module';
import {
  UrlValidator,
  RangeLengthValidator,
  MinLengthValidator,
  EmailValidator,
  IdCardValidator,
  PhoneValidator,
  GreaterThanValidator,
  RequiredValidator,
  LowerThanValidator,
  GreaterEqualsThanValidator,
  LowerEqualsThanValidator,
  RangeValidator,
  CellPhoneValidator,
  CustomValidator,
  RegularExpressionValidator,
  MaxLengthValidator,
  ObjectValidator,
  InValidator
} from 'src/lib/validator/validators/module';
//#endregion

class Validator<T> {
  public value: T;
  public isValid: boolean;
  public errors: string[];
  public validators: ClassOperator[] & ClassValidator[];

  public constructor(value: T) {
    this.value = value;
    this.isValid = true;
    this.errors = [];
    this.validators = [];
  }

  public required(message?: string): Validator<T> {
    const validator = new RequiredValidator(this.value, message);
    this.validators.push(validator);
    return this;
  }

  public email(message?: string): Validator<T> {
    const validator = new EmailValidator(this.value, message);
    this.validators.push(validator);
    return this;
  }

  public idCard(message?: string): Validator<T> {
    const validator = new IdCardValidator(this.value, message);
    this.validators.push(validator);
    return this;
  }

  public phone(includeAreaCode?: boolean, message?: string): Validator<T> {
    const includeCode = (includeAreaCode) ? includeAreaCode : false;
    const validator = new PhoneValidator(this.value, includeCode, message);
    this.validators.push(validator);
    return this;
  }

  public cellPhone(includeAreaCode?: boolean, message?: string): Validator<T> {
    const includeCode = (includeAreaCode) ? includeAreaCode : false;
    const validator = new CellPhoneValidator(this.value, includeCode, message);
    this.validators.push(validator);
    return this;
  }

  public greaterThan(compareValue: number, message?: string): Validator<T> {
    const validator = new GreaterThanValidator(this.value, compareValue, message);
    this.validators.push(validator);
    return this;
  }

  public lowerThan(compareValue: number, message?: string): Validator<T> {
    const validator = new LowerThanValidator(this.value, compareValue, message);
    this.validators.push(validator);
    return this;
  }

  public lowerEqualsThan(compareValue: number, message?: string): Validator<T> {
    const validator = new LowerEqualsThanValidator(this.value, compareValue, message);
    this.validators.push(validator);
    return this;
  }

  public greaterEqualsThan(compareValue: number, message?: string): Validator<T> {
    const validator = new GreaterEqualsThanValidator(this.value, compareValue, message);
    this.validators.push(validator);
    return this;
  }


  public gt(compareValue: number, message?: string): Validator<T> {
    return this.greaterThan(compareValue, message);
  }

  public get(compareValue: number, message?: string): Validator<T> {
    return this.greaterEqualsThan(compareValue, message);
  }

  public lt(compareValue: number, message?: string): Validator<T> {
    return this.lowerThan(compareValue, message);
  }

  public let(compareValue: number, message?: string): Validator<T> {
    return this.lowerEqualsThan(compareValue, message);
  }

  public range(startNumber: number, endNumber: number, includeLimits?: boolean, message?: string): Validator<T> {
    const limit = (includeLimits) ? includeLimits : false;
    const validator = new RangeValidator(this.value, startNumber, endNumber, limit, message);
    this.validators.push(validator);
    return this;
  }

  public rangeLength(startNumber: number, endNumber: number, includeLimits?: boolean, message?: string): Validator<T> {
    const limit = (includeLimits) ? includeLimits : false;
    const validator = new RangeLengthValidator(this.value, startNumber, endNumber, limit, message);
    this.validators.push(validator);
    return this;
  }

  public custom(callback: (value: unknown) => boolean, message?: string): Validator<T> {
    const validator = new CustomValidator(this.value, callback, message);
    this.validators.push(validator);
    return this;
  }

  public regularExpression(regExpression: RegExp, message?: string): Validator<T> {
    const validator = new RegularExpressionValidator(this.value, regExpression, message);
    this.validators.push(validator);
    return this;
  }

  public maxLength(maxLengthValue: number, message?: string): Validator<T> {
    const validator = new MaxLengthValidator(this.value, maxLengthValue, message);
    this.validators.push(validator);
    return this;
  }

  public minLength(minLengthValue: number, message?: string): Validator<T> {
    const validator = new MinLengthValidator(this.value, minLengthValue, message);
    this.validators.push(validator);
    return this;
  }


  public objectCompare(compareObject: unknown, message?: string): Validator<T> {
    const validator = new ObjectValidator(this.value, compareObject, message);
    this.validators.push(validator);
    return this;
  }

  public url(message?: string): Validator<T> {
    const validator = new UrlValidator(this.value, message);
    this.validators.push(validator);
    return this;
  }

  public in(compareArray: unknown[], message?: string): Validator<T> {
    const validator = new InValidator(this.value, compareArray, message);
    this.validators.push(validator);
    return this;
  }

  public if(evalCondition: boolean | (() => boolean)): Validator<T>{
    const validator = new IfOperator(evalCondition);
    this.validators.push(validator);
    return this;
  }

  public ifNot(evalCondition: boolean | (() => boolean)): Validator<T>{
    const validator = new IfNotOperator(evalCondition);
    this.validators.push(validator);
    return this;
  }

  public stop(): Validator<T>{
    const validator = new StopOperator();
    this.validators.push(validator);
    return this;
  }

  public addValidator(validator: ClassValidator): void {
    this.validators.push(validator);
  }

  public async validate(): Promise<boolean> {
    this.isValid = true;
    this.errors = [];

    for(const classValidator of this.validators) {
      if(classValidator instanceof  ClassOperator) {
        if(!this.isValid && classValidator instanceof StopOperator) break;
        if(!await classValidator.validate()) break;
      }
      if(!await classValidator.validate()) {
        this.isValid = false;
        this.errors.push(classValidator.message);
      }
    }
    return this.isValid;
  }
}

export default Validator;

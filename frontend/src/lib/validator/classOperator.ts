abstract class ClassOperator {
  private _evalCondition:  boolean | (() => boolean);
  public isValid: boolean;

  public constructor(evalCondition: boolean | (() => boolean)) {
    this._evalCondition = evalCondition;
    this.isValid = true;
  }

  protected evalCondition():  boolean | (() => boolean) {
    let _evalCondition:   boolean | (() => boolean) = this._evalCondition;
    if(typeof this._evalCondition === 'function') _evalCondition = this._evalCondition();
    return _evalCondition;
  }

  abstract validate(): Promise<boolean>;
}

export default ClassOperator;

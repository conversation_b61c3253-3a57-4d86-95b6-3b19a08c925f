//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class InValidator extends ClassValidator {

  private compareArray: unknown[];

  public constructor(value: unknown, compareArray: unknown[], message?: string) {
    super(value);
    this.compareArray = compareArray;
    this.message = (message) ? message : i18n.global.t('valueNotFound');
  }

  public async validate(): Promise<boolean> {
    const value = this.value();
    return this.isValid = (value) ? this.compareArray.includes(value) : true;
  }
}

export default InValidator;
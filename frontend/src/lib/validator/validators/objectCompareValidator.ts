//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class ObjectCompareValidator extends ClassValidator {

  private compareObject: unknown;

  public constructor(value: unknown, compareObject: unknown, message?: string) {
    super(value);
    this.compareObject = compareObject;
    this.message = (message) ? message : '';
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as object;
    if(!value) return true;
    const compareObject = this.compareObject;
    const keysObject = Object.keys(compareObject as object);
    keysObject.forEach((field: string) => {
      this.isValid = value[field as keyof typeof compareObject] == (compareObject as object)[field as keyof typeof compareObject];
      if(!this.isValid){
        this.message = this.message ? this.message : `${i18n.global.t('theFieldIncorrect', { fieldIncorrect: field })}`;
        return this.isValid;
      }
    });

    return this.isValid;
  }
}

export default ObjectCompareValidator;
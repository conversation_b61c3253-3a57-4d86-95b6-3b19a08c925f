//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class GreaterThanValidator extends ClassValidator {

  private compareValue: number;

  public constructor(value: unknown, compareValue: number, message?: string) {
    super(value);
    this.compareValue = compareValue;
    this.message = (message) ? message : '';
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as number;
    if(!value) return true;
    if(!!Number(value)) {
      this.message = this.message ? this.message : `${i18n.global.t('valueMustBeGt')} ${this.compareValue}.`;
      return this.isValid = value > this.compareValue;
    }
    this.message = i18n.global.t('notNumericalValue');
    return this.isValid = false;
  }
}

export default GreaterThanValidator;
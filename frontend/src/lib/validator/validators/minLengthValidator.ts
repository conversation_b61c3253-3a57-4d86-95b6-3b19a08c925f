//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class MinLengthValidator extends ClassValidator {

  private minLengthValue: number;

  public constructor(value: unknown, minLengthValue: number, message?: string) {
    super(value);
    this.minLengthValue = minLengthValue;
    this.message = (message) ? message : `${i18n.global.t('valueMustHaveMin', { number: this.minLengthValue })}`;
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as string;
    return this.isValid = (value) ? value.length >= this.minLengthValue : true;
  }
}

export default MinLengthValidator;
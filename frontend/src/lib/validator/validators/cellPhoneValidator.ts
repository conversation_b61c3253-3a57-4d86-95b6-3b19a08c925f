//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class CellPhoneValidator extends ClassValidator {

  private includeAreaCode: boolean;

  public constructor(value: unknown, includeAreaCode: boolean, message?: string) {
    super(value);
    this.includeAreaCode = includeAreaCode;
    this.message = (message) ? message : i18n.global.t('invalidCellPhoneNumber');
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as string;
    if(this.includeAreaCode) {
      const validateFields = /^\+[0-9](\-?[0-9]{1,3})? [0-9]{10}$/;
      return this.isValid = (value) ? value.match(validateFields) != null : true;
    }
    const validateFields = /^[0-9]{10}$/;
    return this.isValid = (value) ? value.match(validateFields) != null : true;
  }
}

export default CellPhoneValidator;
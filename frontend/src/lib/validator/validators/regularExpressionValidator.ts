//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class RegularExpressionValidator extends ClassValidator {

  private regExpression: RegExp;

  public constructor(value: unknown, regExpression: RegExp, message?: string) {
    super(value);
    this.regExpression = regExpression;
    this.message = (message) ? message : i18n.global.t('invalidValue');
  }

  public async validate(): Promise<boolean> {
    const value = this.value() as string;
    const validateFields = this.regExpression;
    return this.isValid = (value) ? value.match(validateFields) != null : true;
  }
}

export default RegularExpressionValidator;
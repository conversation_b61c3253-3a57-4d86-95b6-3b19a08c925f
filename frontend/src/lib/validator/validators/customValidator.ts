//#region imports
import ClassValidator from 'src/lib/validator/classValidator';
import { i18n } from 'src/boot/i18n';
//#endregion

class CustomValidator extends ClassValidator {

  private callback: (value: unknown) => boolean;

  public constructor(value: unknown, callback: (value: unknown) => boolean, message?: string) {
    super(value);
    this.callback = callback;
    this.message = (message) ? message : i18n.global.t('invalidValue');
  }

  public async validate(): Promise<boolean> {
    const value = this.value();
    return this.isValid = (value) ? this.callback(value) : true;
  }
}

export default CustomValidator;
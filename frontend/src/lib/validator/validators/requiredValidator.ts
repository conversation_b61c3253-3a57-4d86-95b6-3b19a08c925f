//#region types
import ClassValidator from 'src/lib/validator/classValidator'
import { i18n } from 'src/boot/i18n';
//#endregion

class RequiredValidator extends ClassValidator {
  public constructor(value: unknown, message?: string) {
    super(value);
    this.message = (message) ? message : i18n.global.t('isRequired');
  }

  public async validate(): Promise<boolean> {
    const value = this.value();
    return this.isValid = ((typeof value == 'string' ? value.trim() != '' : true) && value != undefined && value != null && (Array.isArray(value) ? value.length != 0 : true));
  }
}

export default RequiredValidator;

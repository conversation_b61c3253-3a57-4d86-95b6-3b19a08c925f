/* eslint-disable @typescript-eslint/no-explicit-any */
//#region imports
import { AxiosProgressEvent } from 'axios';
//#endregion

export interface Pagination<T> {
  data: T[];
  page?: number;
  rowsPerPage?: number;
  rowsNumber?: number;
  pagesNumber?: number;
}

export interface DevelopConfig {
  responseTime?: number;
}

export interface SelectOption {
  value: any;
  icon?: string;
  label?: string;
  colorIcon?: string;
  hint?: string;
  disable?: boolean;
}

export interface ItemMenu {
  title: string;
  caption?: string;
  link?: string;
  icon?: string;
  target?: '_blank' | '_self';
}

export interface Search {
  [key: string]: {
    exp: string;
    value: string | boolean | number;
  };
}

export type ExportAction = (extension: string, startIndex: number, total: number, filter?: Search) => Promise<void>;

export interface FileData {
  name: string;
  data: string;
}

export interface ServerHealth {
  status: boolean;
  message?: string;
  authenticated: boolean;
  enableBotRetiros: boolean;
}

export interface ApplicationData {
  [key: string]: unknown;
}

export interface Application {
  application: string;
  applicationName: string;
  data?: ApplicationData;
  error?: string;
  startTime: number;
  endTime: number;
  timeDiff: number;
  userNotFound: boolean;
  ignored: boolean;
  hasRemove: boolean;
  hasPrivileges: boolean;
  warning?: string;
}

export interface Roles {
  branch?: string;
  position?: string;
  name?: string;
}

export interface URLResponse {
  url: string;
}

export type TransactionProgressCallback = (progressEvent: AxiosProgressEvent) => void;

export interface RemoveUserData {
  [username: string]: {
    applications: string[];
    catalogNumber: string;
  }
}

export interface RemoveUsersData {
  users: RemoveUserData;
}

export interface RemoveUsersTableData {
  user: string;
  applications: string;
  catalogNumber?: string;
}

export interface CatalogNumberMap {
  [username: string]: string;
}

export interface UsersToBeRemovedMap {
  [username: string]: {
    [application: string]: {
      removeFrom: boolean;
      name: string;
    }
  }
}

export interface DailyResultsForm {
  exportType: 'consult' | 'remove' | 'all' | 'previous_deletions';
  hasPrivileges: boolean;
  includeNotFound: boolean;
  includeErrors: boolean;
}

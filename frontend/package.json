{"name": "frontend", "version": "0.0.1", "description": "Bot Consulta Usuarios", "productName": "Bot Consulta Usuarios", "author": "ARUS", "private": true, "scripts": {"lint": "eslint --ext .js,.ts,.vue ./", "format": "prettier --write \"**/*.{js,ts,vue,scss,html,md,json}\" --ignore-path .gitignore", "test": "echo \"No test specified\" && exit 0", "dev": "quasar dev", "build": "quasar build"}, "dependencies": {"@quasar/extras": "^1.17.0", "axios": "^1.2.1", "dexie": "^4.0.7", "dompurify": "^3.1.6", "dotenv": "^16.4.5", "jwt-decode": "^4.0.0", "pinia": "^2.0.11", "pinia-plugin-store": "^2.1.30", "quasar": "^2.18.2", "vue": "^3.5.20", "vue-i18n": "^9.2.2", "vue-router": "^4.5.1"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^3.3.1", "@quasar/app-vite": "^1.9.0", "@types/dompurify": "^3.0.5", "@types/node": "^12.20.21", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "autoprefixer": "^10.4.2", "eslint": "^8.57.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.5.1", "typescript": "^4.5.4", "vite-plugin-checker": "^0.6.4", "vue-tsc": "^1.8.22"}, "engines": {"node": "^22 || ^20 || ^18 || ^16", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}
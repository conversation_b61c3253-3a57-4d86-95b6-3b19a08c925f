FROM node:lts-alpine

RUN apk add --no-cache --no-check-certificate \
    bash \
    busybox-extras \
    curl \
    procps \
    tzdata \
    vim \
    ca-certificates \
    && update-ca-certificates \
    && cp /usr/share/zoneinfo/America/Bogota /etc/localtime \
    && echo "America/Bogota" > /etc/timezone

WORKDIR /app

COPY package*.json ./

COPY sso-sura-node.tar ./

RUN npm config set strict-ssl false && npm install

COPY . .

CMD [ "npm", "start" ]

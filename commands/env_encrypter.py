"""Command-line tool to encrypt and decrypt an .env file using the <PERSON><PERSON>t algorithm.
Run env_encrypter.py -h to show the help message.
"""

import os
import re
import sys
import base64
import argparse
from io import String<PERSON>
from datetime import datetime
from getpass import getpass
from typing import Literal
from dataclasses import dataclass
from dotenv import dotenv_values
from cryptography.fernet import Ferne<PERSON>, InvalidToken
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


def print_message(level: Literal['info', 'warning', 'error'], message: str) -> None:
    """Prints a log message.

    Parameters
    ----------
    level : Literal['info', 'warning', 'error']
        Log level.
    message : str
        Log message.
    """

    if level == 'info':
        message = '\033[0;36mINFO: \033[0m' + message
    if level == 'warning':
        message = '\033[1;33mWARNING: \033[0m' + message
    if level == 'error':
        message = '\033[0;31mERROR: \033[0m' + message
    print('\n' + message)


@dataclass
class EnvFile:
    text: str
    config: dict[str, str | None]


@dataclass
class EncryptedEnvFile:
    text: str
    config: dict[str, str | None]


class EnvTools:
    """Provides tools to operate with environment variables."""

    @classmethod
    def replace_env(cls, text: str, varname: str, value: str, use_quotation: bool = False, use_highlighting: bool = False) -> str:
        """Replaces the value of an environment variable.

        Parameters
        ----------
        text : str
            Env file text.
        varname : str
            Variable name.
        value : str
            New value.
        use_quotation : bool, optional
            Add double quotes to each non-numeric value, by default False.
        use_highlighting : bool, optional
            Use highlighting, by default False.

        Returns
        -------
        str
            Text with replaced values.
        """

        pattern = '%s={1}.*' % varname
        if use_quotation:
            value = '"{}"'.format(value) if not value.isnumeric() else value
        if use_highlighting and value:
            if value.isnumeric():
                value = f'\033[0;31m{value}\033[0m'
            else:
                value = f'\033[1;33m{value}\033[0m'
            new_value = f'\033[0;36m{varname}\033[0m={value}'
        else:
            new_value = f'{varname}={value}'
        return re.sub(pattern, new_value, text)

    @classmethod
    def print_variable(cls, varname: str, value: str | None) -> None:
        """Prints a variable.

        Parameters
        ----------
        varname : str
            Variable name.
        value : str | None
            Variable value.
        """

        if not value:
            value = f'\033[1;33m null\033[0m'
        elif value.isnumeric():
            value = f'\033[0;31m {value}\033[0m'
        else:
            value = f'\033[1;33m {value}\033[0m'
        print(f'\nVariable:\033[0;36m {varname}\033[0m\nValue:{value}')

    @classmethod
    def print_file(cls, env_config: EnvFile, fernet: Fernet | None = None) -> None:
        """Prints a Env file.

        Parameters
        ----------
        env_config : EnvFile
            Env file object (text and variables).
        fernet : Fernet | None, optional
            Fernet class object to decrypt the variables, by default None.
        """

        text = env_config.text
        for key, value in env_config.config.items():
            if not value:
                value = ''
            if fernet and not value.isnumeric():
                decrypted_value = fernet.decrypt(value).decode()
            else:
                decrypted_value = value
            text = cls.replace_env(text, key, decrypted_value, use_quotation=True, use_highlighting=True)
        print('\n' + text)


class EnvEncrypterParser(argparse.ArgumentParser):

    def error(self, message: str) -> None:
        """Handles raised errors.

        Parameters
        ----------
        message : str
            Error message.
        """

        # If no args are given, show the help message and exit
        if len(sys.argv) == 1:
            self.print_help()
            sys.exit(0)

        self.print_help()
        print_message('error', '%s\n' % message)
        sys.exit(2)


class EnvEncrypter:
    """Encrypts and decrypts an .env file using the Fernet algorithm."""

    args: argparse.Namespace
    prog: str = os.path.basename(__file__)
    description: str = 'Encrypt and decrypt an .env file using the Fernet algorithm.'
    passphrase: bytes = b''

    DEFAULT_PATH = './.env'
    DEFAULT_ENCRYPTED_OUTPUT = '.env.encrypted'
    DEFAULT_DECRYPTED_OUTPUT = '.env.decrypted'
    DEFAULT_DESTINATION_FOLDER = './'
    DEFAULT_KEY_FILE = '.env.key'

    def __init__(self) -> None:
        self.initialize_argument_parser()

    def initialize_argument_parser(self) -> None:
        """Initializes and sets up the argument parser."""

        parser = EnvEncrypterParser(prog=self.prog, description=self.description, formatter_class=argparse.RawTextHelpFormatter)

        # Options "encrypt" and "decrypt"
        actions_group = parser.add_mutually_exclusive_group(required=True)
        actions_group.add_argument('-e', '--encrypt', action='store_true', help='encrypt file')
        actions_group.add_argument('-d', '--decrypt', action='store_true', help='decrypt file')
        actions_group.add_argument('-i', '--inspect', action='store_true', help='inspect file (decrypt and show variables)')

        # Option to specify a variable name to inspect
        parser.add_argument('--no-decrypt', action='store_true', help='inspect file without decrypting it (only if --inspect is given)')

        # Option to specify a variable name to inspect
        parser.add_argument('-v', '--varname', type=str, help='variable name to inspect (only if --inspect is given)\nNOTE: variable names are case-sensitive')

        # Option to specify a path from which the .env file will be read
        parser.add_argument('-f', '--path', type=str, help='.env file path')

        # Option to specify the output file name
        parser.add_argument('-o', '--output', type=str, help='output file name')

        # Option to specify the destination folder
        parser.add_argument('-dest', '--destination', type=str, help='destination folder')

        # Option to read the encryption key from either a file or a passphrase
        method_group = parser.add_mutually_exclusive_group(required=False)
        method_group.add_argument('-k', '--key', type=str, help='.key file path which contains the encryption key (only if --decrypt is given)')
        method_group.add_argument('-p', '--passphrase', action='store_true', help='Use a passphrase as encryption key')

        # Option to prevent generating a .key file
        parser.add_argument('--no-key', action='store_true', help='Do not generate a .key file (only if --encrypt is given)')

        self.args = parser.parse_args()

    def ask_confirmation_for_existing_file(self, path: str) -> bool:
        """Ask confirmation to replace an existing file.

        Parameters
        ----------
        path : str
            Path to the file.

        Returns
        -------
        bool
            Whether replace the file.
        """

        for _ in range(3):
            response = input(f'\nFile "{path}" already exists. Do you want to replace it? (y/[n]): ')
            response = response.strip().lower()
            if not response:
                return False
            if response in ('y', 'yes', 'n', 'no'):
                return response in ('y', 'yes')
            print_message('warning', 'Please select a valid option')
        print_message('error', 'No valid option selected')
        sys.exit(2)

    def check_file_exists(self, path: str, ext: str = '.env') -> str:
        """Checks if a file already exists.

        Parameters
        ----------
        path : str
            Path to the output file.
        ext : str, optional
            Extension of the file, by default '.env'.

        Returns
        -------
        str
            Checked path to the output file.
        """

        if os.path.isdir(path):
            path = os.path.join(path, ext)

        if os.path.exists(path):
            replace_file = self.ask_confirmation_for_existing_file(path)
            if replace_file:
                return path
            path = path + '_' + str(datetime.now())
            if os.path.exists(path):
                print_message('error', f'File "{path}" already exists')
                sys.exit(0)
        return path

    def save_encryption_key(self, key: bytes) -> None:
        """Saves the encryption key to a file.

        Parameters
        ----------
        key : bytes
            Encryption key.
        """

        DESTINATION_FOLDER_PATH = self.args.destination if self.args.destination else self.DEFAULT_DESTINATION_FOLDER
        key_file_path = os.path.join(DESTINATION_FOLDER_PATH, self.DEFAULT_KEY_FILE)
        key_file_path = self.check_file_exists(key_file_path)
        try:
            with open(key_file_path, 'wb') as filekey:
                filekey.write(key)
            print_message('info', f'File "{key_file_path}" saved')
        except Exception as e:
            print_message('warning', f'Encryption key could not be written: {str(e)}')

    def get_encryption_key(self) -> bytes:
        """Gets a new key from either a passphrase or a Fernet key.

        Returns
        -------
        bytes
            Encryption key.
        """

        if self.args.passphrase:
            return self.passphrase

        key = Fernet.generate_key()
        if self.args.no_key:
            return key

        self.save_encryption_key(key)
        return key

    def read_encryption_key(self) -> bytes:
        """Reads the encryption key from either a passphrase or a file.

        Returns
        -------
        bytes
            Encryption key.
        """

        if self.args.passphrase:
            return self.passphrase

        DESTINATION_FOLDER_PATH = self.args.destination if self.args.destination else self.DEFAULT_DESTINATION_FOLDER
        key_file = self.args.key if self.args.key else os.path.join(DESTINATION_FOLDER_PATH, self.DEFAULT_KEY_FILE)

        if os.path.isdir(key_file):
            key_file = os.path.join(key_file, self.DEFAULT_KEY_FILE)

        try:
            with open(key_file, 'rb') as filekey:
                return filekey.read()
        except Exception as e:
            print_message('error', f'Encryption key could not be read: {str(e)}')
            sys.exit(1)

    def read_file(self, path: str) -> EnvFile:
        """Reads an .env file.

        Parameters
        ----------
        path : str
            .env file path.

        Returns
        -------
        EnvFile
            Object which contains the raw text and a dict with the environment variables (config).
        """

        if os.path.isdir(path):
            path = os.path.join(path, '.env')

        with open(path, 'r') as f:
            text = f.read()
            return EnvFile(text=text, config=dotenv_values(stream=StringIO(text)))

    def save_file(self, text: str, encrypted: bool = False) -> None:
        """Saves text to a file.

        Parameters
        ----------
        text : str
            Raw text of the .env file.
        encrypted : bool, optional
            If true, text will be saved to a .encrypted file, otherwise, to a .decrypted file. By default False.
        """

        try:
            DEFAULT_FILE_PATH = self.DEFAULT_ENCRYPTED_OUTPUT if encrypted else self.DEFAULT_DECRYPTED_OUTPUT
            output_file = self.args.output if self.args.output else DEFAULT_FILE_PATH
            if '/' in output_file:
                print_message('error', f'Invalid output file name "{output_file}"')
                sys.exit(1)

            DESTINATION_FOLDER_PATH = self.args.destination if self.args.destination else self.DEFAULT_DESTINATION_FOLDER
            output_file_path = os.path.join(DESTINATION_FOLDER_PATH, output_file)
            output_file_path = self.check_file_exists(output_file_path)
            with open(output_file_path, 'w') as f:
                f.write(text)
            print_message('info', f'File "{output_file_path}" saved')
        except Exception as e:
            print_message('error', f'File could not be saved: {str(e)}')
            sys.exit(1)

    def encrypt(self):
        """Encrypts the .env file."""

        file_path = self.args.path if self.args.path else self.DEFAULT_PATH
        env_config = self.read_file(file_path)
        key = self.get_encryption_key()
        fernet = Fernet(key)
        text = env_config.text
        for key, value in env_config.config.items():
            if not value or value.isnumeric():
                continue
            encrypted_value = fernet.encrypt(value.encode())
            text = EnvTools.replace_env(text, key, encrypted_value.decode())
        self.save_file(text, encrypted=True)

    def decrypt(self):
        """Decrypts the .env file."""

        file_path = self.args.path if self.args.path else self.DEFAULT_PATH
        env_config = self.read_file(file_path)
        key = self.read_encryption_key()
        fernet = Fernet(key)
        text = env_config.text
        for key, value in env_config.config.items():
            if not value or value.isnumeric():
                continue
            decrypted_value = fernet.decrypt(value).decode()
            text = EnvTools.replace_env(text, key, decrypted_value, use_quotation=True)
        self.save_file(text)

    def inspect(self) -> None:
        """Inspects the .env file or a single environment variable in the .env file."""

        file_path = self.args.path if self.args.path else self.DEFAULT_PATH
        env_config = self.read_file(file_path)

        # To inspect a single variable
        if self.args.varname:
            varname = self.args.varname
            if varname not in env_config.config:
                print_message('error', f'Variable name "{varname}" not found in the .env file')
                sys.exit(0)

            value = env_config.config.get(varname)
            if not value:
                EnvTools.print_variable(varname, value)
                sys.exit(0)

            if self.args.no_decrypt:
                EnvTools.print_variable(varname, value)
                sys.exit(0)

            key = self.read_encryption_key()
            fernet = Fernet(key)
            decrypted_value = fernet.decrypt(value).decode()
            EnvTools.print_variable(varname, decrypted_value)
            sys.exit(0)

        # If .env file is not encrypted
        if self.args.no_decrypt:
            EnvTools.print_file(env_config)
            sys.exit(0)

        # If .env file is encrypted
        key = self.read_encryption_key()
        fernet = Fernet(key)
        EnvTools.print_file(env_config, fernet)

    def set_passphrase(self):
        """Asks the user for the passphrase and stores it as 32 url-safe base64-encoded bytes."""

        if self.args.passphrase:
            passphrase = getpass('Passphrase: ')
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'',
                iterations=3900)
            self.passphrase = base64.urlsafe_b64encode(kdf.derive(passphrase.encode()))

    def check_parameters_usage(self) -> None:
        """Prints out which parameters are being ignored."""

        if self.args.key and not self.args.decrypt:
            print_message('info', '--key argument is only if --decrypt is given. Parameter will be ignored.')
        if self.args.no_key and not self.args.decrypt:
            print_message('info', '--no-key argument is only if --decrypt is given. Parameter will be ignored.')
        if self.args.varname and not self.args.inspect:
            print_message('info', '--varname argument is only if --inspect is given. Parameter will be ignored.')
        if self.args.no_decrypt and not self.args.inspect:
            print_message('info', '--no-decrypt argument is only if --inspect is given. Parameter will be ignored.')

    def run(self):
        """Runs the encrypter."""

        try:
            self.check_parameters_usage()
            self.set_passphrase()
            if self.args.encrypt:
                self.encrypt()
            elif self.args.decrypt:
                self.decrypt()
            elif self.args.inspect:
                self.inspect()
            sys.exit(0)
        except InvalidToken:
            key_method = 'Passphrase' if self.args.passphrase else 'Encryption key'
            print_message('error', key_method + ' is not valid')
            sys.exit(1)


if __name__ == '__main__':
    try:
        env = EnvEncrypter()
        env.run()
    except Exception as e:
        print_message('error', str(e))

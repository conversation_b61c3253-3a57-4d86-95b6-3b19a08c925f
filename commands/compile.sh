#!/bin/bash

# Colors
RED="\e[1;31m"
GREEN="\e[1;32m"
YELLOW="\e[1;33m"
BLUE="\e[1;36m"
ENDCOLOR="\e[0m"

# File arguments
file="$1"
filename="${file%.*}"
extension="${file##*.}"

# Show file info
echo -e "\n${BLUE}[INFO] Nombre del archivo: $filename${ENDCOLOR}"
echo -e "${BLUE}[INFO] Extensión del archivo: $extension${ENDCOLOR}\n"

# Check if file exists
if ! test -f ./$file; then
    echo -e "${RED}[ERROR] El archivo '$file' no existe${ENDCOLOR}\n"
    exit 1
fi

# Check if file is a python script
if ! [[ "$extension" == "py" ]]; then
    echo -e "${RED}[ERROR] El archivo '$file' debe ser un script de python${ENDCOLOR}\n"
    exit 1
fi

# Compile file
echo -e "${BLUE}[INFO] Compilando '$file'${ENDCOLOR}\n"
python3 -m PyInstaller --clean --onefile "$file" --distpath ./bin

# Check compilation result
if ! [ $? == 0 ]; then
    echo -e "\n${RED}[ERROR] No se pudo compilar el archivo '$file'${ENDCOLOR}\n"
    exit 1
else
    echo -e "\n${GREEN}[INFO] Compilación finalizada${ENDCOLOR}\n"
fi

# Clean build folder
if test -d ./build; then
    echo -e "${BLUE}[INFO] Limpiando carpeta 'build'${ENDCOLOR}\n"
    rm -rf ./build
    if ! [ $? == 0 ]; then
        echo -e "${YELLOW}[WARNING] No se pudo limpiar la carpeta 'build'${ENDCOLOR}\n"
    else
        echo -e "${GREEN}[INFO] Carpeta 'build' limpiada correctamente${ENDCOLOR}\n"
    fi
fi

# Clean .spec file
if test -f ./$filename.spec; then
    echo -e "${BLUE}[INFO] Limpiando archivo '${filename}.spec'${ENDCOLOR}\n"
    rm -f ./$filename.spec
    if ! [ $? == 0 ]; then
        echo -e "${YELLOW}[WARNING] No se pudo limpiar el archivo '${filename}.spec'${ENDCOLOR}\n"
    else
        echo -e "${GREEN}[INFO] Archivo '${filename}.spec' limpiado correctamente${ENDCOLOR}\n"
    fi
fi

# Exit
echo -e "${GREEN}********** Binario guardado en ./bin/${filename} **********${ENDCOLOR}\n"
exit 0

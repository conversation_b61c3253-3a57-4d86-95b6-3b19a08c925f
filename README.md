<!-- omit in toc -->
# Bot Consulta de Usuarios
[![Generic badge](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://shields.io/) [![Generic badge](https://img.shields.io/badge/Starlette-0.47+-green.svg)](https://shields.io/) [![Generic badge](https://img.shields.io/badge/Vue.js-3.5+-blue.svg)](https://shields.io/) [![Generic badge](https://img.shields.io/badge/Quasar-2.18+-green.svg)](https://shields.io/) [![Generic badge](https://img.shields.io/badge/MongoDB-8.0+-green.svg)](https://shields.io/)

<!-- omit in toc -->
## Descripción

El **Bot Consulta de Usuarios** es una plataforma de automatización RPA (Robotic Process Automation) que permite realizar dos funciones principales:

### 1. Consulta de Usuarios
Consulta información de un usuario por su nombre de usuario (también conocido como **login** o **422** en SURA) en **30 aplicaciones diferentes**, incluyendo:
- **Directorio Activo** - Información básica del usuario
- **Salud Web** - Datos de salud y perfiles
- **Agenda Web** - Información de agenda y citas
- **SEUS 4** - Perfiles y roles de seguridad
- **Salesforce** - Datos CRM y comerciales
- **Ariba** - Información de compras y proveedores
- **IPSA** - Datos de intercambios
- **Policy Center** - Información de pólizas
- **Y 22 aplicaciones adicionales** (ver lista completa en [DEVELOPMENT.md](docs/DEVELOPMENT.md))

### 2. Retiro de Usuarios (Bot Retiros)
Automatiza el proceso de retiro/desactivación de usuarios en múltiples aplicaciones cuando un empleado deja la organización, garantizando el cumplimiento de políticas de seguridad.

- [Características Técnicas](#características-técnicas)
- [Requisitos del Sistema](#requisitos-del-sistema)
    - [Software Requerido](#software-requerido)
    - [Dependencias del Backend (Python)](#dependencias-del-backend-python)
    - [Dependencias del Frontend (Node.js)](#dependencias-del-frontend-nodejs)
- [Desarrollo](#desarrollo)
    - [Configuración del Entorno de Desarrollo](#configuración-del-entorno-de-desarrollo)
    - [Servicios Disponibles en Desarrollo](#servicios-disponibles-en-desarrollo)
    - [Estructura del Proyecto](#estructura-del-proyecto)
- [Configuración](#configuración)
    - [Categorías de Configuración](#categorías-de-configuración)
        - [1. Configuración de Contenedores](#1-configuración-de-contenedores)
        - [2. Base de Datos (MongoDB)](#2-base-de-datos-mongodb)
        - [3. Autenticación y Seguridad](#3-autenticación-y-seguridad)
        - [4. Configuración de Aplicaciones](#4-configuración-de-aplicaciones)
    - [Archivo de Configuración Completo](#archivo-de-configuración-completo)
    - [Seguridad de la Configuración](#seguridad-de-la-configuración)
- [Despliegue en Producción](#despliegue-en-producción)
    - [Prerrequisitos](#prerrequisitos)
    - [Proceso de Despliegue](#proceso-de-despliegue)
        - [1. Preparación del Servidor](#1-preparación-del-servidor)
        - [2. Configuración del Entorno](#2-configuración-del-entorno)
        - [3. Configuración de Certificados SSL](#3-configuración-de-certificados-ssl)
        - [4. Validación de Certificados SSL](#4-validación-de-certificados-ssl)
        - [5. Despliegue de los Servicios](#5-despliegue-de-los-servicios)
    - [Arquitectura de Producción](#arquitectura-de-producción)
    - [Verificación del Despliegue](#verificación-del-despliegue)
    - [Solución de Problemas Comunes](#solución-de-problemas-comunes)
        - [Error de Autenticación MongoDB](#error-de-autenticación-mongodb)
        - [Error de Espacio en Disco](#error-de-espacio-en-disco)
        - [Problemas de Permisos](#problemas-de-permisos)
- [Procedimientos de Mantenimiento](#procedimientos-de-mantenimiento)
    - [Gestión de Certificados SSL](#gestión-de-certificados-ssl)
        - [Generar Certificado Auto-firmado](#generar-certificado-auto-firmado)
        - [Convertir Certificado .pfx a .pem](#convertir-certificado-pfx-a-pem)
        - [Validar Certificados](#validar-certificados)
    - [Solución de Problemas de Base de Datos](#solución-de-problemas-de-base-de-datos)
        - [Error de Autenticación MongoDB](#error-de-autenticación-mongodb-1)
        - [Error de Conexión a la Base de Datos](#error-de-conexión-a-la-base-de-datos)
        - [Verificación de Estado de Servicios](#verificación-de-estado-de-servicios)
    - [Monitoreo y Mantenimiento](#monitoreo-y-mantenimiento)
        - [Logs del Sistema](#logs-del-sistema)
        - [Limpieza y Mantenimiento](#limpieza-y-mantenimiento)
- [Documentación Técnica](#documentación-técnica)
- [Licencia](#licencia)

## Características Técnicas

- **Backend**: API REST desarrollada en Python con Starlette (ASGI)
- **Frontend**: Aplicación web desarrollada en Vue.js 3 con Quasar Framework
- **Base de Datos**: MongoDB para almacenamiento de resultados y transacciones
- **Autenticación**: Sistema de autenticación integrado con SEUS 4
- **Arquitectura**: Microservicios containerizados con Docker
- **Monitoreo**: Sistema de transacciones para seguimiento en tiempo real
- **Exportación**: Generación de reportes en PDF

La consulta puede tomar varios minutos dependiendo de la velocidad de cada aplicación, por lo que se implementó un sistema de **transacciones** que permite monitorear el progreso en tiempo real, mostrando qué aplicación se está consultando, el porcentaje de avance y el tiempo transcurrido.

Los resultados se almacenan en las colecciones **ConsultedUser** y **RemovedUser** de MongoDB, permitiendo su visualización en el frontend y exportación a PDF para reportes.

## Requisitos del Sistema

### Software Requerido
- **Python**: >= 3.10.7
- **MongoDB**: >= 8.0
- **Node.js**: >= 18.x
- **Docker**: >= 20.x (recomendado para despliegue)
- **Docker Compose**: >= 2.x

### Dependencias del Backend (Python)
- **Starlette**: 0.47.2 - Framework ASGI
- **Beanie**: 2.0.0 - ODM para MongoDB
- **Uvicorn**: 0.35.0 - Servidor ASGI
- **Requests**: 2.32.4 - Cliente HTTP
- **BeautifulSoup4**: 4.13.4 - Parser HTML/XML
- **ReportLab**: 4.4.3 - Generación de PDFs
- **LDAP3**: 2.9.1 - Cliente LDAP para Active Directory

### Dependencias del Frontend (Node.js)
- **Vue.js**: 3.5.20 - Framework JavaScript
- **Quasar**: 2.18.2 - Framework UI
- **Axios**: 1.2.1 - Cliente HTTP
- **Pinia**: 2.0.11 - Gestión de estado
- **Vue Router**: 4.5.1 - Enrutamiento

## Desarrollo

### Configuración del Entorno de Desarrollo

1. **Clonar el repositorio**:
```bash
git clone https://github.com/tu-organizacion/bot_consultausuarios.git
cd bot_consultausuarios
```

2. **Configurar variables de entorno**:
```bash
# Copiar el archivo de ejemplo
cp .env.example .env

# Editar las variables necesarias
# Asegúrese de que PROD=false para desarrollo
```

3. **Levantar los servicios con Docker**:
```bash
# Modo desarrollo (con hot-reload)
docker-compose -f docker-compose.dev.yml up -d

# Verificar que todos los servicios estén funcionando
docker-compose -f docker-compose.dev.yml ps
```

### Servicios Disponibles en Desarrollo

| Servicio | Puerto | URL | Descripción |
|----------|--------|-----|-------------|
| **Frontend** | 8080 | http://localhost:8080 | Interfaz web Vue.js/Quasar |
| **Bot API** | 8010 | http://localhost:8010 | API REST del bot (a través del proxy) |
| **Auth Service** | 3000 | http://localhost:3000 | Servicio de autenticación |
| **MongoDB** | 27017 | mongodb://localhost:27017 | Base de datos |

### Estructura del Proyecto

```
bot_consultausuarios/
├── bot/                    # Backend Python (API REST)
├── frontend/              # Frontend Vue.js/Quasar
├── auth/                  # Servicio de autenticación Node.js
├── bot_proxy/            # Proxy Nginx para balanceo de carga
├── cert/                 # Certificados SSL
├── docs/                 # Documentación
├── docker-compose.yml    # Configuración producción
├── docker-compose.dev.yml # Configuración desarrollo
└── .env.example         # Plantilla de configuración
```

Para información detallada sobre la arquitectura, estructura del código y guías de desarrollo, consulte la [documentación técnica completa](docs/DEVELOPMENT.md).

## Configuración

La configuración del sistema se realiza mediante un archivo **.env** ubicado en la raíz del proyecto. Este archivo contiene todas las variables de entorno necesarias para el funcionamiento del bot.

### Categorías de Configuración

#### 1. Configuración de Contenedores
```ini
PROD=true                                    # Modo producción (true/false)
SERVER_NAME="botconsultausuarios.sura.com"  # Dominio o IP del servidor
BOT_PORT=8010                               # Puerto del bot
FRONTEND_PORT=8090                          # Puerto del frontend
LAB_ENV=false                               # Entorno de laboratorio
```

#### 2. Base de Datos (MongoDB)
```ini
MONGO_INITDB_DATABASE="sura"                # Nombre de la base de datos
DATABASE_USER="bot_user"                   # Usuario de conexión
DATABASE_PASSWORD="secure_password"        # Contraseña de conexión
DATABASE_VOLUME_PATH="./db"                # Ruta del volumen de datos
```

#### 3. Autenticación y Seguridad
```ini
SERVICE="BotConsultaUsuarios"               # Nombre del servicio en SEUS 4
SERVICE_URL="https://seus.sura.com/..."    # URL de configuración SEUS 4
SSO_SECRET="..."                           # Secreto para descifrar variables SEUS 4
USE_AUTHORIZATION=false                     # Habilitar autorización
```

#### 4. Configuración de Aplicaciones
```ini
# Credenciales para consulta de usuarios
APPS_CONNECTION_USER="consulta_user"
APPS_CONNECTION_PASSWORD="consulta_pass"

# Credenciales para retiro de usuarios (Bot Retiros)
RETIROS_APPS_CONNECTION_USER="retiros_user"
RETIROS_APPS_CONNECTION_PASSWORD="retiros_pass"

# Configuraciones específicas por aplicación
LDAP_SERVER="ldaps://ad.sura.com:636"      # Active Directory
SALESFORCE_DOMAIN="login"                   # Salesforce
SEUS_USER="seus_user"                      # SEUS 4
# ... y más de 25 aplicaciones adicionales
```

### Archivo de Configuración Completo

Consulte el archivo [**.env.example**](.env.example) para ver la estructura completa con todas las variables disponibles y sus descripciones detalladas.

### Seguridad de la Configuración

> **⚠️ IMPORTANTE - Seguridad**
>
> Por razones de seguridad, el archivo **.env** debe mantenerse encriptado en producción:
>
> 1. **Encriptación**: Use el script `env_encrypter.py` para cifrar el archivo
> 2. **Despliegue**: Solo desencripte durante el despliegue
> 3. **Re-encriptación**: Vuelva a encriptar inmediatamente después del despliegue
>
> ```bash
> # Encriptar archivo de configuración
> python3 commands/env_encrypter.py -e .env
>
> # Desencriptar para despliegue
> python3 commands/env_encrypter.py -d .env.encrypted
> ```

## Despliegue en Producción

### Prerrequisitos
- Servidor con Docker y Docker Compose instalados
- Certificados SSL válidos
- Acceso a las aplicaciones que se van a consultar
- Credenciales de base de datos y aplicaciones

### Proceso de Despliegue

#### 1. Preparación del Servidor

```bash
# Clonar el repositorio
git clone --branch main <URL_DEL_REPOSITORIO>
cd bot_consultausuarios

# En caso de problemas SSL con git
git config --global http.sslVerify false
```

#### 2. Configuración del Entorno

```bash
# Crear archivo de configuración basado en el ejemplo
cp .env.example .env

# Editar las variables de producción
nano .env
```

**Variables críticas para producción**:
```ini
# Modo producción
PROD=true

# Configuración del servidor
SERVER_NAME="tu-dominio.com"
BOT_PORT=8011
FRONTEND_PORT=8090

# Base de datos
MONGO_INITDB_DATABASE="sura"
DATABASE_USER="usuario_produccion"
DATABASE_PASSWORD="contraseña_segura"

# Credenciales de aplicaciones
APPS_CONNECTION_USER="usuario_consulta"
APPS_CONNECTION_PASSWORD="contraseña_consulta"
RETIROS_APPS_CONNECTION_USER="usuario_retiros"
RETIROS_APPS_CONNECTION_PASSWORD="contraseña_retiros"
```

#### 3. Configuración de Certificados SSL

Coloque los certificados SSL en la carpeta `cert/`:
```bash
mkdir -p cert/
# Copiar cert.pem y key.pem a la carpeta cert/
```

#### 4. Validación de Certificados SSL

```bash
# Verificar validez del certificado
openssl x509 -noout -text -in cert/cert.pem

# Verificar que la clave privada coincida con el certificado
openssl rsa -noout -modulus -in cert/key.pem | openssl md5
openssl x509 -noout -modulus -in cert/cert.pem | openssl md5
```

> **⚠️ Importante**: Los certificados deben estar en formato **.pem**. Si tiene un archivo **.pfx**, consulte la sección [Generar certificado y clave a partir de un archivo .pfx](#generar-certificado-y-clave-a-partir-de-un-archivo-pfx).

#### 5. Despliegue de los Servicios

```bash
# Construir y levantar todos los servicios
docker compose up -d

# Verificar el estado de los servicios
docker compose ps

# Ver logs en tiempo real
docker compose logs -f
```

### Arquitectura de Producción

El sistema despliega **5 servicios principales**:

| Servicio | Puerto | Descripción |
|----------|--------|-------------|
| **frontend** | 8090 | Interfaz web (Vue.js/Quasar) |
| **bot_proxy** | 8011 | Proxy Nginx para balanceo de carga |
| **bot_consult** | - | Instancia del bot para consultas |
| **bot_remove** | - | Instancia del bot para retiros |
| **auth** | - | Servicio de autenticación SEUS 4 |
| **db** | - | Base de datos MongoDB |

### Verificación del Despliegue

```bash
# Verificar que todos los servicios estén saludables
docker compose ps

# Probar conectividad
curl -k https://localhost:8090/health  # Frontend
curl -k https://localhost:8011/health  # Bot API

# Verificar logs por errores
docker compose logs bot_consult | grep -i error
docker compose logs db | grep -i error
```

### Solución de Problemas Comunes

#### Error de Autenticación MongoDB
Si aparece "Authentication failed":
```bash
# Conectar al contenedor de MongoDB
docker exec -it <nombre_contenedor_db> mongosh

# Crear usuario de base de datos
use sura
db.createUser({
  user: "usuario_bot",
  pwd: "contraseña_segura",
  roles: [{role: 'readWrite', db: 'sura'}]
})
```

#### Error de Espacio en Disco
```bash
# Verificar espacio disponible
df -h

# Limpiar imágenes Docker no utilizadas
docker system prune -a

# Usar directorio temporal alternativo
sudo mkdir -p /opt/tmp
sudo chown -R 1000:1000 /opt/tmp
export TMPDIR=/opt/tmp/
```

#### Problemas de Permisos
```bash
# Corregir permisos del frontend
chmod -R 775 frontend/public

# Corregir permisos de logs
chmod -R 755 logs/
```

## Procedimientos de Mantenimiento

### Gestión de Certificados SSL

#### Generar Certificado Auto-firmado

Para entornos de desarrollo o testing, puede generar un certificado auto-firmado:

```bash
# 1. Crear clave privada
openssl genrsa -out cert/key.pem 2048

# 2. Crear solicitud de certificado
openssl req -new -key cert/key.pem -out cert/cert.csr \
  -subj "/C=CO/ST=Antioquia/L=Medellin/O=SURA/CN=localhost"

# 3. Generar certificado auto-firmado (válido por 10 años)
openssl x509 -req -days 3650 -in cert/cert.csr \
  -signkey cert/key.pem -out cert/cert.pem

# 4. Limpiar archivos temporales
rm cert/cert.csr
```

#### Convertir Certificado .pfx a .pem

Si tiene un certificado en formato **.pfx**, conviértalo a **.pem**:

```bash
# 1. Extraer clave privada (se solicitará contraseña del .pfx)
openssl pkcs12 -in certificado.pfx -nocerts -out temp_key.key

# 2. Extraer certificado público
openssl pkcs12 -in certificado.pfx -clcerts -nokeys -out temp_cert.cer

# 3. Convertir clave privada a formato PEM sin contraseña
openssl rsa -in temp_key.key -out cert/key.pem

# 4. Convertir certificado a formato PEM
openssl x509 -in temp_cert.cer -out cert/cert.pem

# 5. Limpiar archivos temporales
rm temp_key.key temp_cert.cer
```

#### Validar Certificados

```bash
# Verificar información del certificado
openssl x509 -in cert/cert.pem -text -noout

# Verificar fechas de validez
openssl x509 -in cert/cert.pem -dates -noout

# Verificar que la clave privada coincida con el certificado
openssl rsa -in cert/key.pem -modulus -noout | openssl md5
openssl x509 -in cert/cert.pem -modulus -noout | openssl md5
# Los hash MD5 deben ser idénticos
```

### Solución de Problemas de Base de Datos

#### Error de Autenticación MongoDB

**Síntoma**: `Authentication failed` en los logs del bot.

**Causas y Soluciones**:

1. **Usuario de base de datos no existe**:
```bash
# Conectar al contenedor MongoDB
docker exec -it <nombre_contenedor_db> mongosh

# Crear usuario para el bot
use sura
db.createUser({
  user: "bot_user",
  pwd: "contraseña_segura",
  roles: [
    {role: 'readWrite', db: 'sura'},
    {role: 'dbAdmin', db: 'sura'}
  ]
})
```

2. **Usuario administrador no configurado**:
```bash
# Conectar como administrador
docker exec -it <nombre_contenedor_db> mongosh -u root -p root --authenticationDatabase admin

# Crear usuario de aplicación
use sura
db.createUser({
  user: "bot_user",
  pwd: "contraseña_segura",
  roles: ["readWrite", "dbAdmin"]
})
```

#### Error de Conexión a la Base de Datos

**Síntoma**: Mensajes de "Try again" o timeouts de conexión.

**Solución**:
```bash
# Reiniciar el servicio del bot
docker compose restart bot_consult bot_remove

# Verificar conectividad
docker exec -it <nombre_contenedor_bot> ping db

# Verificar logs
docker compose logs db | tail -50
docker compose logs bot_consult | tail -50
```

#### Verificación de Estado de Servicios

```bash
# Estado general
docker compose ps

# Logs en tiempo real
docker compose logs -f

# Verificar salud de servicios
curl -k https://localhost:8090/health  # Frontend
curl -k https://localhost:8011/health  # Bot API
```

### Monitoreo y Mantenimiento

#### Logs del Sistema
```bash
# Ver logs por servicio
docker compose logs frontend
docker compose logs bot_consult
docker compose logs bot_remove
docker compose logs db

# Logs con filtros
docker compose logs bot_consult | grep -i error
docker compose logs db | grep -i "authentication\|connection"
```

#### Limpieza y Mantenimiento
```bash
# Limpiar logs antiguos
docker system prune -f

# Limpiar volúmenes no utilizados
docker volume prune -f

# Backup de base de datos
docker exec <nombre_contenedor_db> mongodump --db sura --out /backup
```

## Documentación Técnica

Para información detallada sobre la arquitectura, desarrollo y modificación del código, consulte:
- [Documentación de Desarrollo](docs/DEVELOPMENT.md)

## Licencia

ARUS S.A.

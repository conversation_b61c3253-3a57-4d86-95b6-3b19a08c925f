<!-- omit in toc -->
# Bot Consulta de Usuarios
[![Generic badge](https://img.shields.io/badge/on-Python-<Blue>.svg)](https://shields.io/) [![Generic badge](https://img.shields.io/badge/on-Starlette-<Green>.svg)](https://shields.io/)[![Generic badge](https://img.shields.io/badge/on-Vuejs-<Blue>.svg)](https://shields.io/) [![Generic badge](https://img.shields.io/badge/on-Quasar-<Green>.svg)](https://shields.io/)

La funcionalidad de este Bot consiste en la consulta de un usuario por su nombre de usuario (también conocido como **login** o **422** en SURA) en múltiples aplicaciones como Directorio Activo, Salud Web, Agenda Web, IPSA, SEUS 4, entre otras.

La consulta puede tomar varios segundos o minutos dependiendo de la velocidad del servidor de cada aplicación, por lo tanto, se creó una colección en la base de datos llamada **Transaction** para almacenar el progreso y el estado de cada consulta como si fuera una transacción permitiendo consultar la aplicación en la cual se está consultado el usuario en el momento, el porcentaje de progreso y el tiempo que ha tomado de la consulta.

Al finalizar, el Bot almacena los resultados en un nuevo documento de la colección **ConsultedUser** que, como se puede asumir a partir de su nombre, almacena los datos de los usuarios consultados para posteriormente mostrarlos en el frontend y/o exportarlos (actualmente solo es posible exportarlos a PDF).

El Bot cuenta con autenticación contra SEUS 4, por lo que es necesario que haya un **Service Provider** creado en la consola de SEUS. La URL del endpoint de SEUS 4 y el nombre del Service Provider se asignan a las variables **AUTH_SEUS4_URL** y **AUTH_SEUS4_SERVICE_PROVIDER** del archivo de configuración **.env** el cual se explica en la sección de [configuración](#configuración).

Este artículo describe los requerimientos, proceso de despliegue en desarrollo y producción,
configuración para el despliegue, documentación del código y licencia del proyecto.

<!-- omit in toc -->
## Tabla de contenidos
- [Requisitos](#requisitos)
- [Desarrollo](#desarrollo)
- [Configuración](#configuración)
- [Producción](#producción)
- [Procedimientos](#procedimientos)
    - [Generar certificado auto-firmado](#generar-certificado-auto-firmado)
    - [Generar certificado y clave a partir de un archivo .pfx](#generar-certificado-y-clave-a-partir-de-un-archivo-pfx)
    - [Error de autenticación en la base de datos](#error-de-autenticación-en-la-base-de-datos)
    - [Error de conexión a la base de datos](#error-de-conexión-a-la-base-de-datos)
- [Licencia](#licencia)

## Requisitos
```bash
- Python ( >=3.10.7 )
- Mongo ( >= 5.0 )
- Node.js ( >=18.*)
- npm versión 3+ ( o yarn versión 1.16+)
```

## Desarrollo
Para ejecutar el proyecto en modo de desarrollo, se deben seguir los siguientes pasos:

1. Si está usando un archivo .env, asegúrese de que la variable **PROD** sea **false**:
```ini
#  Ubicación: ./.env

PROD=false
```

2. Levantar los servicios de docker para desarrollo:
```bash
docker-compose -f docker-compose.dev.yml up -d
```

Consulte la [documentación de desarrollo](/docs/DEVELOPMENT.md) para conocer la estructura del proyecto y de qué manera puede contribuir y hacer modificaciones.

## Configuración
La configuración del bot consiste en un archivo de configuración llamado **.env** ubicado en la raíz del proyecto.

```ini
# Containers settings
PROD=true
SERVER_NAME="<ip or domain>"

# Database settings
MONGO_INITDB_DATABASE="sura"
DATABASE_USER="<user>"
DATABASE_PASSWORD="<password>"

# Authentication settings
AUTH_SECRET_TOKEN="<secret>"
AUTH_ALGORITHM="HS256"
AUTH_TOKEN_MINUTES=30
...
```

Consulte el archivo de ejemplo [**.env.example**](/.env.example) para conocer la estructura y significado de cada una de las variables de entorno.

> ## <span style="color:red">¡Importante!</span>
> Por seguridad, el archivo de configuración **.env** debe permanecer encriptado. Sólo debe ser descifrado en el momento del despliegue de cambios a producción y debe ser encriptado nuevamente al finalizar el proceso. [Haga click aquí para conocer el proceso de despliegue a producción.](#producción)
>
> El cifrado y descifrado del archivo se realiza con el script [env_encrypter.py](/commands/env_encrypter.py). Puede ejecutar el script **env_encrypter.py** con el parámetro **-h** para ver el modo de uso:
> ```bash
> # Ubicado en ./commands
> python3 env_encrypter.py -h
> ```
> En caso de que el servidor en el que se desplegará el bot no tenga Python 3.10.x instalado, puede ejecutar el binario [env_encrypter](/commands/bin/env_encrypter) así:
> ```bash
> # Ubicado en ./commands/bin/
> env_encrypter -h
> ```

## Producción
Para ejecutar el proyecto en modo de producción, se deben seguir los siguientes pasos:

1. Clone el repositorio del proyecto en la rama **main**:

```bash
git clone --branch main https://<EMAIL>/DirEvolucionOperacion/Agile/_git/bot_consultausuarios
```

> ## <span style="color:red">¡Importante!</span>
> Si se presenta un error de SSL al clonar el proyecto, ejecute el siguiente comando para desactivar la verificación SSL:
> ```bash
> git config --global http.sslVerify false
> ```

2. Cree un archivo **.env** basado en el archivo de ejemplo [**.env.example**](/.env.example) en la **raíz del proyecto** y modifique los valores de cada variable según la necesidad (los valores actuales del archivo de ejemplo son los valores por defecto):
```bash
# Raíz del proyecto
.
├── .env # <---- Archivo de configuración
├── .env.example
├── .git
├── .gitignore
├── README.md
├── bot
├── commands
├── docker-compose.dev.yml
├── docker-compose.yml
├── frontend
└── mongo-init.js
```

3. Asegúrese de que la variable **PROD** sea **true**:
```ini
#  Ubicación: ./.env

PROD=1
```

4. Si cuenta con un certificado SSL de extensión **.pem**, puede verificar su validez con el siguiente comando (en caso de no tener un certificado, puede [generar un certificado auto-firmado](#generar-certificado-auto-firmado)):
```bash
openssl x509 -noout -text -in <certificado>.pem
```

> ## <span style="color:red">¡Importante!</span>
> Tanto el certificado como la clave deben tener extensión **.pem**. Si tiene un archivo **.pfx** [siga los siguientes pasos para generar el certificado y la clave](#generar-certificado-y-clave-a-partir-de-un-archivo-pfx).
> <br><br>

5. Renombre los archivos del certificado y la llave a **cert.pem** y la llave **key.pem** respectivamente y ubique ambos archivos en una carpeta llamada **cert** en la raíz del proyecto.

![Carpeta cert](/docs/screenshots/readme_cert_folder_location.png)

6. Levante los servicios de docker para producción:

```bash
docker compose up -d
```

> ## <span style="color:red">¡Importante!</span>
> Si se presenta un error de SSL que impida descargar y/o construir las imágenes de Docker, ejecute los siguiente comandos para descargar las imágenes de Docker sin verificar ningún certificado SSL:
> ```bash
> docker pull --tls-verify=false python:3.10.14-alpine3.19
> docker pull --tls-verify=false mongo
> docker pull --tls-verify=false node:18
> docker pull --tls-verify=false nginx
> ```
> Del mismo modo, si se presenta un error de "no space left on device", verifique el espacio en disco. Por defecto, docker usa el disco /var/tmp. Para usar otra ruta temporal, por ejemplo, /opt/tmp, ejecute el siguiente comando:
> ```bash
> sudo mkdir -p /opt/tmp
> sudo chown -R 1000:1000 /opt/tmp
> export TMPDIR=/opt/tmp/
> ```

7. Si el contenedor del **bot** arroja el error "**Authentication failed**", consulte el [procedimiento de solución del error de autenticación de mongo](#error-de-autenticación-en-la-base-de-datos) para corregirlo.

8. Si alguno de los contenedores arroja el error "**Name does not resolve**", asegúrese de que las variables del archivo **.env** sean correctas, especialmente la variable **PROD** cuyo valor debe ser **true**.

9. Si las imágenes que se muestran en el **frontend** no cargan, puede ser debido a los permisos de la carpeta "**public**". Ejecute el siguiente comando para garantizar los permisos necesarios a la carpeta "**public**":
```bash
chmod -R 775 frontend/public
```

## Procedimientos

### Generar certificado auto-firmado
Para generar un certificado auto-firmado se deben seguir los siguientes pasos:

1. Crear la clave privada:
```sh
openssl genrsa -out key.pem 2048
```

2. Crear el request del certificado a firmar:
```sh
openssl req -new -key key.pem -out cert.csr
```

3. Firmar el request generado en el paso anterior con la clave privada generada en el primer paso:
```sh
openssl x509 -req -days 3650 -in cert.csr -signkey key.pem -out cert.pem
```

### Generar certificado y clave a partir de un archivo .pfx
Para generar un certificado y clave **.pem** a partir de un archivo **.pfx** se deben seguir los siguientes pasos:

1. Extraer la clave privada **.key**:
```sh
openssl pkcs12 -in <archivo>.pfx -nocerts -out privatekey.key
```

2. Extraer el certificado **.cer**:
```sh
openssl pkcs12 -in <archivo>.pfx -clcerts -nokeys -out certificate.cer
```

3. Convertir la clave privada a **.pem**:
```sh
openssl rsa -in privatekey.key -out key.pem
```

4. Convertir el certificado a **.pem**:
```sh
openssl x509 -in certificate.cer -out cert.pem
```

### Error de autenticación en la base de datos
Existen dos posibles causas del error de autenticación de Mongo, a continuación se presenta cada una de ellas y su solución:

1. El usuario de conexión a la base de datos o la base de datos no existen.

    #### Solución
    ```bash
    docker exec -it <nombre del contenedor> bash
    mongosh -u <usuario admin> -p <contraseña>
    use <nombre de la base de datos>
    db.createUser({user: "<usuario>", pwd: "<contraseña>", roles: [{role: 'readWrite', db: 'sura'}]})
    ```

2. El usuario de administración (usuario con rol "root") no existe.

    #### Solución
    ```bash
    docker exec -it <nombre del contenedor> bash
    mongosh -u root -p root --authenticationDatabase admin
    use <nombre de la base de datos>
    db.createUser({user: "<usuario>", pwd: "<contraseña>", roles: ["readWrite", "dbAdmin"]})
    db.removeUser("root")
    ```

NOTA: Si el contenedor no tiene el shell "bash" se pueden listar los shells con el siguiente comando:
```bash
docker exec -it <nombre del contenedor> cat /etc/shells
```

### Error de conexión a la base de datos
Es posible que ocurra un error de conexión a la base de datos desde el Bot el cual sugiere que se intente de nuevo la conexión mostrando un mensaje como el siguiente:

![Error de conexión a la base de datos](/docs/screenshots/readme_mongo_try_again_error.png)

Si ve el mensaje "Try again" como se observa en la imagen anterior, ejecute el comando restart de Docker para reiniciar el Bot:
```bash
docker restart <nombre del contenedor del bot>
```

Compruebe que el Bot haya iniciado de manera exitosa (debería mostrar el mensaje "*Uvicorn running on https...*").

## Licencia
ARUS S.A

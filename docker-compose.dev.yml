services:
  auth:
    build:
      context: ./auth
      dockerfile: Dockerfile.dev
    ports:
      - 3000:3000
    volumes:
      - ./auth/src:/app/src
    env_file:
      - .env
  db:
    image: mongo:8.0.13
    volumes:
      - ./db:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js
    ports:
      - 27017:27017
    env_file:
      - .env
  bot_consult:
    build:
      context: ./bot
      dockerfile: Dockerfile.dev
    expose:
      - 8010
    volumes:
      - ./bot:/app
    extra_hosts:
      - "ipsa-intercambios.suramericana.com:************"
      - "ca-sd.suramericana.com ca-sd-ppal.suramericana.com www.serviciosensaludsura.com:************"
    env_file:
      - .env
    depends_on:
      - db
  bot_remove:
    build:
      context: ./bot
      dockerfile: Dockerfile.dev
    expose:
      - 8010
    volumes:
      - ./bot:/app
    extra_hosts:
      - "ipsa-intercambios.suramericana.com:************"
      - "ca-sd.suramericana.com ca-sd-ppal.suramericana.com www.serviciosensaludsura.com:************"
    env_file:
      - .env
    depends_on:
      - db
  bot_proxy:
    build:
      context: ./bot_proxy
      dockerfile: Dockerfile.dev
    ports:
      - 8010:80
    env_file:
      - .env
    depends_on:
      - bot_consult
      - bot_remove
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - 8080:8080
    volumes:
      - ./frontend/src:/app/src
    env_file:
      - .env
    depends_on:
      - bot_proxy
